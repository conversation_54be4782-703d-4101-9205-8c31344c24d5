import { p as push, Z as spread_attributes, _ as clsx, Q as bind_props, q as pop, o as setContext, a4 as hasContext, a5 as getContext } from './index3-CqUPEnZw.js';
import { c as cn } from './utils-pWl1tgmi.js';

const EMBLA_CAROUSEL_CONTEXT = Symbol("EMBLA_CAROUSEL_CONTEXT");
function setEmblaContext(config) {
  setContext(EMBLA_CAROUSEL_CONTEXT, config);
  return config;
}
function getEmblaContext(name = "This component") {
  if (!hasContext(EMBLA_CAROUSEL_CONTEXT)) {
    throw new Error(`${name} must be used within a <Carousel.Root> component`);
  }
  return getContext(EMBLA_CAROUSEL_CONTEXT);
}
function Carousel($$payload, $$props) {
  push();
  let {
    ref = null,
    opts = {},
    plugins = [],
    setApi = () => {
    },
    orientation = "horizontal",
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let carouselState = {
    api: void 0,
    scrollPrev,
    scrollNext,
    orientation,
    canScrollNext: false,
    canScrollPrev: false,
    handleKeyDown,
    options: opts,
    plugins,
    onInit,
    scrollSnaps: [],
    selectedIndex: 0,
    scrollTo
  };
  setEmblaContext(carouselState);
  function scrollPrev() {
    carouselState.api?.scrollPrev();
  }
  function scrollNext() {
    carouselState.api?.scrollNext();
  }
  function scrollTo(index, jump) {
    carouselState.api?.scrollTo(index, jump);
  }
  function handleKeyDown(e) {
    if (e.key === "ArrowLeft") {
      e.preventDefault();
      scrollPrev();
    } else if (e.key === "ArrowRight") {
      e.preventDefault();
      scrollNext();
    }
  }
  function onInit(event) {
    carouselState.api = event.detail;
    carouselState.scrollSnaps = carouselState.api.scrollSnapList();
  }
  $$payload.out += `<div${spread_attributes(
    {
      "data-slot": "carousel",
      class: clsx(cn("relative", className)),
      role: "region",
      "aria-roledescription": "carousel",
      ...restProps
    },
    null
  )}>`;
  children?.($$payload);
  $$payload.out += `<!----></div>`;
  bind_props($$props, { ref });
  pop();
}
function Carousel_content($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const emblaCtx = getEmblaContext("<Carousel.Content/>");
  $$payload.out += `<div data-slot="carousel-content" class="overflow-hidden"><div${spread_attributes(
    {
      class: clsx(cn("flex", emblaCtx.orientation === "horizontal" ? "-ml-4" : "-mt-4 flex-col", className)),
      "data-embla-container": "",
      ...restProps
    },
    null
  )}>`;
  children?.($$payload);
  $$payload.out += `<!----></div></div>`;
  bind_props($$props, { ref });
  pop();
}
function Carousel_item($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const emblaCtx = getEmblaContext("<Carousel.Item/>");
  $$payload.out += `<div${spread_attributes(
    {
      "data-slot": "carousel-item",
      role: "group",
      "aria-roledescription": "slide",
      class: clsx(cn("min-w-0 shrink-0 grow-0 basis-full", emblaCtx.orientation === "horizontal" ? "pl-4" : "pt-4", className)),
      "data-embla-slide": "",
      ...restProps
    },
    null
  )}>`;
  children?.($$payload);
  $$payload.out += `<!----></div>`;
  bind_props($$props, { ref });
  pop();
}

export { Carousel as C, Carousel_content as a, Carousel_item as b };
//# sourceMappingURL=carousel-item-D-QWeSJJ.js.map
