import { p as push, R as spread_props, q as pop } from './index3-CqUPEnZw.js';
import { I as Icon } from './Icon2-DkOdBr51.js';

function Check($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [["path", { "d": "M20 6 9 17l-5-5" }]];
  Icon($$payload, spread_props([
    { name: "check" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}

export { Check as C };
//# sourceMappingURL=check2-Bg6barQb.js.map
