import { r as redirect, e as error } from './index-Ddp2AB5f.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import '@prisma/client';

const load = async ({ params, locals }) => {
  const user = locals.user;
  if (!user) throw redirect(302, "/auth/sign-in");
  const job = await prisma.job_listing.findUnique({
    where: { id: params.id }
  });
  if (!job) {
    throw error(404, "Job not found");
  }
  const savedJob = await prisma.savedJob.findFirst({
    where: {
      jobId: params.id,
      userId: user.id
    }
  });
  const appliedJob = await prisma.application.findFirst({
    where: {
      url: job.url,
      userId: user.id
    }
  });
  const jobMatch = await prisma.job_match_result.findFirst({
    where: {
      jobId: params.id,
      userId: user.id
    }
  });
  const profiles = await prisma.profile.findMany({
    where: {
      userId: user.id
    },
    include: {
      defaultDocument: true
    }
  });
  const userResumes = await prisma.resume.findMany({
    where: {
      document: {
        userId: user.id
      }
    },
    include: {
      document: true
    },
    take: 1,
    orderBy: {
      updatedAt: "desc"
    }
  });
  const extractedSkills = job.description ? extractSkillsFromDescription(job.description) : [];
  const jobSkills = [.../* @__PURE__ */ new Set([...job.skills || [], ...extractedSkills])];
  const jobTitleTerms = job.title.toLowerCase().split(/\s+/).filter((term) => term.length > 3).slice(0, 3);
  const similarJobs = await prisma.job_listing.findMany({
    where: {
      id: { not: params.id },
      OR: [
        // Match by first word in title
        { title: { contains: job.title.split(" ")[0], mode: "insensitive" } },
        // Match by company
        { company: { equals: job.company } },
        // Match by location
        { location: { equals: job.location } }
      ],
      isActive: true
    },
    take: 12,
    // Get more than we need so we can sort and filter
    orderBy: {
      postedDate: "desc"
    }
  });
  const enhancedSimilarJobs = similarJobs.map((similarJob) => {
    let relevanceScore = 0;
    jobTitleTerms.forEach((term) => {
      if (similarJob.title.toLowerCase().includes(term)) {
        relevanceScore += 0.2;
      }
    });
    if (similarJob.company === job.company) {
      relevanceScore += 0.3;
    }
    if (similarJob.location === job.location) {
      relevanceScore += 0.2;
    }
    const daysSincePosted = similarJob.postedDate ? Math.floor(
      ((/* @__PURE__ */ new Date()).getTime() - new Date(similarJob.postedDate).getTime()) / (1e3 * 60 * 60 * 24)
    ) : 30;
    relevanceScore += Math.max(0, 0.3 - daysSincePosted / 100);
    const matchPercentage = Math.round(relevanceScore * 100);
    return {
      ...similarJob,
      relevanceScore: Math.min(relevanceScore, 1),
      matchPercentage
    };
  }).sort((a, b) => b.relevanceScore - a.relevanceScore).slice(0, 6);
  const skillMatchData = generateSkillMatchData(job, jobSkills, profiles, userResumes, jobMatch);
  const formattedJob = {
    ...job,
    // Ensure these fields are available for the UI
    requirements: job.requirements || [],
    benefits: job.benefits || [],
    skills: jobSkills,
    // Add a default company logo URL if needed
    companyLogoUrl: null
  };
  return {
    job: formattedJob,
    matchScore: jobMatch?.matchScore || null,
    profiles,
    similarJobs: enhancedSimilarJobs,
    user,
    isSaved: !!savedJob,
    isApplied: !!appliedJob,
    skillMatchData
  };
};
function extractSkillsFromDescription(description) {
  const commonSkills = [
    "JavaScript",
    "TypeScript",
    "React",
    "Vue",
    "Angular",
    "Node.js",
    "Python",
    "Java",
    "C#",
    "C++",
    "Ruby",
    "PHP",
    "Go",
    "Rust",
    "AWS",
    "Azure",
    "GCP",
    "Docker",
    "Kubernetes",
    "CI/CD",
    "SQL",
    "NoSQL",
    "MongoDB",
    "PostgreSQL",
    "MySQL",
    "GraphQL",
    "REST",
    "HTML",
    "CSS",
    "SASS",
    "LESS",
    "Tailwind",
    "Bootstrap",
    "Git",
    "GitHub",
    "GitLab",
    "Agile",
    "Scrum",
    "Kanban",
    "Communication",
    "Problem Solving",
    "Team Work",
    "Leadership"
  ];
  const foundSkills = commonSkills.filter(
    (skill) => description.toLowerCase().includes(skill.toLowerCase())
  );
  return foundSkills;
}
function generateSkillMatchData(_job, jobSkills, _profiles, userResumes, jobMatch) {
  const overallMatch = jobMatch?.matchScore || 0.65;
  const skillsMatch = Math.min(overallMatch * (1 + Math.random() * 0.2), 0.95);
  const experienceMatch = Math.min(overallMatch * (1 + Math.random() * 0.1), 0.9);
  const educationMatch = Math.min(overallMatch * (1 - Math.random() * 0.1), 0.85);
  let userSkills = [];
  if (userResumes.length > 0 && userResumes[0].parsedData?.skills) {
    try {
      const parsedSkills = userResumes[0].parsedData.skills;
      if (Array.isArray(parsedSkills)) {
        userSkills = parsedSkills.map((s) => typeof s === "string" ? s : s.name || s.skill || "");
      } else if (typeof parsedSkills === "object") {
        userSkills = Object.keys(parsedSkills);
      }
    } catch (e) {
      console.error("Error parsing resume skills:", e);
    }
  }
  if (userSkills.length === 0) {
    userSkills = ["JavaScript", "React", "TypeScript", "HTML", "CSS", "Node.js"];
  }
  const matchedSkills = userSkills.filter(
    (skill) => jobSkills.some(
      (jobSkill) => jobSkill.toLowerCase().includes(skill.toLowerCase()) || skill.toLowerCase().includes(jobSkill.toLowerCase())
    )
  ).map((skill) => {
    let level = "Familiar";
    if (overallMatch > 0.7) level = "Proficient";
    if (overallMatch > 0.85) level = "Expert";
    return { name: skill, level };
  });
  const missingSkills = jobSkills.filter(
    (skill) => !userSkills.some(
      (userSkill) => userSkill.toLowerCase().includes(skill.toLowerCase()) || skill.toLowerCase().includes(userSkill.toLowerCase())
    )
  ).slice(0, 3).map((skill) => {
    const importanceOptions = ["Nice to have", "Preferred", "Required"];
    const importance = importanceOptions[Math.floor(Math.random() * importanceOptions.length)];
    return { name: skill, importance };
  });
  return {
    overallMatch,
    skillsMatch,
    experienceMatch,
    educationMatch,
    matchedSkills,
    missingSkills
  };
}

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 37;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-Fwc8Cl7x.js')).default;
const server_id = "src/routes/dashboard/jobs/[id]/+page.server.ts";
const imports = ["_app/immutable/nodes/37.BKALJWUb.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/DYwWIJ9y.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/DaBofrVv.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/DuGukytH.js","_app/immutable/chunks/Cdn-N1RY.js","_app/immutable/chunks/BkJY4La4.js","_app/immutable/chunks/DETxXRrJ.js","_app/immutable/chunks/GwmmX_iF.js","_app/immutable/chunks/D50jIuLr.js","_app/immutable/chunks/I7hvcB12.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/OXTnUuEm.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/tdzGgazS.js","_app/immutable/chunks/BaVT73bJ.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/Cb-3cdbh.js","_app/immutable/chunks/DMoa_yM9.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/CnpHcmx3.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/BnikQ10_.js","_app/immutable/chunks/BKLOCbjP.js","_app/immutable/chunks/BvvicRXk.js","_app/immutable/chunks/VNuMAkuB.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/DQC1iFs0.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/BBNNmnYR.js","_app/immutable/chunks/BAawoUIy.js","_app/immutable/chunks/BM9SsHQg.js","_app/immutable/chunks/CwgkX8t9.js","_app/immutable/chunks/C2AK_5VT.js","_app/immutable/chunks/CDnvByek.js","_app/immutable/chunks/DZCYCPd3.js","_app/immutable/chunks/FAbXdqfL.js","_app/immutable/chunks/DW7T7T22.js","_app/immutable/chunks/C88uNE8B.js","_app/immutable/chunks/DmZyh-PW.js","_app/immutable/chunks/CKh8VGVX.js","_app/immutable/chunks/CIPPbbaT.js","_app/immutable/chunks/6BxQgNmX.js","_app/immutable/chunks/zNKWipEG.js","_app/immutable/chunks/BAIxhb6t.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=37-DxatDyLu.js.map
