{"version": 3, "file": "client2-BLTPQNYX.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/client2.js"], "sourcesContent": ["import { createClient } from \"@sanity/client\";\nconst projectId = process.env.SANITY_PROJECT_ID || \"fqw18aoo\";\nconst dataset = process.env.SANITY_DATASET || \"production\";\nconst apiVersion = process.env.SANITY_API_VERSION || \"2023-05-03\";\nconst client = createClient({\n  projectId,\n  dataset,\n  apiVersion,\n  useCdn: process.env.NODE_ENV === \"production\"\n  // Use CDN in production for better performance\n});\nfunction urlFor(source, options = {}) {\n  if (!source || !source.asset || !source.asset._ref) {\n    return \"\";\n  }\n  const refId = source.asset._ref;\n  if (!refId.startsWith(\"image-\")) {\n    return \"\";\n  }\n  const parts = refId.split(\"-\");\n  const id = parts[1];\n  const format = parts.length > 3 ? parts[3] : \"jpg\";\n  if (!id) {\n    return \"\";\n  }\n  let url = `https://cdn.sanity.io/images/${projectId}/${dataset}/${id}.${format}`;\n  const params = [];\n  const width = options.width || 800;\n  params.push(`w=${width}`);\n  if (options.height) {\n    params.push(`h=${options.height}`);\n  }\n  const fit = options.fit || \"max\";\n  params.push(`fit=${fit}`);\n  params.push(\"auto=format\");\n  const quality = options.quality || 80;\n  params.push(`q=${quality}`);\n  if (params.length > 0) {\n    url += `?${params.join(\"&\")}`;\n  }\n  return url;\n}\nasync function getAll(type) {\n  {\n    return await client.fetch(`\n      *[_type == \"resource\"] | order(publishedAt desc) {\n        _id,\n        title,\n        slug,\n        resourceType,\n        category,\n        description,\n        featured,\n        mainImage,\n        icon,\n        tags,\n        publishedAt,\n        \"relatedResources\": relatedResources[]->\n      }\n    `);\n  }\n}\nasync function getResourceBySlug(slug) {\n  return await client.fetch(`\n    *[_type == \"resource\" && slug.current == \"${slug}\"][0] {\n      _id,\n      title,\n      slug,\n      resourceType,\n      category,\n      description,\n      featured,\n      mainImage,\n      icon,\n      content,\n      tags,\n      publishedAt,\n      downloadUrl,\n      \"relatedResources\": relatedResources[]-> {\n        _id,\n        title,\n        slug,\n        resourceType,\n        description,\n        mainImage,\n        icon\n      }\n    }\n  `);\n}\nasync function getHelpArticles() {\n  return await client.fetch(`\n    *[_type == \"helpArticle\"] | order(publishedAt desc) {\n      _id,\n      title,\n      slug,\n      category,\n      description,\n      featured,\n      icon,\n      tags,\n      publishedAt,\n      updatedAt,\n      viewCount\n    }\n  `);\n}\nasync function getFeaturedHelpArticles(limit = 6) {\n  return await client.fetch(`\n    *[_type == \"helpArticle\" && featured == true] | order(viewCount desc) [0...${limit}] {\n      _id,\n      title,\n      slug,\n      category,\n      description,\n      icon,\n      tags,\n      publishedAt,\n      updatedAt,\n      viewCount\n    }\n  `);\n}\nasync function getRecentHelpArticles(limit = 6) {\n  return await client.fetch(`\n    *[_type == \"helpArticle\"] | order(updatedAt desc) [0...${limit}] {\n      _id,\n      title,\n      slug,\n      category,\n      description,\n      icon,\n      tags,\n      publishedAt,\n      updatedAt,\n      viewCount\n    }\n  `);\n}\nasync function getHelpArticlesByCategory(category) {\n  return await client.fetch(`\n    *[_type == \"helpArticle\" && category == \"${category}\"] | order(title asc) {\n      _id,\n      title,\n      slug,\n      category,\n      description,\n      icon,\n      tags,\n      publishedAt,\n      updatedAt,\n      viewCount\n    }\n  `);\n}\nasync function getHelpArticleBySlug(slug) {\n  return await client.fetch(`\n    *[_type == \"helpArticle\" && slug.current == \"${slug}\"][0] {\n      _id,\n      title,\n      slug,\n      category,\n      description,\n      icon,\n      content,\n      tags,\n      publishedAt,\n      updatedAt,\n      viewCount,\n      \"relatedArticles\": relatedArticles[]-> {\n        _id,\n        title,\n        slug,\n        category,\n        description,\n        icon\n      }\n    }\n  `);\n}\nasync function searchHelpArticles(query, limit = 10) {\n  return await client.fetch(`\n    *[_type == \"helpArticle\" && (title match \"*${query}*\" || description match \"*${query}*\" || category match \"*${query}*\")] | order(viewCount desc) [0...${limit}] {\n      _id,\n      title,\n      slug,\n      category,\n      description,\n      icon,\n      tags,\n      publishedAt,\n      updatedAt,\n      viewCount\n    }\n  `);\n}\nexport {\n  getFeaturedHelpArticles as a,\n  getRecentHelpArticles as b,\n  client as c,\n  getHelpArticlesByCategory as d,\n  getHelpArticleBySlug as e,\n  getAll as f,\n  getHelpArticles as g,\n  getResourceBySlug as h,\n  searchHelpArticles as s,\n  urlFor as u\n};\n"], "names": [], "mappings": ";;AACA,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,UAAU;AAC7D,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,YAAY;AAC1D,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,YAAY;AAC5D,MAAC,MAAM,GAAG,YAAY,CAAC;AAC5B,EAAE,SAAS;AACX,EAAE,OAAO;AACT,EAAE,UAAU;AACZ,EAAE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK;AACnC;AACA,CAAC;AACD,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE;AACtC,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE;AACtD,IAAI,OAAO,EAAE;AACb;AACA,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI;AACjC,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;AACnC,IAAI,OAAO,EAAE;AACb;AACA,EAAE,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC;AAChC,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;AACrB,EAAE,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK;AACpD,EAAE,IAAI,CAAC,EAAE,EAAE;AACX,IAAI,OAAO,EAAE;AACb;AACA,EAAE,IAAI,GAAG,GAAG,CAAC,6BAA6B,EAAE,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAClF,EAAE,MAAM,MAAM,GAAG,EAAE;AACnB,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,GAAG;AACpC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;AAC3B,EAAE,IAAI,OAAO,CAAC,MAAM,EAAE;AACtB,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AACtC;AACA,EAAE,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,KAAK;AAClC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;AAC3B,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;AAC5B,EAAE,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE;AACvC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;AAC7B,EAAE,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;AACzB,IAAI,GAAG,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACjC;AACA,EAAE,OAAO,GAAG;AACZ;AACA,eAAe,MAAM,CAAC,IAAI,EAAE;AAC5B,EAAE;AACF,IAAI,OAAO,MAAM,MAAM,CAAC,KAAK,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,CAAC;AACN;AACA;AACA,eAAe,iBAAiB,CAAC,IAAI,EAAE;AACvC,EAAE,OAAO,MAAM,MAAM,CAAC,KAAK,CAAC;AAC5B,8CAA8C,EAAE,IAAI,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,CAAC,CAAC;AACJ;AACA,eAAe,eAAe,GAAG;AACjC,EAAE,OAAO,MAAM,MAAM,CAAC,KAAK,CAAC;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,CAAC,CAAC;AACJ;AACA,eAAe,uBAAuB,CAAC,KAAK,GAAG,CAAC,EAAE;AAClD,EAAE,OAAO,MAAM,MAAM,CAAC,KAAK,CAAC;AAC5B,+EAA+E,EAAE,KAAK,CAAC;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,CAAC,CAAC;AACJ;AACA,eAAe,qBAAqB,CAAC,KAAK,GAAG,CAAC,EAAE;AAChD,EAAE,OAAO,MAAM,MAAM,CAAC,KAAK,CAAC;AAC5B,2DAA2D,EAAE,KAAK,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,CAAC,CAAC;AACJ;AACA,eAAe,yBAAyB,CAAC,QAAQ,EAAE;AACnD,EAAE,OAAO,MAAM,MAAM,CAAC,KAAK,CAAC;AAC5B,6CAA6C,EAAE,QAAQ,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,CAAC,CAAC;AACJ;AACA,eAAe,oBAAoB,CAAC,IAAI,EAAE;AAC1C,EAAE,OAAO,MAAM,MAAM,CAAC,KAAK,CAAC;AAC5B,iDAAiD,EAAE,IAAI,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,CAAC,CAAC;AACJ;AACA,eAAe,kBAAkB,CAAC,KAAK,EAAE,KAAK,GAAG,EAAE,EAAE;AACrD,EAAE,OAAO,MAAM,MAAM,CAAC,KAAK,CAAC;AAC5B,+CAA+C,EAAE,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAClK;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,CAAC,CAAC;AACJ;;;;"}