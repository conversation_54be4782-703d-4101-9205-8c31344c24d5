import { chromium } from "playwright";
import { uploadFile } from "./lib/storage/r2Storage";

/**
 * Test script to search for "Wyndy logo company" with headless mode
 */
async function testWyndyLogo() {
  console.log("🎨 Testing Wyndy logo search with headless mode");
  
  let browser = null;
  let page = null;

  try {
    // Launch browser in headless mode
    console.log("🌐 Launching browser in headless mode...");
    browser = await chromium.launch({
      headless: true, // Test with headless mode
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
    });

    const context = await browser.newContext({
      userAgent:
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    });

    page = await context.newPage();

    // Search for Wyndy logo
    const companyName = "Wyndy";
    const searchQuery = encodeURIComponent(`"${companyName}" logo company`);
    const bingUrl = `https://www.bing.com/images/search?q=${searchQuery}`;

    console.log(`🔍 Searching for: ${companyName} logo`);
    console.log(`🌐 Bing Images URL: ${bingUrl}`);

    await page.goto(bingUrl, { waitUntil: "networkidle" });

    // Wait for images to load
    await page.waitForTimeout(3000);

    // Extract image URLs from Bing Images results
    const imageUrls = await page.evaluate(() => {
      // Bing Images uses specific selectors for the actual image results
      const imageElements = Array.from(document.querySelectorAll(".iusc"));
      const urls: string[] = [];

      imageElements.forEach((element: any) => {
        try {
          const dataAttr = element.getAttribute("m");
          if (dataAttr) {
            const data = JSON.parse(dataAttr);
            if (data.murl) {
              urls.push(data.murl);
            }
          }
        } catch (e) {
          // Skip invalid elements
        }
      });

      return urls.slice(0, 3); // Get first 3 images
    });

    if (imageUrls.length === 0) {
      console.log("❌ No images found in Bing Images results");
      return;
    }

    console.log(`🎯 Found ${imageUrls.length} potential logo images:`);
    imageUrls.forEach((url, index) => {
      console.log(`  ${index + 1}. ${url}`);
    });

    // Try to download and upload the first image
    const imageUrl = imageUrls[0];
    console.log(`📥 Trying to download: ${imageUrl}`);

    try {
      const imageResponse = await fetch(imageUrl, {
        headers: {
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
          Referer: "https://www.google.com/",
        },
      });

      if (
        imageResponse.ok &&
        imageResponse.headers.get("content-type")?.startsWith("image/")
      ) {
        const buffer = Buffer.from(await imageResponse.arrayBuffer());
        const contentType =
          imageResponse.headers.get("content-type") || "image/png";

        console.log(`📦 Downloaded image: ${buffer.length} bytes, type: ${contentType}`);

        // Generate a clean filename
        const cleanCompanyName = companyName
          .toLowerCase()
          .replace(/[^a-z0-9]/g, "-")
          .replace(/-+/g, "-")
          .replace(/^-|-$/g, "");

        const extension = contentType.includes("svg")
          ? "svg"
          : contentType.includes("jpeg")
            ? "jpg"
            : "png";
        const filename = `${cleanCompanyName}-test-${Date.now()}.${extension}`;

        console.log(`📤 Uploading to R2: ${filename}`);

        const uploadResult = await uploadFile(
          buffer,
          filename,
          contentType,
          "companyLogos"
        );

        if (uploadResult.success) {
          console.log(`✅ Upload successful!`);
          console.log(`🔗 Public URL: ${uploadResult.publicUrl}`);
        } else {
          console.log(`❌ Upload failed: ${uploadResult.error}`);
        }
      } else {
        console.log(`❌ Failed to download image: ${imageResponse.status} ${imageResponse.statusText}`);
      }
    } catch (downloadError) {
      console.log(`❌ Error downloading image: ${downloadError}`);
    }

  } catch (error) {
    console.error("❌ Error during test:", error);
  } finally {
    if (page) {
      await page.close();
    }
    if (browser) {
      await browser.close();
    }
  }
}

// Run the test
testWyndyLogo()
  .then(() => {
    console.log("✅ Test completed");
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Test failed:", error);
    process.exit(1);
  });
