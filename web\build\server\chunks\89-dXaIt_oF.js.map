{"version": 3, "file": "89-dXaIt_oF.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/profile/_id_/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/89.js"], "sourcesContent": ["import { r as redirect, e as error } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nimport { p as parseProfileData } from \"../../../../chunks/profileHelpers.js\";\nconst load = async ({ params, locals }) => {\n  const user = locals.user;\n  if (!user) {\n    throw redirect(302, \"/auth/sign-in\");\n  }\n  const { id } = params;\n  if (!id) {\n    throw error(400, \"Profile ID is required\");\n  }\n  try {\n    console.log(\"Loading profile with ID:\", id);\n    console.log(\"User ID:\", user.id);\n    const profile = await prisma.profile.findUnique({\n      where: {\n        id,\n        userId: user.id\n        // Ensure the profile belongs to the user\n      },\n      include: {\n        data: true,\n        defaultDocument: true\n      }\n    });\n    console.log(\"Profile found:\", profile ? \"Yes\" : \"No\");\n    if (!profile) {\n      throw error(404, \"Profile not found\");\n    }\n    console.log(\"Fetching documents for user:\", user.id);\n    const documents = await prisma.document.findMany({\n      where: {\n        userId: user.id,\n        // Filter for documents that have a resume relation\n        resume: {\n          isNot: null\n        }\n      },\n      select: {\n        id: true,\n        label: true,\n        resume: true,\n        updatedAt: true\n      },\n      orderBy: {\n        updatedAt: \"desc\"\n      }\n    });\n    console.log(\"Documents found:\", documents.length);\n    const formattedDocuments = documents.map((doc) => ({\n      id: doc.id,\n      label: doc.label || `Resume (${new Date(doc.updatedAt).toLocaleDateString()})`,\n      isDefault: doc.id === profile.defaultDocumentId\n    }));\n    const profileData = profile.data ? parseProfileData(profile.data.data) : {};\n    return {\n      profile,\n      profileData,\n      documents: formattedDocuments,\n      user\n    };\n  } catch (err) {\n    console.error(\"Error loading profile:\", err);\n    throw error(500, \"Failed to load profile\");\n  }\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/profile/_id_/_page.server.ts.js';\n\nexport const index = 89;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/profile/_id_/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/profile/[id]/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/89.OU2lh2fk.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/DaBofrVv.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/BPr9JIwg.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/OXTnUuEm.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/BwkAotBa.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/C2MdR6K0.js\",\"_app/immutable/chunks/hQ6uUXJy.js\",\"_app/immutable/chunks/Cb-3cdbh.js\",\"_app/immutable/chunks/DQC1iFs0.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/Ce6y1v79.js\",\"_app/immutable/chunks/B_6ivTD3.js\",\"_app/immutable/chunks/ChqRiddM.js\",\"_app/immutable/chunks/CDnvByek.js\",\"_app/immutable/chunks/DQB68x0Z.js\",\"_app/immutable/chunks/mCB4pHNc.js\",\"_app/immutable/chunks/B_tyjpYb.js\"];\nexport const stylesheets = [\"_app/immutable/assets/scroll-area.bHHIbcsu.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;;AAGA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC3C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACxC;AACA,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACvB,EAAE,IAAI,CAAC,EAAE,EAAE;AACX,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,wBAAwB,CAAC;AAC9C;AACA,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,EAAE,CAAC;AAC/C,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC;AACpC,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AACpD,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE;AACV,QAAQ,MAAM,EAAE,IAAI,CAAC;AACrB;AACA,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,eAAe,EAAE;AACzB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,GAAG,KAAK,GAAG,IAAI,CAAC;AACzD,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,MAAM,KAAK,CAAC,GAAG,EAAE,mBAAmB,CAAC;AAC3C;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC,EAAE,CAAC;AACxD,IAAI,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACrD,MAAM,KAAK,EAAE;AACb,QAAQ,MAAM,EAAE,IAAI,CAAC,EAAE;AACvB;AACA,QAAQ,MAAM,EAAE;AAChB,UAAU,KAAK,EAAE;AACjB;AACA,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,IAAI;AAChB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,SAAS,EAAE;AACnB,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,SAAS,CAAC,MAAM,CAAC;AACrD,IAAI,MAAM,kBAAkB,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AACvD,MAAM,EAAE,EAAE,GAAG,CAAC,EAAE;AAChB,MAAM,KAAK,EAAE,GAAG,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;AACpF,MAAM,SAAS,EAAE,GAAG,CAAC,EAAE,KAAK,OAAO,CAAC;AACpC,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,GAAG,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;AAC/E,IAAI,OAAO;AACX,MAAM,OAAO;AACb,MAAM,WAAW;AACjB,MAAM,SAAS,EAAE,kBAAkB;AACnC,MAAM;AACN,KAAK;AACL,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,GAAG,CAAC;AAChD,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,wBAAwB,CAAC;AAC9C;AACA,CAAC;;;;;;;AChEW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA+C,CAAC,EAAE;AAE7G,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACroD,MAAC,WAAW,GAAG,CAAC,gDAAgD;AAChE,MAAC,KAAK,GAAG;;;;"}