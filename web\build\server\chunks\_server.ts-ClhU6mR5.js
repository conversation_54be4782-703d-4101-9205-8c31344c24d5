import { j as json } from './index-Ddp2AB5f.js';
import { v as verifySessionToken } from './auth-BPad-IlN.js';
import { getUserFeatureUsageWithPlanLimits } from './feature-usage-SYWaZZiX.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import { g as getPlanById } from './plan-sync-CZNz1Ayv.js';
import 'jsonwebtoken';
import 'ua-parser-js';
import './index4-HpJcNJHQ.js';
import './false-CRHihH2U.js';
import './dynamic-registry-Cmy1Wm2Q.js';
import './features-SWeUHekJ.js';
import '@prisma/client';
import './stripe-Dj5-FP5W.js';
import './stripe.esm.node-BpZO3rKl.js';
import 'crypto';
import 'events';
import 'http';
import 'https';
import './_commonjsHelpers-BFTU3MAI.js';
import 'util';
import 'child_process';

const GET = async ({ cookies, url }) => {
  const token = cookies.get("auth_token");
  if (!token) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }
  const userData = await verifySessionToken(token);
  if (!userData?.id) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }
  try {
    const dataType = url.searchParams.get("type") || "all";
    if (dataType === "resume") {
      const startOfMonth = /* @__PURE__ */ new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);
      const used = await prisma.documentSubmission.count({
        where: {
          userId: userData.id,
          createdAt: {
            gte: startOfMonth
          }
        }
      });
      const userPlan = await getPlanById(userData.role || "free");
      const resumeScannerFeature = userPlan?.features.find((f) => f.featureId === "resume_scanner");
      const resumeScansLimit = resumeScannerFeature?.limits?.find(
        (l) => l.limitId === "resume_scans_per_month"
      );
      const limit = resumeScansLimit ? resumeScansLimit.value === "unlimited" ? null : Number(resumeScansLimit.value) : null;
      const remaining = limit !== null ? Math.max(0, limit - used) : null;
      return json({ used, limit, remaining });
    } else if (dataType === "features") {
      const usageData = await getUserFeatureUsageWithPlanLimits(userData.id);
      return json(usageData);
    } else {
      const startOfMonth = /* @__PURE__ */ new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);
      const used = await prisma.documentSubmission.count({
        where: {
          userId: userData.id,
          createdAt: {
            gte: startOfMonth
          }
        }
      });
      const userPlan = await getPlanById(userData.role || "free");
      const resumeScannerFeature = userPlan?.features.find((f) => f.featureId === "resume_scanner");
      const resumeScansLimit = resumeScannerFeature?.limits?.find(
        (l) => l.limitId === "resume_scans_per_month"
      );
      const limit = resumeScansLimit ? resumeScansLimit.value === "unlimited" ? null : Number(resumeScansLimit.value) : null;
      const remaining = limit !== null ? Math.max(0, limit - used) : null;
      const usageData = await getUserFeatureUsageWithPlanLimits(userData.id);
      return json({
        resume: { used, limit, remaining },
        features: usageData
      });
    }
  } catch (error) {
    console.error("Error in usage API:", error);
    return json(
      {
        error: error.message || "An error occurred while fetching usage data"
      },
      { status: 500 }
    );
  }
};

export { GET };
//# sourceMappingURL=_server.ts-ClhU6mR5.js.map
