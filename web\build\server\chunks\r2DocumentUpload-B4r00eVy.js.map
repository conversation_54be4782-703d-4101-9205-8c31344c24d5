{"version": 3, "file": "r2DocumentUpload-B4r00eVy.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/r2DocumentUpload.js"], "sourcesContent": ["import { S3Client, CopyObjectCommand, DeleteObjectCommand, PutObjectCommand } from \"@aws-sdk/client-s3\";\nconst logger = {\n  info: (message, ...args) => console.log(`[INFO] ${message}`, ...args),\n  error: (message, ...args) => console.error(`[ERROR] ${message}`, ...args),\n  warn: (message, ...args) => console.warn(`[WARN] ${message}`, ...args)\n};\nconst R2_CONFIG = {\n  endpoint: process.env.R2_ENDPOINT?.replace(/\\/+$/, \"\") || \"https://7efc1bf67e7d23f5683e06d0227c883f.r2.cloudflarestorage.com\",\n  // Working endpoint\n  region: \"auto\",\n  // R2 uses 'auto' as region\n  credentials: {\n    accessKeyId: process.env.R2_ACCESS_KEY_ID ?? \"\",\n    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY ?? \"\"\n  },\n  forcePathStyle: true\n  // Required for R2 compatibility\n};\nconst r2Client = new S3Client(R2_CONFIG);\nconst BUCKET_NAMES = {\n  [\n    \"company\"\n    /* COMPANY */\n  ]: \"hirli-company-logos\",\n  [\n    \"resumes\"\n    /* RESUMES */\n  ]: \"hirli-resume-files\",\n  [\n    \"user\"\n    /* USER */\n  ]: \"hirli-user-images\",\n  [\n    \"jobs\"\n    /* JOBS */\n  ]: \"hirli-job-assets\",\n  [\n    \"analytics\"\n    /* ANALYTICS */\n  ]: \"hirli-analytics-data\"\n};\nfunction getBucketName(bucketType) {\n  return BUCKET_NAMES[bucketType];\n}\nfunction generateFileKey(fileType, originalName, identifier) {\n  const timestamp = Date.now();\n  const randomSuffix = Math.random().toString(36).substring(2, 8);\n  const extension = originalName.split(\".\").pop() || \"\";\n  const baseName = originalName.replace(/\\.[^/.]+$/, \"\").replace(/[^a-zA-Z0-9]/g, \"-\").toLowerCase();\n  if (fileType === \"resumes\" && identifier) {\n    return `profile-${identifier}-resume-${timestamp}.${extension}`;\n  }\n  return `${baseName}-${timestamp}-${randomSuffix}.${extension}`;\n}\nfunction getPublicUrl(fileKey) {\n  const customDomain = process.env.R2_CUSTOM_DOMAIN;\n  if (customDomain) {\n    return `https://${customDomain}/${fileKey}`;\n  }\n  const workerUrl = process.env.R2_WORKER_URL || \"https://hirli-static-assets.christopher-eugene-rodriguez.workers.dev\";\n  let pathPrefix = \"\";\n  if (fileKey.includes(\"resume\")) {\n    pathPrefix = \"resumes/\";\n  } else if (fileKey.includes(\"logo\")) {\n    pathPrefix = \"logos/\";\n  } else {\n    pathPrefix = \"user/\";\n  }\n  return `${workerUrl}/${pathPrefix}${fileKey}`;\n}\nasync function uploadFile(buffer, originalName, contentType, fileType, identifier) {\n  try {\n    let bucketType;\n    switch (fileType) {\n      case \"resumes\":\n        bucketType = \"resumes\";\n        break;\n      case \"companyLogos\":\n        bucketType = \"company\";\n        break;\n      case \"userDocuments\":\n      default:\n        bucketType = \"user\";\n        break;\n    }\n    const bucketName = getBucketName(bucketType);\n    const fileKey = generateFileKey(fileType, originalName, identifier);\n    logger.info(`📤 Uploading to bucket: ${bucketName} with key: ${fileKey}`);\n    const uploadCommand = new PutObjectCommand({\n      Bucket: bucketName,\n      Key: fileKey,\n      Body: buffer,\n      ContentType: contentType,\n      Metadata: {\n        originalName,\n        uploadedAt: (/* @__PURE__ */ new Date()).toISOString(),\n        fileType,\n        bucketType,\n        ...identifier && { identifier }\n      }\n    });\n    await r2Client.send(uploadCommand);\n    const publicUrl = getPublicUrl(fileKey);\n    logger.info(\n      `✅ File uploaded successfully to ${bucketName}: ${fileKey} (${buffer.length} bytes)`\n    );\n    return {\n      success: true,\n      fileKey,\n      publicUrl,\n      fileSize: buffer.length,\n      contentType,\n      bucketName\n    };\n  } catch (error) {\n    logger.error(`❌ Failed to upload file ${originalName}:`, error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : \"Unknown upload error\"\n    };\n  }\n}\nconst DOCUMENT_TYPE_TO_R2_TYPE = {\n  resume: \"resumes\",\n  \"cover-letter\": \"userDocuments\",\n  portfolio: \"userDocuments\",\n  transcript: \"userDocuments\",\n  certificate: \"userDocuments\",\n  default: \"userDocuments\"\n};\nasync function uploadDocumentToR2(file, documentType = \"default\", identifier) {\n  try {\n    logger.info(`📤 Uploading document to R2: ${file.name} (type: ${documentType})`);\n    if (!file || file.size === 0) {\n      throw new Error(\"No file provided or file is empty\");\n    }\n    const maxSize = 10 * 1024 * 1024;\n    if (file.size > maxSize) {\n      throw new Error(`File size ${file.size} exceeds maximum allowed size of ${maxSize} bytes`);\n    }\n    const allowedTypes = [\n      \"application/pdf\",\n      \"application/msword\",\n      \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\"\n    ];\n    if (!allowedTypes.includes(file.type)) {\n      throw new Error(\n        `File type ${file.type} is not supported. Please upload a PDF or Word document.`\n      );\n    }\n    const buffer = Buffer.from(await file.arrayBuffer());\n    const r2FileType = DOCUMENT_TYPE_TO_R2_TYPE[documentType] || DOCUMENT_TYPE_TO_R2_TYPE.default;\n    const uploadResult = await uploadFile(buffer, file.name, file.type, r2FileType, identifier);\n    if (!uploadResult.success) {\n      throw new Error(uploadResult.error || \"Upload failed\");\n    }\n    logger.info(`✅ Document uploaded successfully to R2: ${uploadResult.publicUrl}`);\n    return {\n      success: true,\n      originalFileName: file.name,\n      filename: uploadResult.fileKey?.split(\"/\").pop() || file.name,\n      filePath: uploadResult.fileKey,\n      // R2 file key\n      publicPath: uploadResult.publicUrl,\n      // R2 public URL\n      fileSize: uploadResult.fileSize,\n      contentType: uploadResult.contentType\n    };\n  } catch (error) {\n    logger.error(`❌ Failed to upload document to R2:`, error);\n    return {\n      success: false,\n      originalFileName: file.name,\n      filename: \"\",\n      filePath: \"\",\n      publicPath: \"\",\n      fileSize: 0,\n      contentType: file.type,\n      error: error instanceof Error ? error.message : \"Unknown upload error\"\n    };\n  }\n}\nasync function updateResumeFileKey(oldFileKey, resumeId) {\n  try {\n    logger.info(`🔄 Updating resume file key: ${oldFileKey} -> resume-${resumeId}`);\n    const bucketName = getBucketName(\n      \"resumes\"\n      /* RESUMES */\n    );\n    const extension = oldFileKey.split(\".\").pop() || \"pdf\";\n    const newFileKey = `profile-resume-${resumeId}.${extension}`;\n    const copyCommand = new CopyObjectCommand({\n      Bucket: bucketName,\n      Key: newFileKey,\n      CopySource: `${bucketName}/${oldFileKey}`,\n      MetadataDirective: \"COPY\"\n    });\n    await r2Client.send(copyCommand);\n    const deleteCommand = new DeleteObjectCommand({\n      Bucket: bucketName,\n      Key: oldFileKey\n    });\n    await r2Client.send(deleteCommand);\n    const newPublicUrl = getPublicUrl(newFileKey);\n    logger.info(`✅ Resume file key updated successfully: ${newFileKey}`);\n    return {\n      success: true,\n      newFileKey,\n      newPublicUrl\n    };\n  } catch (error) {\n    logger.error(`❌ Failed to update resume file key:`, error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : \"Unknown error\"\n    };\n  }\n}\nexport {\n  uploadDocumentToR2 as default,\n  getPublicUrl,\n  updateResumeFileKey,\n  uploadDocumentToR2,\n  uploadFile\n};\n"], "names": [], "mappings": ";;AACA,MAAM,MAAM,GAAG;AACf,EAAE,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC;AACvE,EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC;AAC3E,EAAE,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE,GAAG,IAAI;AACvE,CAAC;AACD,MAAM,SAAS,GAAG;AAClB,EAAE,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,mEAAmE;AAC/H;AACA,EAAE,MAAM,EAAE,MAAM;AAChB;AACA,EAAE,WAAW,EAAE;AACf,IAAI,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,EAAE;AACnD,IAAI,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI;AACzD,GAAG;AACH,EAAE,cAAc,EAAE;AAClB;AACA,CAAC;AACD,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,SAAS,CAAC;AACxC,MAAM,YAAY,GAAG;AACrB,EAAE;AACF,IAAI;AACJ;AACA,KAAK,qBAAqB;AAC1B,EAAE;AACF,IAAI;AACJ;AACA,KAAK,oBAAoB;AACzB,EAAE;AACF,IAAI;AACJ;AACA,KAAK,mBAAmB;AACxB,EAAE;AACF,IAAI;AACJ;AACA,KAAK,kBAAkB;AACvB,EAAE;AACF,IAAI;AACJ;AACA,KAAK;AACL,CAAC;AACD,SAAS,aAAa,CAAC,UAAU,EAAE;AACnC,EAAE,OAAO,YAAY,CAAC,UAAU,CAAC;AACjC;AACA,SAAS,eAAe,CAAC,QAAQ,EAAE,YAAY,EAAE,UAAU,EAAE;AAC7D,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE;AAC9B,EAAE,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;AACjE,EAAE,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE;AACvD,EAAE,MAAM,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE;AACpG,EAAE,IAAI,QAAQ,KAAK,SAAS,IAAI,UAAU,EAAE;AAC5C,IAAI,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;AACnE;AACA,EAAE,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;AAChE;AACA,SAAS,YAAY,CAAC,OAAO,EAAE;AAC/B,EAAE,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB;AACnD,EAAE,IAAI,YAAY,EAAE;AACpB,IAAI,OAAO,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AAC/C;AACA,EAAE,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,sEAAsE;AACvH,EAAE,IAAI,UAAU,GAAG,EAAE;AACrB,EAAE,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AAClC,IAAI,UAAU,GAAG,UAAU;AAC3B,GAAG,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AACvC,IAAI,UAAU,GAAG,QAAQ;AACzB,GAAG,MAAM;AACT,IAAI,UAAU,GAAG,OAAO;AACxB;AACA,EAAE,OAAO,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,OAAO,CAAC,CAAC;AAC/C;AACA,eAAe,UAAU,CAAC,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE;AACnF,EAAE,IAAI;AACN,IAAI,IAAI,UAAU;AAClB,IAAI,QAAQ,QAAQ;AACpB,MAAM,KAAK,SAAS;AACpB,QAAQ,UAAU,GAAG,SAAS;AAC9B,QAAQ;AACR,MAAM,KAAK,cAAc;AACzB,QAAQ,UAAU,GAAG,SAAS;AAC9B,QAAQ;AACR,MAAM,KAAK,eAAe;AAC1B,MAAM;AACN,QAAQ,UAAU,GAAG,MAAM;AAC3B,QAAQ;AACR;AACA,IAAI,MAAM,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC;AAChD,IAAI,MAAM,OAAO,GAAG,eAAe,CAAC,QAAQ,EAAE,YAAY,EAAE,UAAU,CAAC;AACvE,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,wBAAwB,EAAE,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;AAC7E,IAAI,MAAM,aAAa,GAAG,IAAI,gBAAgB,CAAC;AAC/C,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,GAAG,EAAE,OAAO;AAClB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,WAAW,EAAE,WAAW;AAC9B,MAAM,QAAQ,EAAE;AAChB,QAAQ,YAAY;AACpB,QAAQ,UAAU,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE;AAC9D,QAAQ,QAAQ;AAChB,QAAQ,UAAU;AAClB,QAAQ,GAAG,UAAU,IAAI,EAAE,UAAU;AACrC;AACA,KAAK,CAAC;AACN,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC;AACtC,IAAI,MAAM,SAAS,GAAG,YAAY,CAAC,OAAO,CAAC;AAC3C,IAAI,MAAM,CAAC,IAAI;AACf,MAAM,CAAC,gCAAgC,EAAE,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO;AACzF,KAAK;AACL,IAAI,OAAO;AACX,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO;AACb,MAAM,SAAS;AACf,MAAM,QAAQ,EAAE,MAAM,CAAC,MAAM;AAC7B,MAAM,WAAW;AACjB,MAAM;AACN,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,wBAAwB,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;AACnE,IAAI,OAAO;AACX,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,KAAK,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG;AACtD,KAAK;AACL;AACA;AACA,MAAM,wBAAwB,GAAG;AACjC,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,cAAc,EAAE,eAAe;AACjC,EAAE,SAAS,EAAE,eAAe;AAC5B,EAAE,UAAU,EAAE,eAAe;AAC7B,EAAE,WAAW,EAAE,eAAe;AAC9B,EAAE,OAAO,EAAE;AACX,CAAC;AACD,eAAe,kBAAkB,CAAC,IAAI,EAAE,YAAY,GAAG,SAAS,EAAE,UAAU,EAAE;AAC9E,EAAE,IAAI;AACN,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,6BAA6B,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;AACpF,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE;AAClC,MAAM,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC;AAC1D;AACA,IAAI,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI;AACpC,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,OAAO,EAAE;AAC7B,MAAM,MAAM,IAAI,KAAK,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,iCAAiC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AAChG;AACA,IAAI,MAAM,YAAY,GAAG;AACzB,MAAM,iBAAiB;AACvB,MAAM,oBAAoB;AAC1B,MAAM;AACN,KAAK;AACL,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AAC3C,MAAM,MAAM,IAAI,KAAK;AACrB,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,wDAAwD;AACvF,OAAO;AACP;AACA,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;AACxD,IAAI,MAAM,UAAU,GAAG,wBAAwB,CAAC,YAAY,CAAC,IAAI,wBAAwB,CAAC,OAAO;AACjG,IAAI,MAAM,YAAY,GAAG,MAAM,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,UAAU,CAAC;AAC/F,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;AAC/B,MAAM,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI,eAAe,CAAC;AAC5D;AACA,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,wCAAwC,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC;AACpF,IAAI,OAAO;AACX,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,gBAAgB,EAAE,IAAI,CAAC,IAAI;AACjC,MAAM,QAAQ,EAAE,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,IAAI;AACnE,MAAM,QAAQ,EAAE,YAAY,CAAC,OAAO;AACpC;AACA,MAAM,UAAU,EAAE,YAAY,CAAC,SAAS;AACxC;AACA,MAAM,QAAQ,EAAE,YAAY,CAAC,QAAQ;AACrC,MAAM,WAAW,EAAE,YAAY,CAAC;AAChC,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,kCAAkC,CAAC,EAAE,KAAK,CAAC;AAC7D,IAAI,OAAO;AACX,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,gBAAgB,EAAE,IAAI,CAAC,IAAI;AACjC,MAAM,QAAQ,EAAE,EAAE;AAClB,MAAM,QAAQ,EAAE,EAAE;AAClB,MAAM,UAAU,EAAE,EAAE;AACpB,MAAM,QAAQ,EAAE,CAAC;AACjB,MAAM,WAAW,EAAE,IAAI,CAAC,IAAI;AAC5B,MAAM,KAAK,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG;AACtD,KAAK;AACL;AACA;AACA,eAAe,mBAAmB,CAAC,UAAU,EAAE,QAAQ,EAAE;AACzD,EAAE,IAAI;AACN,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,6BAA6B,EAAE,UAAU,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;AACnF,IAAI,MAAM,UAAU,GAAG,aAAa;AACpC,MAAM;AACN;AACA,KAAK;AACL,IAAI,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK;AAC1D,IAAI,MAAM,UAAU,GAAG,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;AAChE,IAAI,MAAM,WAAW,GAAG,IAAI,iBAAiB,CAAC;AAC9C,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,GAAG,EAAE,UAAU;AACrB,MAAM,UAAU,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AAC/C,MAAM,iBAAiB,EAAE;AACzB,KAAK,CAAC;AACN,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC;AACpC,IAAI,MAAM,aAAa,GAAG,IAAI,mBAAmB,CAAC;AAClD,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,GAAG,EAAE;AACX,KAAK,CAAC;AACN,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC;AACtC,IAAI,MAAM,YAAY,GAAG,YAAY,CAAC,UAAU,CAAC;AACjD,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,wCAAwC,EAAE,UAAU,CAAC,CAAC,CAAC;AACxE,IAAI,OAAO;AACX,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU;AAChB,MAAM;AACN,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,mCAAmC,CAAC,EAAE,KAAK,CAAC;AAC9D,IAAI,OAAO;AACX,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,KAAK,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG;AACtD,KAAK;AACL;AACA;;;;"}