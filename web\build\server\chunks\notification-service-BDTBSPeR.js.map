{"version": 3, "file": "notification-service-BDTBSPeR.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/notification-service.js"], "sourcesContent": ["import { p as prisma } from \"./prisma.js\";\nimport { g as getRedisClient } from \"./redis.js\";\nvar NotificationType = /* @__PURE__ */ ((NotificationType2) => {\n  NotificationType2[\"SYSTEM\"] = \"system\";\n  NotificationType2[\"JOB\"] = \"job\";\n  NotificationType2[\"APPLICATION\"] = \"application\";\n  NotificationType2[\"INTERVIEW\"] = \"interview\";\n  NotificationType2[\"MESSAGE\"] = \"message\";\n  NotificationType2[\"ERROR\"] = \"error\";\n  NotificationType2[\"SUCCESS\"] = \"success\";\n  NotificationType2[\"WARNING\"] = \"warning\";\n  NotificationType2[\"INFO\"] = \"info\";\n  return NotificationType2;\n})(NotificationType || {});\nvar NotificationPriority = /* @__PURE__ */ ((NotificationPriority2) => {\n  NotificationPriority2[\"LOW\"] = \"low\";\n  NotificationPriority2[\"MEDIUM\"] = \"medium\";\n  NotificationPriority2[\"HIGH\"] = \"high\";\n  NotificationPriority2[\"URGENT\"] = \"urgent\";\n  return NotificationPriority2;\n})(NotificationPriority || {});\nasync function sendNotificationToUser(userId, data) {\n  try {\n    const preferences = await prisma.notificationSettings.findUnique({\n      where: { userId }\n    });\n    if (preferences && !preferences.browserEnabled) {\n      console.log(`User ${userId} has disabled in-app notifications`);\n      return false;\n    }\n    const notificationData = {\n      userId,\n      title: data.title,\n      message: data.message,\n      url: data.url,\n      type: data.type || \"info\",\n      priority: data.priority || \"medium\",\n      metadata: data.metadata ? JSON.stringify(data.metadata) : null,\n      expiresAt: data.expiresAt,\n      read: false\n    };\n    let notification;\n    try {\n      notification = await prisma.notification.create({\n        data: notificationData\n      });\n      console.log(`Created notification in database with ID ${notification.id}`);\n    } catch (error) {\n      console.error(\"Error creating notification:\", error);\n      throw error;\n    }\n    const redis = await getRedisClient();\n    if (!redis) {\n      console.error(\"Redis client not available\");\n      return false;\n    }\n    const requestId = `notification:${notification.id}:${Date.now()}`;\n    console.log(`Generated request ID: ${requestId} for notification ${notification.id}`);\n    console.log(\n      `Publishing notification ${notification.id} to Redis channel user:${userId}:notifications with request ID ${requestId}`\n    );\n    await redis.publish(\n      `user:${userId}:notifications`,\n      JSON.stringify({\n        id: notification.id,\n        title: notification.title,\n        message: notification.message,\n        url: notification.url,\n        type: notification.type,\n        timestamp: notification.createdAt.toISOString(),\n        requestId\n        // Include the request ID in the notification\n      })\n    );\n    const pushSubscription = await prisma.pushSubscription.findFirst({\n      where: { userId }\n    });\n    try {\n      if (pushSubscription) {\n        const subscription = {\n          endpoint: pushSubscription.endpoint,\n          keys: {\n            p256dh: pushSubscription.p256dh,\n            auth: pushSubscription.auth\n          }\n        };\n        await fetch(\"/api/push/send\", {\n          method: \"POST\",\n          headers: { \"Content-Type\": \"application/json\" },\n          body: JSON.stringify({\n            subscription,\n            payload: JSON.stringify({\n              title: data.title,\n              message: data.message,\n              url: data.url,\n              data: data.metadata\n            })\n          })\n        });\n      }\n    } catch (pushError) {\n      console.error(\"Error sending push notification:\", pushError);\n    }\n    return true;\n  } catch (error) {\n    console.error(\"Error sending notification to user:\", error);\n    return false;\n  }\n}\nasync function sendGlobalNotification(data) {\n  try {\n    const notificationData = {\n      title: data.title,\n      message: data.message,\n      url: data.url,\n      type: data.type || \"system\",\n      priority: data.priority || \"medium\",\n      metadata: data.metadata ? JSON.stringify(data.metadata) : null,\n      expiresAt: data.expiresAt,\n      global: true\n    };\n    let notification;\n    try {\n      notification = await prisma.notification.create({\n        data: notificationData\n      });\n      console.log(`Created global notification in database with ID ${notification.id}`);\n    } catch (error) {\n      console.error(\"Error creating global notification:\", error);\n      throw error;\n    }\n    const redis = await getRedisClient();\n    if (!redis) {\n      console.error(\"Redis client not available\");\n      return false;\n    }\n    const requestId = `notification:${notification.id}:${Date.now()}`;\n    console.log(`Generated request ID: ${requestId} for global notification ${notification.id}`);\n    console.log(\n      `Publishing global notification ${notification.id} to Redis channel global:notifications with request ID ${requestId}`\n    );\n    await redis.publish(\n      \"global:notifications\",\n      JSON.stringify({\n        id: notification.id,\n        title: notification.title,\n        message: notification.message,\n        url: notification.url,\n        type: notification.type,\n        timestamp: notification.createdAt.toISOString(),\n        global: true,\n        requestId\n        // Include the request ID in the notification\n      })\n    );\n    return true;\n  } catch (error) {\n    console.error(\"Error sending global notification:\", error);\n    return false;\n  }\n}\nasync function sendJobNotification(userId, data) {\n  try {\n    const preferences = await prisma.notificationSettings.findUnique({\n      where: { userId }\n    });\n    if (preferences && !preferences.jobMatchEnabled) {\n      console.log(`User ${userId} has disabled job alerts`);\n      return false;\n    }\n    const jobData = {\n      ...data,\n      type: \"job\"\n      /* JOB */\n    };\n    return await sendNotificationToUser(userId, jobData);\n  } catch (error) {\n    console.error(\"Error sending job notification:\", error);\n    return false;\n  }\n}\nasync function markNotificationAsRead(notificationId) {\n  try {\n    const notification = await prisma.notification.update({\n      where: { id: notificationId },\n      data: { read: true }\n    });\n    const requestId = `notification_read:${notificationId}:${Date.now()}`;\n    console.log(\n      `Generated request ID: ${requestId} for marking notification ${notificationId} as read`\n    );\n    const redis = await getRedisClient();\n    if (redis && notification.userId) {\n      console.log(\n        `Publishing read status update for notification ${notificationId} to Redis channel user:${notification.userId}:notifications with request ID ${requestId}`\n      );\n      await redis.publish(\n        `user:${notification.userId}:notifications`,\n        JSON.stringify({\n          type: \"notification_read\",\n          id: notificationId,\n          timestamp: (/* @__PURE__ */ new Date()).toISOString(),\n          requestId\n          // Include the request ID\n        })\n      );\n    }\n    return true;\n  } catch (error) {\n    console.error(\"Error marking notification as read:\", error);\n    return false;\n  }\n}\nasync function deleteNotification(notificationId) {\n  try {\n    await prisma.notification.delete({\n      where: { id: notificationId }\n    });\n    return true;\n  } catch (error) {\n    console.error(\"Error deleting notification:\", error);\n    return false;\n  }\n}\nasync function getUserNotifications(userId, options = {}) {\n  const { limit = 50, offset = 0, includeRead = false, type } = options;\n  try {\n    const whereClause = {\n      OR: [{ userId }, { global: true }]\n    };\n    if (type) {\n      whereClause.type = type;\n    }\n    if (!includeRead) {\n      whereClause.read = false;\n    }\n    let notifications = [];\n    try {\n      notifications = await prisma.notification.findMany({\n        where: whereClause,\n        orderBy: { createdAt: \"desc\" },\n        take: limit,\n        skip: offset\n      });\n      console.log(`Found ${notifications.length} notifications in database`);\n      if (notifications.length > 0) {\n        console.log(\"First notification:\", JSON.stringify(notifications[0]));\n      }\n    } catch (error) {\n      console.error(\"Error getting user notifications from database:\", error);\n      return [];\n    }\n    return notifications.map((notification) => ({\n      id: notification.id,\n      title: notification.title,\n      message: notification.message,\n      url: notification.url,\n      type: notification.type,\n      read: notification.read,\n      global: notification.global,\n      createdAt: notification.createdAt,\n      timestamp: notification.createdAt\n      // Add timestamp for backward compatibility\n    }));\n  } catch (error) {\n    console.error(\"Error getting user notifications:\", error);\n    return [];\n  }\n}\nexport {\n  NotificationPriority as N,\n  NotificationType as a,\n  sendJobNotification as b,\n  sendNotificationToUser as c,\n  deleteNotification as d,\n  getUserNotifications as g,\n  markNotificationAsRead as m,\n  sendGlobalNotification as s\n};\n"], "names": [], "mappings": ";;;AAEG,IAAC,gBAAgB,mBAAmB,CAAC,CAAC,iBAAiB,KAAK;AAC/D,EAAE,iBAAiB,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACxC,EAAE,iBAAiB,CAAC,KAAK,CAAC,GAAG,KAAK;AAClC,EAAE,iBAAiB,CAAC,aAAa,CAAC,GAAG,aAAa;AAClD,EAAE,iBAAiB,CAAC,WAAW,CAAC,GAAG,WAAW;AAC9C,EAAE,iBAAiB,CAAC,SAAS,CAAC,GAAG,SAAS;AAC1C,EAAE,iBAAiB,CAAC,OAAO,CAAC,GAAG,OAAO;AACtC,EAAE,iBAAiB,CAAC,SAAS,CAAC,GAAG,SAAS;AAC1C,EAAE,iBAAiB,CAAC,SAAS,CAAC,GAAG,SAAS;AAC1C,EAAE,iBAAiB,CAAC,MAAM,CAAC,GAAG,MAAM;AACpC,EAAE,OAAO,iBAAiB;AAC1B,CAAC,EAAE,gBAAgB,IAAI,EAAE;AACtB,IAAC,oBAAoB,mBAAmB,CAAC,CAAC,qBAAqB,KAAK;AACvE,EAAE,qBAAqB,CAAC,KAAK,CAAC,GAAG,KAAK;AACtC,EAAE,qBAAqB,CAAC,QAAQ,CAAC,GAAG,QAAQ;AAC5C,EAAE,qBAAqB,CAAC,MAAM,CAAC,GAAG,MAAM;AACxC,EAAE,qBAAqB,CAAC,QAAQ,CAAC,GAAG,QAAQ;AAC5C,EAAE,OAAO,qBAAqB;AAC9B,CAAC,EAAE,oBAAoB,IAAI,EAAE;AAC7B,eAAe,sBAAsB,CAAC,MAAM,EAAE,IAAI,EAAE;AACpD,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,oBAAoB,CAAC,UAAU,CAAC;AACrE,MAAM,KAAK,EAAE,EAAE,MAAM;AACrB,KAAK,CAAC;AACN,IAAI,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE;AACpD,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,kCAAkC,CAAC,CAAC;AACrE,MAAM,OAAO,KAAK;AAClB;AACA,IAAI,MAAM,gBAAgB,GAAG;AAC7B,MAAM,MAAM;AACZ,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK;AACvB,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;AAC3B,MAAM,GAAG,EAAE,IAAI,CAAC,GAAG;AACnB,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,MAAM;AAC/B,MAAM,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,QAAQ;AACzC,MAAM,QAAQ,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI;AACpE,MAAM,SAAS,EAAE,IAAI,CAAC,SAAS;AAC/B,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI,IAAI,YAAY;AACpB,IAAI,IAAI;AACR,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AACtD,QAAQ,IAAI,EAAE;AACd,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,yCAAyC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;AAChF,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AAC1D,MAAM,MAAM,KAAK;AACjB;AACA,IAAI,MAAM,KAAK,GAAG,MAAM,cAAc,EAAE;AACxC,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC;AACjD,MAAM,OAAO,KAAK;AAClB;AACA,IAAI,MAAM,SAAS,GAAG,CAAC,aAAa,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AACrE,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,sBAAsB,EAAE,SAAS,CAAC,kBAAkB,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;AACzF,IAAI,OAAO,CAAC,GAAG;AACf,MAAM,CAAC,wBAAwB,EAAE,YAAY,CAAC,EAAE,CAAC,uBAAuB,EAAE,MAAM,CAAC,+BAA+B,EAAE,SAAS,CAAC;AAC5H,KAAK;AACL,IAAI,MAAM,KAAK,CAAC,OAAO;AACvB,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,cAAc,CAAC;AACpC,MAAM,IAAI,CAAC,SAAS,CAAC;AACrB,QAAQ,EAAE,EAAE,YAAY,CAAC,EAAE;AAC3B,QAAQ,KAAK,EAAE,YAAY,CAAC,KAAK;AACjC,QAAQ,OAAO,EAAE,YAAY,CAAC,OAAO;AACrC,QAAQ,GAAG,EAAE,YAAY,CAAC,GAAG;AAC7B,QAAQ,IAAI,EAAE,YAAY,CAAC,IAAI;AAC/B,QAAQ,SAAS,EAAE,YAAY,CAAC,SAAS,CAAC,WAAW,EAAE;AACvD,QAAQ;AACR;AACA,OAAO;AACP,KAAK;AACL,IAAI,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC;AACrE,MAAM,KAAK,EAAE,EAAE,MAAM;AACrB,KAAK,CAAC;AACN,IAAI,IAAI;AACR,MAAM,IAAI,gBAAgB,EAAE;AAC5B,QAAQ,MAAM,YAAY,GAAG;AAC7B,UAAU,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;AAC7C,UAAU,IAAI,EAAE;AAChB,YAAY,MAAM,EAAE,gBAAgB,CAAC,MAAM;AAC3C,YAAY,IAAI,EAAE,gBAAgB,CAAC;AACnC;AACA,SAAS;AACT,QAAQ,MAAM,KAAK,CAAC,gBAAgB,EAAE;AACtC,UAAU,MAAM,EAAE,MAAM;AACxB,UAAU,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;AACzD,UAAU,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;AAC/B,YAAY,YAAY;AACxB,YAAY,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;AACpC,cAAc,KAAK,EAAE,IAAI,CAAC,KAAK;AAC/B,cAAc,OAAO,EAAE,IAAI,CAAC,OAAO;AACnC,cAAc,GAAG,EAAE,IAAI,CAAC,GAAG;AAC3B,cAAc,IAAI,EAAE,IAAI,CAAC;AACzB,aAAa;AACb,WAAW;AACX,SAAS,CAAC;AACV;AACA,KAAK,CAAC,OAAO,SAAS,EAAE;AACxB,MAAM,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,SAAS,CAAC;AAClE;AACA,IAAI,OAAO,IAAI;AACf,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC;AAC/D,IAAI,OAAO,KAAK;AAChB;AACA;AACA,eAAe,sBAAsB,CAAC,IAAI,EAAE;AAC5C,EAAE,IAAI;AACN,IAAI,MAAM,gBAAgB,GAAG;AAC7B,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK;AACvB,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;AAC3B,MAAM,GAAG,EAAE,IAAI,CAAC,GAAG;AACnB,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,QAAQ;AACjC,MAAM,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,QAAQ;AACzC,MAAM,QAAQ,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI;AACpE,MAAM,SAAS,EAAE,IAAI,CAAC,SAAS;AAC/B,MAAM,MAAM,EAAE;AACd,KAAK;AACL,IAAI,IAAI,YAAY;AACpB,IAAI,IAAI;AACR,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AACtD,QAAQ,IAAI,EAAE;AACd,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,gDAAgD,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;AACvF,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC;AACjE,MAAM,MAAM,KAAK;AACjB;AACA,IAAI,MAAM,KAAK,GAAG,MAAM,cAAc,EAAE;AACxC,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC;AACjD,MAAM,OAAO,KAAK;AAClB;AACA,IAAI,MAAM,SAAS,GAAG,CAAC,aAAa,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AACrE,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,sBAAsB,EAAE,SAAS,CAAC,yBAAyB,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;AAChG,IAAI,OAAO,CAAC,GAAG;AACf,MAAM,CAAC,+BAA+B,EAAE,YAAY,CAAC,EAAE,CAAC,uDAAuD,EAAE,SAAS,CAAC;AAC3H,KAAK;AACL,IAAI,MAAM,KAAK,CAAC,OAAO;AACvB,MAAM,sBAAsB;AAC5B,MAAM,IAAI,CAAC,SAAS,CAAC;AACrB,QAAQ,EAAE,EAAE,YAAY,CAAC,EAAE;AAC3B,QAAQ,KAAK,EAAE,YAAY,CAAC,KAAK;AACjC,QAAQ,OAAO,EAAE,YAAY,CAAC,OAAO;AACrC,QAAQ,GAAG,EAAE,YAAY,CAAC,GAAG;AAC7B,QAAQ,IAAI,EAAE,YAAY,CAAC,IAAI;AAC/B,QAAQ,SAAS,EAAE,YAAY,CAAC,SAAS,CAAC,WAAW,EAAE;AACvD,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ;AACR;AACA,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI;AACf,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC;AAC9D,IAAI,OAAO,KAAK;AAChB;AACA;AACA,eAAe,mBAAmB,CAAC,MAAM,EAAE,IAAI,EAAE;AACjD,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,oBAAoB,CAAC,UAAU,CAAC;AACrE,MAAM,KAAK,EAAE,EAAE,MAAM;AACrB,KAAK,CAAC;AACN,IAAI,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE;AACrD,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAC3D,MAAM,OAAO,KAAK;AAClB;AACA,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,GAAG,IAAI;AACb,MAAM,IAAI,EAAE;AACZ;AACA,KAAK;AACL,IAAI,OAAO,MAAM,sBAAsB,CAAC,MAAM,EAAE,OAAO,CAAC;AACxD,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC3D,IAAI,OAAO,KAAK;AAChB;AACA;AACA,eAAe,sBAAsB,CAAC,cAAc,EAAE;AACtD,EAAE,IAAI;AACN,IAAI,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AAC1D,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE;AACnC,MAAM,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI;AACxB,KAAK,CAAC;AACN,IAAI,MAAM,SAAS,GAAG,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AACzE,IAAI,OAAO,CAAC,GAAG;AACf,MAAM,CAAC,sBAAsB,EAAE,SAAS,CAAC,0BAA0B,EAAE,cAAc,CAAC,QAAQ;AAC5F,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,MAAM,cAAc,EAAE;AACxC,IAAI,IAAI,KAAK,IAAI,YAAY,CAAC,MAAM,EAAE;AACtC,MAAM,OAAO,CAAC,GAAG;AACjB,QAAQ,CAAC,+CAA+C,EAAE,cAAc,CAAC,uBAAuB,EAAE,YAAY,CAAC,MAAM,CAAC,+BAA+B,EAAE,SAAS,CAAC;AACjK,OAAO;AACP,MAAM,MAAM,KAAK,CAAC,OAAO;AACzB,QAAQ,CAAC,KAAK,EAAE,YAAY,CAAC,MAAM,CAAC,cAAc,CAAC;AACnD,QAAQ,IAAI,CAAC,SAAS,CAAC;AACvB,UAAU,IAAI,EAAE,mBAAmB;AACnC,UAAU,EAAE,EAAE,cAAc;AAC5B,UAAU,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE;AAC/D,UAAU;AACV;AACA,SAAS;AACT,OAAO;AACP;AACA,IAAI,OAAO,IAAI;AACf,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC;AAC/D,IAAI,OAAO,KAAK;AAChB;AACA;AACA,eAAe,kBAAkB,CAAC,cAAc,EAAE;AAClD,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AACrC,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,cAAc;AACjC,KAAK,CAAC;AACN,IAAI,OAAO,IAAI;AACf,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACxD,IAAI,OAAO,KAAK;AAChB;AACA;AACA,eAAe,oBAAoB,CAAC,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE;AAC1D,EAAE,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,GAAG,CAAC,EAAE,WAAW,GAAG,KAAK,EAAE,IAAI,EAAE,GAAG,OAAO;AACvE,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG;AACxB,MAAM,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;AACvC,KAAK;AACL,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,WAAW,CAAC,IAAI,GAAG,IAAI;AAC7B;AACA,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,WAAW,CAAC,IAAI,GAAG,KAAK;AAC9B;AACA,IAAI,IAAI,aAAa,GAAG,EAAE;AAC1B,IAAI,IAAI;AACR,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;AACzD,QAAQ,KAAK,EAAE,WAAW;AAC1B,QAAQ,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;AACtC,QAAQ,IAAI,EAAE,KAAK;AACnB,QAAQ,IAAI,EAAE;AACd,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,aAAa,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC;AAC5E,MAAM,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AACpC,QAAQ,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5E;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,iDAAiD,EAAE,KAAK,CAAC;AAC7E,MAAM,OAAO,EAAE;AACf;AACA,IAAI,OAAO,aAAa,CAAC,GAAG,CAAC,CAAC,YAAY,MAAM;AAChD,MAAM,EAAE,EAAE,YAAY,CAAC,EAAE;AACzB,MAAM,KAAK,EAAE,YAAY,CAAC,KAAK;AAC/B,MAAM,OAAO,EAAE,YAAY,CAAC,OAAO;AACnC,MAAM,GAAG,EAAE,YAAY,CAAC,GAAG;AAC3B,MAAM,IAAI,EAAE,YAAY,CAAC,IAAI;AAC7B,MAAM,IAAI,EAAE,YAAY,CAAC,IAAI;AAC7B,MAAM,MAAM,EAAE,YAAY,CAAC,MAAM;AACjC,MAAM,SAAS,EAAE,YAAY,CAAC,SAAS;AACvC,MAAM,SAAS,EAAE,YAAY,CAAC;AAC9B;AACA,KAAK,CAAC,CAAC;AACP,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC;AAC7D,IAAI,OAAO,EAAE;AACb;AACA;;;;"}