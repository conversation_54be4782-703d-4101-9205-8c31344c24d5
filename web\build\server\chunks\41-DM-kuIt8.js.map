{"version": 3, "file": "41-DM-kuIt8.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/resumes/_id_/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/41.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport \"../../../../../chunks/auth.js\";\nimport { r as redirect } from \"../../../../../chunks/index.js\";\nconst load = async ({ params, locals }) => {\n  const user = locals.user;\n  if (!user) throw redirect(302, \"/auth/sign-in\");\n  const resume = await prisma.resume.findFirst({\n    where: {\n      id: params.id,\n      OR: [\n        { profile: { userId: user.id } },\n        {\n          profile: {\n            team: {\n              members: { some: { userId: user.id } }\n            }\n          }\n        }\n      ]\n    },\n    include: {\n      profile: true,\n      document: true,\n      // Include the document to get the fileUrl\n      optimizationResult: true,\n      JobSearch: {\n        take: 1,\n        orderBy: { createdAt: \"desc\" },\n        include: {\n          _count: { select: { results: true } }\n        }\n      }\n    }\n  });\n  if (!resume) throw redirect(302, \"/dashboard/resumes\");\n  return {\n    resume: {\n      id: resume.id,\n      name: resume.document?.label ?? \"\",\n      fileName: resume.document?.fileName ?? resume.document?.fileUrl?.split(\"/\").pop() ?? \"\",\n      fileUrl: resume.document?.fileUrl ?? \"\",\n      createdAt: resume.createdAt,\n      score: resume.optimizationResult?.score ?? null,\n      profile: {\n        name: resume.profile?.name ?? \"Unknown\"\n      },\n      jobSearch: resume.JobSearch[0] ?? null\n    }\n  };\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/dashboard/resumes/_id_/_page.server.ts.js';\n\nexport const index = 41;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/resumes/_id_/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/resumes/[id]/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/41.s_vSGjEv.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/Cdn-N1RY.js\",\"_app/immutable/chunks/GwmmX_iF.js\",\"_app/immutable/chunks/D50jIuLr.js\",\"_app/immutable/chunks/DQC1iFs0.js\",\"_app/immutable/chunks/I7hvcB12.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/OXTnUuEm.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/C88uNE8B.js\",\"_app/immutable/chunks/DmZyh-PW.js\"];\nexport const stylesheets = [];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;AAGA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC3C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACjD,EAAE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC;AAC/C,IAAI,KAAK,EAAE;AACX,MAAM,EAAE,EAAE,MAAM,CAAC,EAAE;AACnB,MAAM,EAAE,EAAE;AACV,QAAQ,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE;AACxC,QAAQ;AACR,UAAU,OAAO,EAAE;AACnB,YAAY,IAAI,EAAE;AAClB,cAAc,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;AAClD;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,QAAQ,EAAE,IAAI;AACpB;AACA,MAAM,kBAAkB,EAAE,IAAI;AAC9B,MAAM,SAAS,EAAE;AACjB,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;AACtC,QAAQ,OAAO,EAAE;AACjB,UAAU,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;AAC7C;AACA;AACA;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,QAAQ,CAAC,GAAG,EAAE,oBAAoB,CAAC;AACxD,EAAE,OAAO;AACT,IAAI,MAAM,EAAE;AACZ,MAAM,EAAE,EAAE,MAAM,CAAC,EAAE;AACnB,MAAM,IAAI,EAAE,MAAM,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;AACxC,MAAM,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,QAAQ,IAAI,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE;AAC7F,MAAM,OAAO,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE;AAC7C,MAAM,SAAS,EAAE,MAAM,CAAC,SAAS;AACjC,MAAM,KAAK,EAAE,MAAM,CAAC,kBAAkB,EAAE,KAAK,IAAI,IAAI;AACrD,MAAM,OAAO,EAAE;AACf,QAAQ,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE,IAAI,IAAI;AACtC,OAAO;AACP,MAAM,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI;AACxC;AACA,GAAG;AACH,CAAC;;;;;;;AC/CW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAyD,CAAC,EAAE;AAEvH,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACzvC,MAAC,WAAW,GAAG;AACf,MAAC,KAAK,GAAG;;;;"}