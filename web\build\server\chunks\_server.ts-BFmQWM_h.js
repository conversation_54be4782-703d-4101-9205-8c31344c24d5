import { j as json } from './index-Ddp2AB5f.js';
import { g as getRedisClient } from './redis-DxlM1ibh.js';
import 'ioredis';

const POST = async ({ request }) => {
  try {
    const data = await request.json();
    const redis = await getRedisClient();
    if (!redis) {
      return json({ error: "Redis client not available" }, { status: 500 });
    }
    if (!data.timestamp) {
      data.timestamp = (/* @__PURE__ */ new Date()).toISOString();
    }
    await redis.publish("websocket::broadcast", JSON.stringify(data));
    return json({ success: true, message: "WebSocket message sent" });
  } catch (error) {
    console.error("Error sending WebSocket message:", error);
    return json({ error: "Failed to send WebSocket message" }, { status: 500 });
  }
};
const GET = async () => {
  return json({
    status: "WebSocket server is running",
    timestamp: (/* @__PURE__ */ new Date()).toISOString()
  });
};

export { GET, POST };
//# sourceMappingURL=_server.ts-BFmQWM_h.js.map
