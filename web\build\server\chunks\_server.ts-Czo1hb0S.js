let lastLogTime = 0;
const LOG_INTERVAL = 864e5;
function GET() {
  const now = Date.now();
  if (now - lastLogTime > LOG_INTERVAL) {
    console.log("Health check endpoint accessed at:", (/* @__PURE__ */ new Date()).toISOString());
    lastLogTime = now;
  }
  return new Response("OK", {
    status: 200,
    headers: {
      "Content-Type": "text/plain",
      // Add cache headers to reduce frequency of checks
      "Cache-Control": "public, max-age=3600"
      // Cache for 1 hour
    }
  });
}

export { GET };
//# sourceMappingURL=_server.ts-Czo1hb0S.js.map
