import { p as push, V as copy_payload, W as assign_payload, Q as bind_props, q as pop, R as spread_props, Z as spread_attributes } from './index3-CqUPEnZw.js';
import { L as Label$1 } from './label-Dt8gTF_8.js';
import { c as cn } from './utils-pWl1tgmi.js';
import { u as useId, d as useLabel, c as box, m as mergeProps } from './index15-D3NL0C7o.js';
import './index-DAbaXdpL.js';

function Label($$payload, $$props) {
  push();
  let {
    id = useId(),
    ref = null,
    children,
    child,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const labelState = useLabel({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, labelState.props);
  if (child) {
    $$payload.out += "<!--[-->";
    child($$payload, { props: mergedProps });
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<label${spread_attributes({ ...mergedProps }, null)}>`;
    children?.($$payload);
    $$payload.out += `<!----></label>`;
  }
  $$payload.out += `<!--]-->`;
  bind_props($$props, { ref });
  pop();
}
function Form_label($$payload, $$props) {
  push();
  let {
    ref = null,
    children,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    {
      let child = function($$payload3, { props }) {
        Label$1($$payload3, spread_props([
          props,
          {
            "data-slot": "form-label",
            class: cn("data-[fs-error]:text-destructive", className),
            children: ($$payload4) => {
              children?.($$payload4);
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          }
        ]));
      };
      Label($$payload2, spread_props([
        restProps,
        {
          get ref() {
            return ref;
          },
          set ref($$value) {
            ref = $$value;
            $$settled = false;
          },
          child,
          $$slots: { child: true }
        }
      ]));
    }
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}

export { Form_label as F };
//# sourceMappingURL=form-label-C5yTxnxS.js.map
