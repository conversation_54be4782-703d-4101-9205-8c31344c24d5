import { p as push, Z as spread_attributes, _ as clsx, Q as bind_props, q as pop } from './index3-CqUPEnZw.js';
import { c as cn } from './utils-pWl1tgmi.js';

function Card_description($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out += `<p${spread_attributes(
    {
      "data-slot": "card-description",
      class: clsx(cn("text-muted-foreground text-sm", className)),
      ...restProps
    },
    null
  )}>`;
  children?.($$payload);
  $$payload.out += `<!----></p>`;
  bind_props($$props, { ref });
  pop();
}

export { Card_description as C };
//# sourceMappingURL=card-description-CMuO6f9m.js.map
