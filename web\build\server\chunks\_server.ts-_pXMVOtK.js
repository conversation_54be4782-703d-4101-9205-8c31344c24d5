import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const studioPath = path.resolve(__dirname, "../../../static/studio");
async function GET({ params, url }) {
  let filePath = params.path || "index.html";
  filePath = filePath.replace(/\.\.\//g, "");
  const fullPath = path.join(studioPath, filePath);
  try {
    if (!fs.existsSync(fullPath)) {
      return new Response(fs.readFileSync(path.join(studioPath, "index.html")), {
        headers: {
          "Content-Type": "text/html"
        }
      });
    }
    const ext = path.extname(fullPath).toLowerCase();
    let contentType = "text/plain";
    switch (ext) {
      case ".html":
        contentType = "text/html";
        break;
      case ".js":
        contentType = "application/javascript";
        break;
      case ".css":
        contentType = "text/css";
        break;
      case ".json":
        contentType = "application/json";
        break;
      case ".png":
        contentType = "image/png";
        break;
      case ".jpg":
      case ".jpeg":
        contentType = "image/jpeg";
        break;
      case ".svg":
        contentType = "image/svg+xml";
        break;
      case ".ico":
        contentType = "image/x-icon";
        break;
    }
    return new Response(fs.readFileSync(fullPath), {
      headers: {
        "Content-Type": contentType
      }
    });
  } catch (error) {
    console.error("Error serving Sanity Studio file:", error);
    return new Response("Not Found", { status: 404 });
  }
}

export { GET };
//# sourceMappingURL=_server.ts-_pXMVOtK.js.map
