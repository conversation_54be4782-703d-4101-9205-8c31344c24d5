import { j as json } from './index-Ddp2AB5f.js';

const GET = async ({ request }) => {
  console.log("WebSocket status endpoint accessed");
  const isDev = process.env.NODE_ENV === "development";
  const wsHost = isDev ? "localhost:3000" : request.headers.get("host");
  const wsProtocol = request.headers.get("x-forwarded-proto") === "https" ? "wss" : "ws";
  return json({
    status: "ok",
    message: "WebSocket server is running",
    endpoint: "/ws",
    wsUrl: `${wsProtocol}://${wsHost}/ws`,
    environment: isDev ? "development" : "production",
    instructions: "Connect to the WebSocket endpoint with a WebSocket client, not this HTTP endpoint",
    timestamp: (/* @__PURE__ */ new Date()).toISOString()
  });
};

export { GET };
//# sourceMappingURL=_server.ts-CLZSFM5c.js.map
