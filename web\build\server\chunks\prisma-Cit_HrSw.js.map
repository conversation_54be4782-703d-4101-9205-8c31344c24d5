{"version": 3, "file": "prisma-Cit_HrSw.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/prisma.js"], "sourcesContent": ["import { PrismaClient } from \"@prisma/client\";\nconst isBuildMode = process.env.NODE_ENV === \"production\" && true && true;\nconst externalDbUrl = process.env.DATABASE_URL_EXTERNAL;\nconst createPrismaClient = () => {\n  if (isBuildMode) {\n    console.log(\"Skipping Prisma initialization during build\");\n    return null;\n  }\n  return new PrismaClient({\n    datasources: {\n      db: {\n        url: process.env.DATABASE_URL || externalDbUrl\n      }\n    },\n    log: [\"error\", \"warn\"]\n  });\n};\nconst prisma = createPrismaClient();\nfunction getPrismaClient() {\n  if (isBuildMode) {\n    console.log(\"Prisma client not initialized during build\");\n    return null;\n  }\n  return prisma;\n}\nexport {\n  getPrismaClient as g,\n  prisma as p\n};\n"], "names": [], "mappings": ";;AACA,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,IAAI,IAAI,IAAI,IAAI;AACzE,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB;AACvD,MAAM,kBAAkB,GAAG,MAAM;AACjC,EAAE,IAAI,WAAW,EAAE;AACnB,IAAI,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC;AAC9D,IAAI,OAAO,IAAI;AACf;AACA,EAAE,OAAO,IAAI,YAAY,CAAC;AAC1B,IAAI,WAAW,EAAE;AACjB,MAAM,EAAE,EAAE;AACV,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI;AACzC;AACA,KAAK;AACL,IAAI,GAAG,EAAE,CAAC,OAAO,EAAE,MAAM;AACzB,GAAG,CAAC;AACJ,CAAC;AACI,MAAC,MAAM,GAAG,kBAAkB;AACjC,SAAS,eAAe,GAAG;AAC3B,EAAE,IAAI,WAAW,EAAE;AACnB,IAAI,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC;AAC7D,IAAI,OAAO,IAAI;AACf;AACA,EAAE,OAAO,MAAM;AACf;;;;"}