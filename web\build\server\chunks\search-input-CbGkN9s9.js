import { p as push, N as attr, J as attr_class, _ as clsx, Q as bind_props, q as pop } from './index3-CqUPEnZw.js';
import { o as onDestroy } from './index-server-CezSOnuG.js';
import { c as cn } from './utils-pWl1tgmi.js';
import { S as Search } from './search-B0oHlTPS.js';

function Search_input($$payload, $$props) {
  push();
  let {
    value = "",
    placeholder = "Search...",
    className = "",
    paramName = "title",
    onSearch = void 0,
    disabled = false,
    iconPosition = "left",
    iconClass = "",
    inputClass = "",
    autofocus = false
  } = $$props;
  onDestroy(() => {
  });
  $$payload.out += `<div class="w-full"${attr("data-param-name", paramName)}><div class="relative">`;
  if (iconPosition === "left") {
    $$payload.out += "<!--[-->";
    Search($$payload, {
      class: cn("text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2", iconClass)
    });
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div><input type="text"${attr("placeholder", placeholder)}${attr("value", value)}${attr("disabled", disabled, true)}${attr_class(clsx(cn("border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm", className, iconPosition === "left" ? "pl-9" : "", iconPosition === "right" ? "pr-9" : "", inputClass)))}${attr("data-search-input", paramName)}/></div> `;
  if (iconPosition === "right") {
    $$payload.out += "<!--[-->";
    Search($$payload, {
      class: cn("text-muted-foreground absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2", iconClass)
    });
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div>`;
  bind_props($$props, { value });
  pop();
}

export { Search_input as S };
//# sourceMappingURL=search-input-CbGkN9s9.js.map
