import { p as push, Z as spread_attributes, _ as clsx, Q as bind_props, q as pop } from './index3-CqUPEnZw.js';
import { c as cn } from './utils-pWl1tgmi.js';

function Sheet_footer($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out += `<div${spread_attributes(
    {
      "data-slot": "sheet-footer",
      class: clsx(cn("mt-auto flex flex-col gap-2 p-4", className)),
      ...restProps
    },
    null
  )}>`;
  children?.($$payload);
  $$payload.out += `<!----></div>`;
  bind_props($$props, { ref });
  pop();
}

export { Sheet_footer as S };
//# sourceMappingURL=sheet-footer-B80ycEhL.js.map
