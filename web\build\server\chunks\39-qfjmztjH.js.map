{"version": 3, "file": "39-qfjmztjH.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/notifications/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/39.js"], "sourcesContent": ["import { r as redirect } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nconst load = async ({ locals, url }) => {\n  const user = locals.user;\n  if (!user) {\n    throw redirect(302, \"/auth/sign-in\");\n  }\n  const page = parseInt(url.searchParams.get(\"page\") || \"1\");\n  const limit = parseInt(url.searchParams.get(\"limit\") || \"20\");\n  const type = url.searchParams.get(\"type\") || void 0;\n  const includeRead = url.searchParams.get(\"includeRead\") === \"true\";\n  const offset = (page - 1) * limit;\n  try {\n    const whereClause = {\n      OR: [{ userId: user.id }, { global: true }]\n    };\n    if (!includeRead) {\n      whereClause.read = false;\n    }\n    if (type) {\n      whereClause.type = type;\n    }\n    const notifications = await prisma.Notification.findMany({\n      where: whereClause,\n      orderBy: { createdAt: \"desc\" },\n      take: limit,\n      skip: offset\n    });\n    const totalCount = await prisma.Notification.count({\n      where: whereClause\n    });\n    const preferences = await prisma.notificationSettings.findUnique({\n      where: { userId: user.id }\n    });\n    const userPreferences = preferences || await prisma.notificationSettings.create({\n      data: {\n        userId: user.id,\n        emailEnabled: true,\n        pushEnabled: true,\n        browserEnabled: true,\n        jobMatchEnabled: true,\n        applicationStatusEnabled: true,\n        marketingEnabled: false,\n        productUpdatesEnabled: true\n      }\n    });\n    const unreadCount = await prisma.Notification.count({\n      where: {\n        OR: [{ userId: user.id }, { global: true }],\n        read: false\n      }\n    });\n    return {\n      notifications,\n      pagination: {\n        page,\n        limit,\n        totalCount,\n        totalPages: Math.ceil(totalCount / limit)\n      },\n      filters: {\n        type,\n        includeRead\n      },\n      preferences: userPreferences,\n      unreadCount\n    };\n  } catch (error) {\n    console.error(\"Error loading notifications:\", error);\n    return {\n      notifications: [],\n      pagination: {\n        page: 1,\n        limit,\n        totalCount: 0,\n        totalPages: 0\n      },\n      filters: {\n        type,\n        includeRead\n      },\n      preferences: {\n        email: true,\n        push: true,\n        inApp: true,\n        jobAlerts: true,\n        applicationUpdates: true,\n        marketingUpdates: false,\n        systemAnnouncements: true\n      },\n      unreadCount: 0,\n      error: \"Failed to load notifications\"\n    };\n  }\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/dashboard/notifications/_page.server.ts.js';\n\nexport const index = 39;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/notifications/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/notifications/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/39.CQt5EkTL.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/Cdn-N1RY.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/D9yI7a4E.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/BjCTmJLi.js\",\"_app/immutable/chunks/CzsE_FAw.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/BvvicRXk.js\",\"_app/immutable/chunks/DaBofrVv.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/DMTMHyMa.js\",\"_app/immutable/chunks/DjPYYl4Z.js\",\"_app/immutable/chunks/DQC1iFs0.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/xCOJ4D9d.js\",\"_app/immutable/chunks/Dc4vaUpe.js\",\"_app/immutable/chunks/26EXiO5K.js\",\"_app/immutable/chunks/C2MdR6K0.js\",\"_app/immutable/chunks/hQ6uUXJy.js\",\"_app/immutable/chunks/Cb-3cdbh.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/CGK0g3x_.js\",\"_app/immutable/chunks/D2egQzE8.js\",\"_app/immutable/chunks/Ntteq2n_.js\",\"_app/immutable/chunks/DrQfh6BY.js\",\"_app/immutable/chunks/DxW95yuQ.js\",\"_app/immutable/chunks/D-o7ybA5.js\",\"_app/immutable/chunks/BaVT73bJ.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/yW0TxTga.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/BoNCRmBc.js\",\"_app/immutable/chunks/qwsZpUIl.js\",\"_app/immutable/chunks/DW7T7T22.js\",\"_app/immutable/chunks/CnpHcmx3.js\",\"_app/immutable/chunks/B2lQHLf_.js\",\"_app/immutable/chunks/hA0h0kTo.js\",\"_app/immutable/chunks/C33xR25f.js\",\"_app/immutable/chunks/BBNNmnYR.js\",\"_app/immutable/chunks/DkmCSZhC.js\",\"_app/immutable/chunks/BuYRPDDz.js\",\"_app/immutable/chunks/QtAhPN2H.js\",\"_app/immutable/chunks/CTO_B1Jk.js\",\"_app/immutable/chunks/CDnvByek.js\"];\nexport const stylesheets = [\"_app/immutable/assets/Toaster.DKF17Rty.css\",\"_app/immutable/assets/scroll-area.bHHIbcsu.css\",\"_app/immutable/assets/index.CV-KWLNP.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;AAEA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK;AACxC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACxC;AACA,EAAE,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC;AAC5D,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;AAC/D,EAAE,MAAM,IAAI,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,MAAM;AACrD,EAAE,MAAM,WAAW,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,MAAM;AACpE,EAAE,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK;AACnC,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG;AACxB,MAAM,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;AAChD,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,WAAW,CAAC,IAAI,GAAG,KAAK;AAC9B;AACA,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,WAAW,CAAC,IAAI,GAAG,IAAI;AAC7B;AACA,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;AAC7D,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;AACpC,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;AACN,IAAI,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC;AACvD,MAAM,KAAK,EAAE;AACb,KAAK,CAAC;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,oBAAoB,CAAC,UAAU,CAAC;AACrE,MAAM,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE;AAC9B,KAAK,CAAC;AACN,IAAI,MAAM,eAAe,GAAG,WAAW,IAAI,MAAM,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;AACpF,MAAM,IAAI,EAAE;AACZ,QAAQ,MAAM,EAAE,IAAI,CAAC,EAAE;AACvB,QAAQ,YAAY,EAAE,IAAI;AAC1B,QAAQ,WAAW,EAAE,IAAI;AACzB,QAAQ,cAAc,EAAE,IAAI;AAC5B,QAAQ,eAAe,EAAE,IAAI;AAC7B,QAAQ,wBAAwB,EAAE,IAAI;AACtC,QAAQ,gBAAgB,EAAE,KAAK;AAC/B,QAAQ,qBAAqB,EAAE;AAC/B;AACA,KAAK,CAAC;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC;AACxD,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AACnD,QAAQ,IAAI,EAAE;AACd;AACA,KAAK,CAAC;AACN,IAAI,OAAO;AACX,MAAM,aAAa;AACnB,MAAM,UAAU,EAAE;AAClB,QAAQ,IAAI;AACZ,QAAQ,KAAK;AACb,QAAQ,UAAU;AAClB,QAAQ,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,KAAK;AAChD,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,IAAI;AACZ,QAAQ;AACR,OAAO;AACP,MAAM,WAAW,EAAE,eAAe;AAClC,MAAM;AACN,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACxD,IAAI,OAAO;AACX,MAAM,aAAa,EAAE,EAAE;AACvB,MAAM,UAAU,EAAE;AAClB,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,KAAK;AACb,QAAQ,UAAU,EAAE,CAAC;AACrB,QAAQ,UAAU,EAAE;AACpB,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,IAAI;AACZ,QAAQ;AACR,OAAO;AACP,MAAM,WAAW,EAAE;AACnB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,kBAAkB,EAAE,IAAI;AAChC,QAAQ,gBAAgB,EAAE,KAAK;AAC/B,QAAQ,mBAAmB,EAAE;AAC7B,OAAO;AACP,MAAM,WAAW,EAAE,CAAC;AACpB,MAAM,KAAK,EAAE;AACb,KAAK;AACL;AACA,CAAC;;;;;;;AC5FW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA0D,CAAC,EAAE;AAExH,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACzgF,MAAC,WAAW,GAAG,CAAC,4CAA4C,CAAC,gDAAgD,CAAC,0CAA0C;AACxJ,MAAC,KAAK,GAAG;;;;"}