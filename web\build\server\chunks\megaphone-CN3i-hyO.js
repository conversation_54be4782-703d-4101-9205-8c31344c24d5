import { X as sanitize_props, R as spread_props, a0 as slot } from './index3-CqUPEnZw.js';
import { I as Icon } from './Icon-A4vzmk-O.js';

function Megaphone($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    ["path", { "d": "m3 11 18-5v12L3 14v-3z" }],
    [
      "path",
      { "d": "M11.6 16.8a3 3 0 1 1-5.8-1.6" }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "megaphone" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}

export { Megaphone as M };
//# sourceMappingURL=megaphone-CN3i-hyO.js.map
