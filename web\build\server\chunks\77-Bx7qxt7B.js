import { s as searchHelpArticles, g as getHelpArticles } from './client2-BLTPQNYX.js';
import '@sanity/client';

const load = async ({ url }) => {
  const query = url.searchParams.get("q") || "";
  try {
    const searchResults = query ? await searchHelpArticles(query, 20) : [];
    const allArticles = await getHelpArticles();
    const articlesByCategory = allArticles.reduce((acc, article) => {
      if (!acc[article.category]) {
        acc[article.category] = {
          name: getCategoryName(article.category),
          slug: article.category,
          icon: getCategoryIcon(article.category),
          articles: []
        };
      }
      acc[article.category].articles.push({
        id: article._id,
        title: article.title,
        slug: article.slug.current
      });
      return acc;
    }, {});
    const categories = Object.values(articlesByCategory).sort(
      (a, b) => a.name.localeCompare(b.name)
    );
    const formattedSearchResults = searchResults.map((article) => ({
      ...article,
      id: article._id,
      slug: article.slug.current,
      excerpt: article.description,
      category: {
        id: article.category,
        name: getCategoryName(article.category),
        slug: article.category,
        icon: getCategoryIcon(article.category)
      },
      tags: article.tags?.map((tag) => ({
        id: tag,
        name: tag,
        slug: tag.toLowerCase().replace(/\s+/g, "-")
      })) || []
    }));
    return {
      query,
      searchResults: formattedSearchResults,
      categories,
      resultCount: searchResults.length
    };
  } catch (error) {
    console.error("Error searching help articles:", error);
    return {
      query,
      searchResults: [],
      categories: [],
      resultCount: 0
    };
  }
};
function getCategoryName(slug) {
  const categoryMap = {
    "getting-started": "Getting Started",
    "auto-apply": "Using Auto Apply",
    "account-billing": "Account & Billing",
    troubleshooting: "Troubleshooting",
    "privacy-security": "Privacy & Security"
  };
  return categoryMap[slug] || slug;
}
function getCategoryIcon(slug) {
  const iconMap = {
    "getting-started": "BookOpen",
    "auto-apply": "FileText",
    "account-billing": "CreditCard",
    troubleshooting: "HelpCircle",
    "privacy-security": "Shield"
  };
  return iconMap[slug] || "HelpCircle";
}

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 77;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-CmyhXzfv.js')).default;
const server_id = "src/routes/help/search/+page.server.ts";
const imports = ["_app/immutable/nodes/77.Cbb8jQDs.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/P2mEA7T3.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/DQC1iFs0.js","_app/immutable/chunks/CfcZq63z.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/Cf6rS4LV.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/yW0TxTga.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/jRvHGFcG.js","_app/immutable/chunks/CGtH72Kl.js","_app/immutable/chunks/C1FmrZbK.js","_app/immutable/chunks/3WmhYGjL.js","_app/immutable/chunks/BaVT73bJ.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/Cb-3cdbh.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/D-o7ybA5.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/C2MdR6K0.js","_app/immutable/chunks/hQ6uUXJy.js","_app/immutable/chunks/Cs0qIT7f.js","_app/immutable/chunks/CBdr9r-W.js","_app/immutable/chunks/BPr9JIwg.js","_app/immutable/chunks/OXTnUuEm.js","_app/immutable/chunks/BwkAotBa.js","_app/immutable/chunks/C8-oZ3V_.js","_app/immutable/chunks/CsOU4yHs.js","_app/immutable/chunks/BJwwRUaF.js","_app/immutable/chunks/DuGukytH.js","_app/immutable/chunks/BkJY4La4.js","_app/immutable/chunks/DETxXRrJ.js","_app/immutable/chunks/GwmmX_iF.js","_app/immutable/chunks/D50jIuLr.js","_app/immutable/chunks/DaBofrVv.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/ChqRiddM.js","_app/immutable/chunks/CxmsTEaf.js","_app/immutable/chunks/rNI1Perp.js","_app/immutable/chunks/Ce6y1v79.js"];
const stylesheets = ["_app/immutable/assets/scroll-area.bHHIbcsu.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=77-Bx7qxt7B.js.map
