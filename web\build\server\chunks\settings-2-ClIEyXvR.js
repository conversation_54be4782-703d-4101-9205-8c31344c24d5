import { X as sanitize_props, R as spread_props, a0 as slot } from './index3-CqUPEnZw.js';
import { I as Icon } from './Icon-A4vzmk-O.js';

function Chart_column_stacked($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    ["path", { "d": "M11 13H7" }],
    ["path", { "d": "M19 9h-4" }],
    ["path", { "d": "M3 3v16a2 2 0 0 0 2 2h16" }],
    [
      "rect",
      {
        "x": "15",
        "y": "5",
        "width": "4",
        "height": "12",
        "rx": "1"
      }
    ],
    [
      "rect",
      {
        "x": "7",
        "y": "8",
        "width": "4",
        "height": "9",
        "rx": "1"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "chart-column-stacked" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}
function Settings_2($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    ["path", { "d": "M20 7h-9" }],
    ["path", { "d": "M14 17H5" }],
    [
      "circle",
      { "cx": "17", "cy": "17", "r": "3" }
    ],
    ["circle", { "cx": "7", "cy": "7", "r": "3" }]
  ];
  Icon($$payload, spread_props([
    { name: "settings-2" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}

export { Chart_column_stacked as C, Settings_2 as S };
//# sourceMappingURL=settings-2-ClIEyXvR.js.map
