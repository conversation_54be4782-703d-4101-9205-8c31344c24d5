import { p as push, V as copy_payload, W as assign_payload, Q as bind_props, q as pop, R as spread_props, $ as attr_style, P as stringify, Z as spread_attributes, a1 as derived } from './index3-CqUPEnZw.js';
import { c as cn } from './utils-pWl1tgmi.js';
import { b as box, m as mergeProps, u as useRefById } from './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import 'clsx';
import { u as useId } from './use-id-CcFpwo20.js';

const ROOT_ATTR = "data-progress-root";
class ProgressRootState {
  opts;
  constructor(opts) {
    this.opts = opts;
    useRefById(opts);
  }
  #props = derived(() => ({
    role: "progressbar",
    value: this.opts.value.current,
    "aria-valuemin": this.opts.min.current,
    "aria-valuemax": this.opts.max.current,
    "aria-valuenow": this.opts.value.current === null ? void 0 : this.opts.value.current,
    "data-value": this.opts.value.current === null ? void 0 : this.opts.value.current,
    "data-state": getProgressDataState(this.opts.value.current, this.opts.max.current),
    "data-max": this.opts.max.current,
    "data-min": this.opts.min.current,
    "data-indeterminate": this.opts.value.current === null ? "" : void 0,
    [ROOT_ATTR]: ""
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
function getProgressDataState(value, max) {
  if (value === null) return "indeterminate";
  return value === max ? "loaded" : "loading";
}
function useProgressRootState(props) {
  return new ProgressRootState(props);
}
function Progress$1($$payload, $$props) {
  push();
  let {
    child,
    children,
    value = 0,
    max = 100,
    min = 0,
    id = useId(),
    ref = null,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const rootState = useProgressRootState({
    value: box.with(() => value),
    max: box.with(() => max),
    min: box.with(() => min),
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, rootState.props);
  if (child) {
    $$payload.out += "<!--[-->";
    child($$payload, { props: mergedProps });
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;
    children?.($$payload);
    $$payload.out += `<!----></div>`;
  }
  $$payload.out += `<!--]-->`;
  bind_props($$props, { ref });
  pop();
}
function Progress($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    max = 100,
    value,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Progress$1($$payload2, spread_props([
      {
        "data-slot": "progress",
        class: cn("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full", className),
        value,
        max
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        },
        children: ($$payload3) => {
          $$payload3.out += `<div data-slot="progress-indicator" class="bg-primary h-full w-full flex-1 transition-all"${attr_style(`transform: translateX(-${stringify(100 - 100 * (value ?? 0) / (max ?? 1))}%)`)}></div>`;
        },
        $$slots: { default: true }
      }
    ]));
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}

export { Progress as P };
//# sourceMappingURL=progress-DR0SfStT.js.map
