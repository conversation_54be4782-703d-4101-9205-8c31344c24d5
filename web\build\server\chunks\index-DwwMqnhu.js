import { parseISO } from 'date-fns';
import 'svelte/store';
import clsx from 'clsx';
import { extendTailwindMerge } from 'tailwind-merge';

function range(start, stop, step) {
  start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;

  var i = -1,
      n = Math.max(0, Math.ceil((stop - start) / step)) | 0,
      range = new Array(n);

  while (++i < n) {
    range[i] = start + i * step;
  }

  return range;
}

const parseNumber = (color, len) => {
	if (typeof color !== 'number') return;

	// hex3: #c93 -> #cc9933
	if (len === 3) {
		return {
			mode: 'rgb',
			r: (((color >> 8) & 0xf) | ((color >> 4) & 0xf0)) / 255,
			g: (((color >> 4) & 0xf) | (color & 0xf0)) / 255,
			b: ((color & 0xf) | ((color << 4) & 0xf0)) / 255
		};
	}

	// hex4: #c931 -> #cc993311
	if (len === 4) {
		return {
			mode: 'rgb',
			r: (((color >> 12) & 0xf) | ((color >> 8) & 0xf0)) / 255,
			g: (((color >> 8) & 0xf) | ((color >> 4) & 0xf0)) / 255,
			b: (((color >> 4) & 0xf) | (color & 0xf0)) / 255,
			alpha: ((color & 0xf) | ((color << 4) & 0xf0)) / 255
		};
	}

	// hex6: #f0f1f2
	if (len === 6) {
		return {
			mode: 'rgb',
			r: ((color >> 16) & 0xff) / 255,
			g: ((color >> 8) & 0xff) / 255,
			b: (color & 0xff) / 255
		};
	}

	// hex8: #f0f1f2ff
	if (len === 8) {
		return {
			mode: 'rgb',
			r: ((color >> 24) & 0xff) / 255,
			g: ((color >> 16) & 0xff) / 255,
			b: ((color >> 8) & 0xff) / 255,
			alpha: (color & 0xff) / 255
		};
	}
};

const named = {
	aliceblue: 0xf0f8ff,
	antiquewhite: 0xfaebd7,
	aqua: 0x00ffff,
	aquamarine: 0x7fffd4,
	azure: 0xf0ffff,
	beige: 0xf5f5dc,
	bisque: 0xffe4c4,
	black: 0x000000,
	blanchedalmond: 0xffebcd,
	blue: 0x0000ff,
	blueviolet: 0x8a2be2,
	brown: 0xa52a2a,
	burlywood: 0xdeb887,
	cadetblue: 0x5f9ea0,
	chartreuse: 0x7fff00,
	chocolate: 0xd2691e,
	coral: 0xff7f50,
	cornflowerblue: 0x6495ed,
	cornsilk: 0xfff8dc,
	crimson: 0xdc143c,
	cyan: 0x00ffff,
	darkblue: 0x00008b,
	darkcyan: 0x008b8b,
	darkgoldenrod: 0xb8860b,
	darkgray: 0xa9a9a9,
	darkgreen: 0x006400,
	darkgrey: 0xa9a9a9,
	darkkhaki: 0xbdb76b,
	darkmagenta: 0x8b008b,
	darkolivegreen: 0x556b2f,
	darkorange: 0xff8c00,
	darkorchid: 0x9932cc,
	darkred: 0x8b0000,
	darksalmon: 0xe9967a,
	darkseagreen: 0x8fbc8f,
	darkslateblue: 0x483d8b,
	darkslategray: 0x2f4f4f,
	darkslategrey: 0x2f4f4f,
	darkturquoise: 0x00ced1,
	darkviolet: 0x9400d3,
	deeppink: 0xff1493,
	deepskyblue: 0x00bfff,
	dimgray: 0x696969,
	dimgrey: 0x696969,
	dodgerblue: 0x1e90ff,
	firebrick: 0xb22222,
	floralwhite: 0xfffaf0,
	forestgreen: 0x228b22,
	fuchsia: 0xff00ff,
	gainsboro: 0xdcdcdc,
	ghostwhite: 0xf8f8ff,
	gold: 0xffd700,
	goldenrod: 0xdaa520,
	gray: 0x808080,
	green: 0x008000,
	greenyellow: 0xadff2f,
	grey: 0x808080,
	honeydew: 0xf0fff0,
	hotpink: 0xff69b4,
	indianred: 0xcd5c5c,
	indigo: 0x4b0082,
	ivory: 0xfffff0,
	khaki: 0xf0e68c,
	lavender: 0xe6e6fa,
	lavenderblush: 0xfff0f5,
	lawngreen: 0x7cfc00,
	lemonchiffon: 0xfffacd,
	lightblue: 0xadd8e6,
	lightcoral: 0xf08080,
	lightcyan: 0xe0ffff,
	lightgoldenrodyellow: 0xfafad2,
	lightgray: 0xd3d3d3,
	lightgreen: 0x90ee90,
	lightgrey: 0xd3d3d3,
	lightpink: 0xffb6c1,
	lightsalmon: 0xffa07a,
	lightseagreen: 0x20b2aa,
	lightskyblue: 0x87cefa,
	lightslategray: 0x778899,
	lightslategrey: 0x778899,
	lightsteelblue: 0xb0c4de,
	lightyellow: 0xffffe0,
	lime: 0x00ff00,
	limegreen: 0x32cd32,
	linen: 0xfaf0e6,
	magenta: 0xff00ff,
	maroon: 0x800000,
	mediumaquamarine: 0x66cdaa,
	mediumblue: 0x0000cd,
	mediumorchid: 0xba55d3,
	mediumpurple: 0x9370db,
	mediumseagreen: 0x3cb371,
	mediumslateblue: 0x7b68ee,
	mediumspringgreen: 0x00fa9a,
	mediumturquoise: 0x48d1cc,
	mediumvioletred: 0xc71585,
	midnightblue: 0x191970,
	mintcream: 0xf5fffa,
	mistyrose: 0xffe4e1,
	moccasin: 0xffe4b5,
	navajowhite: 0xffdead,
	navy: 0x000080,
	oldlace: 0xfdf5e6,
	olive: 0x808000,
	olivedrab: 0x6b8e23,
	orange: 0xffa500,
	orangered: 0xff4500,
	orchid: 0xda70d6,
	palegoldenrod: 0xeee8aa,
	palegreen: 0x98fb98,
	paleturquoise: 0xafeeee,
	palevioletred: 0xdb7093,
	papayawhip: 0xffefd5,
	peachpuff: 0xffdab9,
	peru: 0xcd853f,
	pink: 0xffc0cb,
	plum: 0xdda0dd,
	powderblue: 0xb0e0e6,
	purple: 0x800080,

	// Added in CSS Colors Level 4:
	// https://drafts.csswg.org/css-color/#changes-from-3
	rebeccapurple: 0x663399,

	red: 0xff0000,
	rosybrown: 0xbc8f8f,
	royalblue: 0x4169e1,
	saddlebrown: 0x8b4513,
	salmon: 0xfa8072,
	sandybrown: 0xf4a460,
	seagreen: 0x2e8b57,
	seashell: 0xfff5ee,
	sienna: 0xa0522d,
	silver: 0xc0c0c0,
	skyblue: 0x87ceeb,
	slateblue: 0x6a5acd,
	slategray: 0x708090,
	slategrey: 0x708090,
	snow: 0xfffafa,
	springgreen: 0x00ff7f,
	steelblue: 0x4682b4,
	tan: 0xd2b48c,
	teal: 0x008080,
	thistle: 0xd8bfd8,
	tomato: 0xff6347,
	turquoise: 0x40e0d0,
	violet: 0xee82ee,
	wheat: 0xf5deb3,
	white: 0xffffff,
	whitesmoke: 0xf5f5f5,
	yellow: 0xffff00,
	yellowgreen: 0x9acd32
};

// Also supports the `transparent` color as defined in:
// https://drafts.csswg.org/css-color/#transparent-black
const parseNamed = color => {
	return parseNumber(named[color.toLowerCase()], 6);
};

const hex = /^#?([0-9a-f]{8}|[0-9a-f]{6}|[0-9a-f]{4}|[0-9a-f]{3})$/i;

const parseHex = color => {
	let match;
	// eslint-disable-next-line no-cond-assign
	return (match = color.match(hex))
		? parseNumber(parseInt(match[1], 16), match[1].length)
		: undefined;
};

/*
	Basic building blocks for color regexes
	---------------------------------------

	These regexes are expressed as strings
	to be interpolated in the color regexes.
 */

// <number>
const num$1 = '([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)';

// <percentage>
const per = `${num$1}%`;

// <number-percentage> (<alpha-value>)
const num_per = `(?:${num$1}%|${num$1})`;

// <hue>
const hue$1 = `(?:${num$1}(deg|grad|rad|turn)|${num$1})`;

const c = `\\s*,\\s*`; // comma

/*
	rgb() regular expressions for legacy format
	Reference: https://drafts.csswg.org/css-color/#rgb-functions
 */
const rgb_num_old = new RegExp(
	`^rgba?\\(\\s*${num$1}${c}${num$1}${c}${num$1}\\s*(?:,\\s*${num_per}\\s*)?\\)$`
);

const rgb_per_old = new RegExp(
	`^rgba?\\(\\s*${per}${c}${per}${c}${per}\\s*(?:,\\s*${num_per}\\s*)?\\)$`
);

const parseRgbLegacy = color => {
	let res = { mode: 'rgb' };
	let match;
	if ((match = color.match(rgb_num_old))) {
		if (match[1] !== undefined) {
			res.r = match[1] / 255;
		}
		if (match[2] !== undefined) {
			res.g = match[2] / 255;
		}
		if (match[3] !== undefined) {
			res.b = match[3] / 255;
		}
	} else if ((match = color.match(rgb_per_old))) {
		if (match[1] !== undefined) {
			res.r = match[1] / 100;
		}
		if (match[2] !== undefined) {
			res.g = match[2] / 100;
		}
		if (match[3] !== undefined) {
			res.b = match[3] / 100;
		}
	} else {
		return undefined;
	}

	if (match[4] !== undefined) {
		res.alpha = Math.max(0, Math.min(1, match[4] / 100));
	} else if (match[5] !== undefined) {
		res.alpha = Math.max(0, Math.min(1, +match[5]));
	}

	return res;
};

const prepare = (color, mode) =>
	color === undefined
		? undefined
		: typeof color !== 'object'
		? parse(color)
		: color.mode !== undefined
		? color
		: mode
		? { ...color, mode }
		: undefined;

const converter =
	(target_mode = 'rgb') =>
	color =>
		(color = prepare(color, target_mode)) !== undefined
			? // if the color's mode corresponds to our target mode
			  color.mode === target_mode
				? // then just return the color
				  color
				: // otherwise check to see if we have a dedicated
				// converter for the target mode
				converters[color.mode][target_mode]
				? // and return its result...
				  converters[color.mode][target_mode](color)
				: // ...otherwise pass through RGB as an intermediary step.
				// if the target mode is RGB...
				target_mode === 'rgb'
				? // just return the RGB
				  converters[color.mode].rgb(color)
				: // otherwise convert color.mode -> RGB -> target_mode
				  converters.rgb[target_mode](converters[color.mode].rgb(color))
			: undefined;

const converters = {};
const modes = {};

const parsers = [];
const colorProfiles = {};

const identity$1 = v => v;

const useMode = definition => {
	converters[definition.mode] = {
		...converters[definition.mode],
		...definition.toMode
	};

	Object.keys(definition.fromMode || {}).forEach(k => {
		if (!converters[k]) {
			converters[k] = {};
		}
		converters[k][definition.mode] = definition.fromMode[k];
	});

	// Color space channel ranges
	if (!definition.ranges) {
		definition.ranges = {};
	}

	if (!definition.difference) {
		definition.difference = {};
	}

	definition.channels.forEach(channel => {
		// undefined channel ranges default to the [0, 1] interval
		if (definition.ranges[channel] === undefined) {
			definition.ranges[channel] = [0, 1];
		}

		if (!definition.interpolate[channel]) {
			throw new Error(`Missing interpolator for: ${channel}`);
		}

		if (typeof definition.interpolate[channel] === 'function') {
			definition.interpolate[channel] = {
				use: definition.interpolate[channel]
			};
		}

		if (!definition.interpolate[channel].fixup) {
			definition.interpolate[channel].fixup = identity$1;
		}
	});

	modes[definition.mode] = definition;
	(definition.parse || []).forEach(parser => {
		useParser(parser, definition.mode);
	});

	return converter(definition.mode);
};

const getMode = mode => modes[mode];

const useParser = (parser, mode) => {
	if (typeof parser === 'string') {
		if (!mode) {
			throw new Error(`'mode' required when 'parser' is a string`);
		}
		colorProfiles[parser] = mode;
	} else if (typeof parser === 'function') {
		if (parsers.indexOf(parser) < 0) {
			parsers.push(parser);
		}
	}
};

/* eslint-disable-next-line no-control-regex */
const IdentStartCodePoint = /[^\x00-\x7F]|[a-zA-Z_]/;

/* eslint-disable-next-line no-control-regex */
const IdentCodePoint = /[^\x00-\x7F]|[-\w]/;

const Tok = {
	Function: 'function',
	Ident: 'ident',
	Number: 'number',
	Percentage: 'percentage',
	ParenClose: ')',
	None: 'none',
	Hue: 'hue',
	Alpha: 'alpha'
};

let _i = 0;

/*
	4.3.10. Check if three code points would start a number
	https://drafts.csswg.org/css-syntax/#starts-with-a-number
 */
function is_num(chars) {
	let ch = chars[_i];
	let ch1 = chars[_i + 1];
	if (ch === '-' || ch === '+') {
		return /\d/.test(ch1) || (ch1 === '.' && /\d/.test(chars[_i + 2]));
	}
	if (ch === '.') {
		return /\d/.test(ch1);
	}
	return /\d/.test(ch);
}

/*
	Check if the stream starts with an identifier.
 */

function is_ident(chars) {
	if (_i >= chars.length) {
		return false;
	}
	let ch = chars[_i];
	if (IdentStartCodePoint.test(ch)) {
		return true;
	}
	if (ch === '-') {
		if (chars.length - _i < 2) {
			return false;
		}
		let ch1 = chars[_i + 1];
		if (ch1 === '-' || IdentStartCodePoint.test(ch1)) {
			return true;
		}
		return false;
	}
	return false;
}

/*
	4.3.3. Consume a numeric token
	https://drafts.csswg.org/css-syntax/#consume-numeric-token
 */

const huenits = {
	deg: 1,
	rad: 180 / Math.PI,
	grad: 9 / 10,
	turn: 360
};

function num(chars) {
	let value = '';
	if (chars[_i] === '-' || chars[_i] === '+') {
		value += chars[_i++];
	}
	value += digits(chars);
	if (chars[_i] === '.' && /\d/.test(chars[_i + 1])) {
		value += chars[_i++] + digits(chars);
	}
	if (chars[_i] === 'e' || chars[_i] === 'E') {
		if (
			(chars[_i + 1] === '-' || chars[_i + 1] === '+') &&
			/\d/.test(chars[_i + 2])
		) {
			value += chars[_i++] + chars[_i++] + digits(chars);
		} else if (/\d/.test(chars[_i + 1])) {
			value += chars[_i++] + digits(chars);
		}
	}
	if (is_ident(chars)) {
		let id = ident(chars);
		if (id === 'deg' || id === 'rad' || id === 'turn' || id === 'grad') {
			return { type: Tok.Hue, value: value * huenits[id] };
		}
		return undefined;
	}
	if (chars[_i] === '%') {
		_i++;
		return { type: Tok.Percentage, value: +value };
	}
	return { type: Tok.Number, value: +value };
}

/*
	Consume digits.
 */
function digits(chars) {
	let v = '';
	while (/\d/.test(chars[_i])) {
		v += chars[_i++];
	}
	return v;
}

/*
	Consume an identifier.
 */
function ident(chars) {
	let v = '';
	while (_i < chars.length && IdentCodePoint.test(chars[_i])) {
		v += chars[_i++];
	}
	return v;
}

/*
	Consume an ident-like token.
 */
function identlike(chars) {
	let v = ident(chars);
	if (chars[_i] === '(') {
		_i++;
		return { type: Tok.Function, value: v };
	}
	if (v === 'none') {
		return { type: Tok.None, value: undefined };
	}
	return { type: Tok.Ident, value: v };
}

function tokenize(str = '') {
	let chars = str.trim();
	let tokens = [];
	let ch;

	/* reset counter */
	_i = 0;

	while (_i < chars.length) {
		ch = chars[_i++];

		/*
			Consume whitespace without emitting it
		 */
		if (ch === '\n' || ch === '\t' || ch === ' ') {
			while (
				_i < chars.length &&
				(chars[_i] === '\n' || chars[_i] === '\t' || chars[_i] === ' ')
			) {
				_i++;
			}
			continue;
		}

		if (ch === ',') {
			return undefined;
		}

		if (ch === ')') {
			tokens.push({ type: Tok.ParenClose });
			continue;
		}

		if (ch === '+') {
			_i--;
			if (is_num(chars)) {
				tokens.push(num(chars));
				continue;
			}
			return undefined;
		}

		if (ch === '-') {
			_i--;
			if (is_num(chars)) {
				tokens.push(num(chars));
				continue;
			}
			if (is_ident(chars)) {
				tokens.push({ type: Tok.Ident, value: ident(chars) });
				continue;
			}
			return undefined;
		}

		if (ch === '.') {
			_i--;
			if (is_num(chars)) {
				tokens.push(num(chars));
				continue;
			}
			return undefined;
		}

		if (ch === '/') {
			while (
				_i < chars.length &&
				(chars[_i] === '\n' || chars[_i] === '\t' || chars[_i] === ' ')
			) {
				_i++;
			}
			let alpha;
			if (is_num(chars)) {
				alpha = num(chars);
				if (alpha.type !== Tok.Hue) {
					tokens.push({ type: Tok.Alpha, value: alpha });
					continue;
				}
			}
			if (is_ident(chars)) {
				if (ident(chars) === 'none') {
					tokens.push({
						type: Tok.Alpha,
						value: { type: Tok.None, value: undefined }
					});
					continue;
				}
			}
			return undefined;
		}

		if (/\d/.test(ch)) {
			_i--;
			tokens.push(num(chars));
			continue;
		}

		if (IdentStartCodePoint.test(ch)) {
			_i--;
			tokens.push(identlike(chars));
			continue;
		}

		/*
			Treat everything not already handled as an error.
		 */
		return undefined;
	}

	return tokens;
}

function parseColorSyntax(tokens) {
	tokens._i = 0;
	let token = tokens[tokens._i++];
	if (!token || token.type !== Tok.Function || token.value !== 'color') {
		return undefined;
	}
	token = tokens[tokens._i++];
	if (token.type !== Tok.Ident) {
		return undefined;
	}
	const mode = colorProfiles[token.value];
	if (!mode) {
		return undefined;
	}
	const res = { mode };
	const coords = consumeCoords(tokens, false);
	if (!coords) {
		return undefined;
	}
	const channels = getMode(mode).channels;
	for (let ii = 0, c, ch; ii < channels.length; ii++) {
		c = coords[ii];
		ch = channels[ii];
		if (c.type !== Tok.None) {
			res[ch] = c.type === Tok.Number ? c.value : c.value / 100;
			if (ch === 'alpha') {
				res[ch] = Math.max(0, Math.min(1, res[ch]));
			}
		}
	}
	return res;
}

function consumeCoords(tokens, includeHue) {
	const coords = [];
	let token;
	while (tokens._i < tokens.length) {
		token = tokens[tokens._i++];
		if (
			token.type === Tok.None ||
			token.type === Tok.Number ||
			token.type === Tok.Alpha ||
			token.type === Tok.Percentage ||
			(includeHue && token.type === Tok.Hue)
		) {
			coords.push(token);
			continue;
		}
		if (token.type === Tok.ParenClose) {
			if (tokens._i < tokens.length) {
				return undefined;
			}
			continue;
		}
		return undefined;
	}

	if (coords.length < 3 || coords.length > 4) {
		return undefined;
	}

	if (coords.length === 4) {
		if (coords[3].type !== Tok.Alpha) {
			return undefined;
		}
		coords[3] = coords[3].value;
	}
	if (coords.length === 3) {
		coords.push({ type: Tok.None, value: undefined });
	}

	return coords.every(c => c.type !== Tok.Alpha) ? coords : undefined;
}

function parseModernSyntax(tokens, includeHue) {
	tokens._i = 0;
	let token = tokens[tokens._i++];
	if (!token || token.type !== Tok.Function) {
		return undefined;
	}
	let coords = consumeCoords(tokens, includeHue);
	if (!coords) {
		return undefined;
	}
	coords.unshift(token.value);
	return coords;
}

const parse = color => {
	if (typeof color !== 'string') {
		return undefined;
	}
	const tokens = tokenize(color);
	const parsed = tokens ? parseModernSyntax(tokens, true) : undefined;
	let result = undefined;
	let i = 0;
	let len = parsers.length;
	while (i < len) {
		if ((result = parsers[i++](color, parsed)) !== undefined) {
			return result;
		}
	}
	return tokens ? parseColorSyntax(tokens) : undefined;
};

function parseRgb(color, parsed) {
	if (!parsed || (parsed[0] !== 'rgb' && parsed[0] !== 'rgba')) {
		return undefined;
	}
	const res = { mode: 'rgb' };
	const [, r, g, b, alpha] = parsed;
	if (r.type === Tok.Hue || g.type === Tok.Hue || b.type === Tok.Hue) {
		return undefined;
	}
	if (r.type !== Tok.None) {
		res.r = r.type === Tok.Number ? r.value / 255 : r.value / 100;
	}
	if (g.type !== Tok.None) {
		res.g = g.type === Tok.Number ? g.value / 255 : g.value / 100;
	}
	if (b.type !== Tok.None) {
		res.b = b.type === Tok.Number ? b.value / 255 : b.value / 100;
	}
	if (alpha.type !== Tok.None) {
		res.alpha = Math.min(
			1,
			Math.max(
				0,
				alpha.type === Tok.Number ? alpha.value : alpha.value / 100
			)
		);
	}

	return res;
}

const parseTransparent = c =>
	c === 'transparent'
		? { mode: 'rgb', r: 0, g: 0, b: 0, alpha: 0 }
		: undefined;

const lerp = (a, b, t) => a + t * (b - a);

const get_classes = arr => {
	let classes = [];
	for (let i = 0; i < arr.length - 1; i++) {
		let a = arr[i];
		let b = arr[i + 1];
		if (a === undefined && b === undefined) {
			classes.push(undefined);
		} else if (a !== undefined && b !== undefined) {
			classes.push([a, b]);
		} else {
			classes.push(a !== undefined ? [a, a] : [b, b]);
		}
	}
	return classes;
};

const interpolatorPiecewise = interpolator => arr => {
	let classes = get_classes(arr);
	return t => {
		let cls = t * classes.length;
		let idx = t >= 1 ? classes.length - 1 : Math.max(Math.floor(cls), 0);
		let pair = classes[idx];
		return pair === undefined
			? undefined
			: interpolator(pair[0], pair[1], cls - idx);
	};
};

const interpolatorLinear = interpolatorPiecewise(lerp);

const fixupAlpha = arr => {
	let some_defined = false;
	let res = arr.map(v => {
		if (v !== undefined) {
			some_defined = true;
			return v;
		}
		return 1;
	});
	return some_defined ? res : arr;
};

/*
	sRGB color space
 */

const definition$r = {
	mode: 'rgb',
	channels: ['r', 'g', 'b', 'alpha'],
	parse: [
		parseRgb,
		parseHex,
		parseRgbLegacy,
		parseNamed,
		parseTransparent,
		'srgb'
	],
	serialize: 'srgb',
	interpolate: {
		r: interpolatorLinear,
		g: interpolatorLinear,
		b: interpolatorLinear,
		alpha: { use: interpolatorLinear, fixup: fixupAlpha }
	},
	gamut: true,
	white: { r: 1, g: 1, b: 1 },
	black: { r: 0, g: 0, b: 0 }
};

/*
	Convert A98 RGB values to CIE XYZ D65

	References:
		* https://drafts.csswg.org/css-color/#color-conversion-code
		* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
		* https://www.adobe.com/digitalimag/pdfs/AdobeRGB1998.pdf
*/

const linearize$2 = (v = 0) => Math.pow(Math.abs(v), 563 / 256) * Math.sign(v);

const convertA98ToXyz65 = a98 => {
	let r = linearize$2(a98.r);
	let g = linearize$2(a98.g);
	let b = linearize$2(a98.b);
	let res = {
		mode: 'xyz65',
		x:
			0.5766690429101305 * r +
			0.1855582379065463 * g +
			0.1882286462349947 * b,
		y:
			0.297344975250536 * r +
			0.6273635662554661 * g +
			0.0752914584939979 * b,
		z:
			0.0270313613864123 * r +
			0.0706888525358272 * g +
			0.9913375368376386 * b
	};
	if (a98.alpha !== undefined) {
		res.alpha = a98.alpha;
	}
	return res;
};

/*
	Convert CIE XYZ D65 values to A98 RGB

	References:
		* https://drafts.csswg.org/css-color/#color-conversion-code
		* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
*/

const gamma$2 = v => Math.pow(Math.abs(v), 256 / 563) * Math.sign(v);

const convertXyz65ToA98 = ({ x, y, z, alpha }) => {
	if (x === undefined) x = 0;
	if (y === undefined) y = 0;
	if (z === undefined) z = 0;
	let res = {
		mode: 'a98',
		r: gamma$2(
			x * 2.0415879038107465 -
				y * 0.5650069742788597 -
				0.3447313507783297 * z
		),
		g: gamma$2(
			x * -0.9692436362808798 +
				y * 1.8759675015077206 +
				0.0415550574071756 * z
		),
		b: gamma$2(
			x * 0.0134442806320312 -
				y * 0.1183623922310184 +
				1.0151749943912058 * z
		)
	};
	if (alpha !== undefined) {
		res.alpha = alpha;
	}
	return res;
};

const fn$3 = (c = 0) => {
	const abs = Math.abs(c);
	if (abs <= 0.04045) {
		return c / 12.92;
	}
	return (Math.sign(c) || 1) * Math.pow((abs + 0.055) / 1.055, 2.4);
};

const convertRgbToLrgb = ({ r, g, b, alpha }) => {
	let res = {
		mode: 'lrgb',
		r: fn$3(r),
		g: fn$3(g),
		b: fn$3(b)
	};
	if (alpha !== undefined) res.alpha = alpha;
	return res;
};

/*
	Convert sRGB values to CIE XYZ D65

	References:
		* https://drafts.csswg.org/css-color/#color-conversion-code
		* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
		* https://observablehq.com/@danburzo/color-matrix-calculator
*/


const convertRgbToXyz65 = rgb => {
	let { r, g, b, alpha } = convertRgbToLrgb(rgb);
	let res = {
		mode: 'xyz65',
		x:
			0.4123907992659593 * r +
			0.357584339383878 * g +
			0.1804807884018343 * b,
		y:
			0.2126390058715102 * r +
			0.715168678767756 * g +
			0.0721923153607337 * b,
		z:
			0.0193308187155918 * r +
			0.119194779794626 * g +
			0.9505321522496607 * b
	};
	if (alpha !== undefined) {
		res.alpha = alpha;
	}
	return res;
};

const fn$2 = (c = 0) => {
	const abs = Math.abs(c);
	if (abs > 0.0031308) {
		return (Math.sign(c) || 1) * (1.055 * Math.pow(abs, 1 / 2.4) - 0.055);
	}
	return c * 12.92;
};

const convertLrgbToRgb = ({ r, g, b, alpha }, mode = 'rgb') => {
	let res = {
		mode,
		r: fn$2(r),
		g: fn$2(g),
		b: fn$2(b)
	};
	if (alpha !== undefined) res.alpha = alpha;
	return res;
};

/*
	CIE XYZ D65 values to sRGB.

	References:
		* https://drafts.csswg.org/css-color/#color-conversion-code
		* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
		* https://observablehq.com/@danburzo/color-matrix-calculator
*/


const convertXyz65ToRgb = ({ x, y, z, alpha }) => {
	if (x === undefined) x = 0;
	if (y === undefined) y = 0;
	if (z === undefined) z = 0;
	let res = convertLrgbToRgb({
		r:
			x * 3.2409699419045226 -
			y * 1.5373831775700939 -
			0.4986107602930034 * z,
		g:
			x * -0.9692436362808796 +
			y * 1.8759675015077204 +
			0.0415550574071756 * z,
		b:
			x * 0.0556300796969936 -
			y * 0.2039769588889765 +
			1.0569715142428784 * z
	});
	if (alpha !== undefined) {
		res.alpha = alpha;
	}
	return res;
};

const definition$q = {
	...definition$r,
	mode: 'a98',
	parse: ['a98-rgb'],
	serialize: 'a98-rgb',

	fromMode: {
		rgb: color => convertXyz65ToA98(convertRgbToXyz65(color)),
		xyz65: convertXyz65ToA98
	},

	toMode: {
		rgb: color => convertXyz65ToRgb(convertA98ToXyz65(color)),
		xyz65: convertA98ToXyz65
	}
};

const normalizeHue = hue => ((hue = hue % 360) < 0 ? hue + 360 : hue);

const hue = (hues, fn) => {
	return hues
		.map((hue, idx, arr) => {
			if (hue === undefined) {
				return hue;
			}
			let normalized = normalizeHue(hue);
			if (idx === 0 || hues[idx - 1] === undefined) {
				return normalized;
			}
			return fn(normalized - normalizeHue(arr[idx - 1]));
		})
		.reduce((acc, curr) => {
			if (
				!acc.length ||
				curr === undefined ||
				acc[acc.length - 1] === undefined
			) {
				acc.push(curr);
				return acc;
			}
			acc.push(curr + acc[acc.length - 1]);
			return acc;
		}, []);
};

const fixupHueShorter = arr =>
	hue(arr, d => (Math.abs(d) <= 180 ? d : d - 360 * Math.sign(d)));

const M = [-0.14861, 1.78277, -0.29227, -0.90649, 1.97294, 0];

const degToRad = Math.PI / 180;
const radToDeg = 180 / Math.PI;

/*
	Convert a RGB color to the Cubehelix HSL color space.

	This computation is not present in Green's paper:
	https://arxiv.org/pdf/1108.5083.pdf

	...but can be derived from the inverse, HSL to RGB conversion.

	It matches the math in Mike Bostock's D3 implementation:

	https://github.com/d3/d3-color/blob/master/src/cubehelix.js
 */


let DE = M[3] * M[4];
let BE = M[1] * M[4];
let BCAD = M[1] * M[2] - M[0] * M[3];

const convertRgbToCubehelix = ({ r, g, b, alpha }) => {
	if (r === undefined) r = 0;
	if (g === undefined) g = 0;
	if (b === undefined) b = 0;
	let l = (BCAD * b + r * DE - g * BE) / (BCAD + DE - BE);
	let x = b - l;
	let y = (M[4] * (g - l) - M[2] * x) / M[3];

	let res = {
		mode: 'cubehelix',
		l: l,
		s:
			l === 0 || l === 1
				? undefined
				: Math.sqrt(x * x + y * y) / (M[4] * l * (1 - l))
	};

	if (res.s) res.h = Math.atan2(y, x) * radToDeg - 120;
	if (alpha !== undefined) res.alpha = alpha;

	return res;
};

const convertCubehelixToRgb = ({ h, s, l, alpha }) => {
	let res = { mode: 'rgb' };

	h = (h === undefined ? 0 : h + 120) * degToRad;
	if (l === undefined) l = 0;

	let amp = s === undefined ? 0 : s * l * (1 - l);

	let cosh = Math.cos(h);
	let sinh = Math.sin(h);

	res.r = l + amp * (M[0] * cosh + M[1] * sinh);
	res.g = l + amp * (M[2] * cosh + M[3] * sinh);
	res.b = l + amp * (M[4] * cosh + M[5] * sinh);

	if (alpha !== undefined) res.alpha = alpha;
	return res;
};

const differenceHueSaturation = (std, smp) => {
	if (std.h === undefined || smp.h === undefined || !std.s || !smp.s) {
		return 0;
	}
	let std_h = normalizeHue(std.h);
	let smp_h = normalizeHue(smp.h);
	let dH = Math.sin((((smp_h - std_h + 360) / 2) * Math.PI) / 180);
	return 2 * Math.sqrt(std.s * smp.s) * dH;
};

const differenceHueNaive = (std, smp) => {
	if (std.h === undefined || smp.h === undefined) {
		return 0;
	}
	let std_h = normalizeHue(std.h);
	let smp_h = normalizeHue(smp.h);
	if (Math.abs(smp_h - std_h) > 180) {
		// todo should this be normalized once again?
		return std_h - (smp_h - 360 * Math.sign(smp_h - std_h));
	}
	return smp_h - std_h;
};

const differenceHueChroma = (std, smp) => {
	if (std.h === undefined || smp.h === undefined || !std.c || !smp.c) {
		return 0;
	}
	let std_h = normalizeHue(std.h);
	let smp_h = normalizeHue(smp.h);
	let dH = Math.sin((((smp_h - std_h + 360) / 2) * Math.PI) / 180);
	return 2 * Math.sqrt(std.c * smp.c) * dH;
};

const averageAngle = val => {
	// See: https://en.wikipedia.org/wiki/Mean_of_circular_quantities
	let sum = val.reduce(
		(sum, val) => {
			if (val !== undefined) {
				let rad = (val * Math.PI) / 180;
				sum.sin += Math.sin(rad);
				sum.cos += Math.cos(rad);
			}
			return sum;
		},
		{ sin: 0, cos: 0 }
	);
	let angle = (Math.atan2(sum.sin, sum.cos) * 180) / Math.PI;
	return angle < 0 ? 360 + angle : angle;
};

/* 
	Dave Green's Cubehelix
	----------------------

	Green, D. A., 2011, "A colour scheme for the display of astronomical intensity images", 
	Bulletin of the Astronomical Society of India, 39, 289. (2011BASI...39..289G at ADS.) 

	https://www.mrao.cam.ac.uk/%7Edag/CUBEHELIX/
	https://arxiv.org/pdf/1108.5083.pdf

	Although Cubehelix was defined to be a method to obtain a colour scheme,
	it actually contains a definition of a colour space, as identified by 
	Mike Bostock and implemented in D3.js.

	Green's paper introduces the following terminology:

	* 	a `lightness` dimension in the interval [0, 1] 
		on which we interpolate to obtain the colour scheme
	*	a `start` colour that is analogous to a Hue in HSL space
	*	a number of `rotations` around the Hue cylinder.
	*	a `hue` parameter which should more appropriately be called `saturation`
	
	As such, the original definition of the Cubehelix scheme is actually an
	interpolation between two colors in the Cubehelix space:

	H: start 				H: start + 360 * rotations
	S: hue 			->		S: hue
	L: 0					L: 1

	We can therefore extend the interpolation to any two colors in this space,
	with a variable Saturation and a Lightness interval other than the fixed 0 -> 1.
*/


const definition$p = {
	mode: 'cubehelix',
	channels: ['h', 's', 'l', 'alpha'],
	parse: ['--cubehelix'],
	serialize: '--cubehelix',

	ranges: {
		h: [0, 360],
		s: [0, 4.614],
		l: [0, 1]
	},

	fromMode: {
		rgb: convertRgbToCubehelix
	},

	toMode: {
		rgb: convertCubehelixToRgb
	},

	interpolate: {
		h: {
			use: interpolatorLinear,
			fixup: fixupHueShorter
		},
		s: interpolatorLinear,
		l: interpolatorLinear,
		alpha: {
			use: interpolatorLinear,
			fixup: fixupAlpha
		}
	},

	difference: {
		h: differenceHueSaturation
	},

	average: {
		h: averageAngle
	}
};

/* 
	References: 
		* https://drafts.csswg.org/css-color/#lab-to-lch
		* https://drafts.csswg.org/css-color/#color-conversion-code
*/
const convertLabToLch = ({ l, a, b, alpha }, mode = 'lch') => {
	if (a === undefined) a = 0;
	if (b === undefined) b = 0;
	let c = Math.sqrt(a * a + b * b);
	let res = { mode, l, c };
	if (c) res.h = normalizeHue((Math.atan2(b, a) * 180) / Math.PI);
	if (alpha !== undefined) res.alpha = alpha;
	return res;
};

/* 
	References: 
		* https://drafts.csswg.org/css-color/#lch-to-lab
		* https://drafts.csswg.org/css-color/#color-conversion-code
*/
const convertLchToLab = ({ l, c, h, alpha }, mode = 'lab') => {
	if (h === undefined) h = 0;
	let res = {
		mode,
		l,
		a: c ? c * Math.cos((h / 180) * Math.PI) : 0,
		b: c ? c * Math.sin((h / 180) * Math.PI) : 0
	};
	if (alpha !== undefined) res.alpha = alpha;
	return res;
};

const k$1 = Math.pow(29, 3) / Math.pow(3, 3);
const e$1 = Math.pow(6, 3) / Math.pow(29, 3);

/*
	The XYZ tristimulus values (white point)
	of standard illuminants for the CIE 1931 2° 
	standard observer.

	See: https://en.wikipedia.org/wiki/Standard_illuminant
 */

const D50 = {
	X: 0.3457 / 0.3585,
	Y: 1,
	Z: (1 - 0.3457 - 0.3585) / 0.3585
};

const D65 = {
	X: 0.3127 / 0.329,
	Y: 1,
	Z: (1 - 0.3127 - 0.329) / 0.329
};

let fn$1 = v => (Math.pow(v, 3) > e$1 ? Math.pow(v, 3) : (116 * v - 16) / k$1);

const convertLab65ToXyz65 = ({ l, a, b, alpha }) => {
	if (l === undefined) l = 0;
	if (a === undefined) a = 0;
	if (b === undefined) b = 0;

	let fy = (l + 16) / 116;
	let fx = a / 500 + fy;
	let fz = fy - b / 200;

	let res = {
		mode: 'xyz65',
		x: fn$1(fx) * D65.X,
		y: fn$1(fy) * D65.Y,
		z: fn$1(fz) * D65.Z
	};

	if (alpha !== undefined) {
		res.alpha = alpha;
	}

	return res;
};

const convertLab65ToRgb = lab => convertXyz65ToRgb(convertLab65ToXyz65(lab));

const f$1 = value => (value > e$1 ? Math.cbrt(value) : (k$1 * value + 16) / 116);

const convertXyz65ToLab65 = ({ x, y, z, alpha }) => {
	if (x === undefined) x = 0;
	if (y === undefined) y = 0;
	if (z === undefined) z = 0;
	let f0 = f$1(x / D65.X);
	let f1 = f$1(y / D65.Y);
	let f2 = f$1(z / D65.Z);

	let res = {
		mode: 'lab65',
		l: 116 * f1 - 16,
		a: 500 * (f0 - f1),
		b: 200 * (f1 - f2)
	};

	if (alpha !== undefined) {
		res.alpha = alpha;
	}

	return res;
};

const convertRgbToLab65 = rgb => {
	let res = convertXyz65ToLab65(convertRgbToXyz65(rgb));

	// Fixes achromatic RGB colors having a _slight_ chroma due to floating-point errors
	// and approximated computations in sRGB <-> CIELab.
	// See: https://github.com/d3/d3-color/pull/46
	if (rgb.r === rgb.b && rgb.b === rgb.g) {
		res.a = res.b = 0;
	}
	return res;
};

const kE = 1;
const kCH = 1;
const θ = (26 / 180) * Math.PI;
const cosθ = Math.cos(θ);
const sinθ = Math.sin(θ);
const factor = 100 / Math.log(139 / 100); // ~ 303.67

/*
	Convert DIN99o LCh to CIELab D65
	--------------------------------
 */

const convertDlchToLab65 = ({ l, c, h, alpha }) => {
	if (l === undefined) l = 0;
	if (c === undefined) c = 0;
	if (h === undefined) h = 0;
	let res = {
		mode: 'lab65',
		l: (Math.exp((l * kE) / factor) - 1) / 0.0039
	};

	let G = (Math.exp(0.0435 * c * kCH * kE) - 1) / 0.075;
	let e = G * Math.cos((h / 180) * Math.PI - θ);
	let f = G * Math.sin((h / 180) * Math.PI - θ);
	res.a = e * cosθ - (f / 0.83) * sinθ;
	res.b = e * sinθ + (f / 0.83) * cosθ;

	if (alpha !== undefined) res.alpha = alpha;
	return res;
};

/*
	Convert CIELab D65 to DIN99o LCh
	================================
 */

const convertLab65ToDlch = ({ l, a, b, alpha }) => {
	if (l === undefined) l = 0;
	if (a === undefined) a = 0;
	if (b === undefined) b = 0;
	let e = a * cosθ + b * sinθ;
	let f = 0.83 * (b * cosθ - a * sinθ);
	let G = Math.sqrt(e * e + f * f);
	let res = {
		mode: 'dlch',
		l: (factor / kE) * Math.log(1 + 0.0039 * l),
		c: Math.log(1 + 0.075 * G) / (0.0435 * kCH * kE)
	};

	if (res.c) {
		res.h = normalizeHue(((Math.atan2(f, e) + θ) / Math.PI) * 180);
	}

	if (alpha !== undefined) res.alpha = alpha;
	return res;
};

const convertDlabToLab65 = c => convertDlchToLab65(convertLabToLch(c, 'dlch'));
const convertLab65ToDlab = c => convertLchToLab(convertLab65ToDlch(c), 'dlab');

const definition$o = {
	mode: 'dlab',

	parse: ['--din99o-lab'],
	serialize: '--din99o-lab',

	toMode: {
		lab65: convertDlabToLab65,
		rgb: c => convertLab65ToRgb(convertDlabToLab65(c))
	},

	fromMode: {
		lab65: convertLab65ToDlab,
		rgb: c => convertLab65ToDlab(convertRgbToLab65(c))
	},

	channels: ['l', 'a', 'b', 'alpha'],

	ranges: {
		l: [0, 100],
		a: [-40.09, 45.501],
		b: [-40.469, 44.344]
	},

	interpolate: {
		l: interpolatorLinear,
		a: interpolatorLinear,
		b: interpolatorLinear,
		alpha: {
			use: interpolatorLinear,
			fixup: fixupAlpha
		}
	}
};

const definition$n = {
	mode: 'dlch',

	parse: ['--din99o-lch'],
	serialize: '--din99o-lch',

	toMode: {
		lab65: convertDlchToLab65,
		dlab: c => convertLchToLab(c, 'dlab'),
		rgb: c => convertLab65ToRgb(convertDlchToLab65(c))
	},

	fromMode: {
		lab65: convertLab65ToDlch,
		dlab: c => convertLabToLch(c, 'dlch'),
		rgb: c => convertLab65ToDlch(convertRgbToLab65(c))
	},

	channels: ['l', 'c', 'h', 'alpha'],

	ranges: {
		l: [0, 100],
		c: [0, 51.484],
		h: [0, 360]
	},

	interpolate: {
		l: interpolatorLinear,
		c: interpolatorLinear,
		h: {
			use: interpolatorLinear,
			fixup: fixupHueShorter
		},
		alpha: {
			use: interpolatorLinear,
			fixup: fixupAlpha
		}
	},

	difference: {
		h: differenceHueChroma
	},

	average: {
		h: averageAngle
	}
};

// Based on: https://en.wikipedia.org/wiki/HSL_and_HSV#Converting_to_RGB

function convertHsiToRgb({ h, s, i, alpha }) {
	h = normalizeHue(h !== undefined ? h : 0);
	if (s === undefined) s = 0;
	if (i === undefined) i = 0;
	let f = Math.abs(((h / 60) % 2) - 1);
	let res;
	switch (Math.floor(h / 60)) {
		case 0:
			res = {
				r: i * (1 + s * (3 / (2 - f) - 1)),
				g: i * (1 + s * ((3 * (1 - f)) / (2 - f) - 1)),
				b: i * (1 - s)
			};
			break;
		case 1:
			res = {
				r: i * (1 + s * ((3 * (1 - f)) / (2 - f) - 1)),
				g: i * (1 + s * (3 / (2 - f) - 1)),
				b: i * (1 - s)
			};
			break;
		case 2:
			res = {
				r: i * (1 - s),
				g: i * (1 + s * (3 / (2 - f) - 1)),
				b: i * (1 + s * ((3 * (1 - f)) / (2 - f) - 1))
			};
			break;
		case 3:
			res = {
				r: i * (1 - s),
				g: i * (1 + s * ((3 * (1 - f)) / (2 - f) - 1)),
				b: i * (1 + s * (3 / (2 - f) - 1))
			};
			break;
		case 4:
			res = {
				r: i * (1 + s * ((3 * (1 - f)) / (2 - f) - 1)),
				g: i * (1 - s),
				b: i * (1 + s * (3 / (2 - f) - 1))
			};
			break;
		case 5:
			res = {
				r: i * (1 + s * (3 / (2 - f) - 1)),
				g: i * (1 - s),
				b: i * (1 + s * ((3 * (1 - f)) / (2 - f) - 1))
			};
			break;
		default:
			res = { r: i * (1 - s), g: i * (1 - s), b: i * (1 - s) };
	}

	res.mode = 'rgb';
	if (alpha !== undefined) res.alpha = alpha;
	return res;
}

// Based on: https://en.wikipedia.org/wiki/HSL_and_HSV#Formal_derivation

function convertRgbToHsi({ r, g, b, alpha }) {
	if (r === undefined) r = 0;
	if (g === undefined) g = 0;
	if (b === undefined) b = 0;
	let M = Math.max(r, g, b),
		m = Math.min(r, g, b);
	let res = {
		mode: 'hsi',
		s: r + g + b === 0 ? 0 : 1 - (3 * m) / (r + g + b),
		i: (r + g + b) / 3
	};
	if (M - m !== 0)
		res.h =
			(M === r
				? (g - b) / (M - m) + (g < b) * 6
				: M === g
				? (b - r) / (M - m) + 2
				: (r - g) / (M - m) + 4) * 60;
	if (alpha !== undefined) res.alpha = alpha;
	return res;
}

const definition$m = {
	mode: 'hsi',

	toMode: {
		rgb: convertHsiToRgb
	},

	parse: ['--hsi'],
	serialize: '--hsi',

	fromMode: {
		rgb: convertRgbToHsi
	},

	channels: ['h', 's', 'i', 'alpha'],

	ranges: {
		h: [0, 360]
	},

	gamut: 'rgb',

	interpolate: {
		h: { use: interpolatorLinear, fixup: fixupHueShorter },
		s: interpolatorLinear,
		i: interpolatorLinear,
		alpha: { use: interpolatorLinear, fixup: fixupAlpha }
	},

	difference: {
		h: differenceHueSaturation
	},

	average: {
		h: averageAngle
	}
};

// Based on: https://en.wikipedia.org/wiki/HSL_and_HSV#Converting_to_RGB

function convertHslToRgb({ h, s, l, alpha }) {
	h = normalizeHue(h !== undefined ? h : 0);
	if (s === undefined) s = 0;
	if (l === undefined) l = 0;
	let m1 = l + s * (l < 0.5 ? l : 1 - l);
	let m2 = m1 - (m1 - l) * 2 * Math.abs(((h / 60) % 2) - 1);
	let res;
	switch (Math.floor(h / 60)) {
		case 0:
			res = { r: m1, g: m2, b: 2 * l - m1 };
			break;
		case 1:
			res = { r: m2, g: m1, b: 2 * l - m1 };
			break;
		case 2:
			res = { r: 2 * l - m1, g: m1, b: m2 };
			break;
		case 3:
			res = { r: 2 * l - m1, g: m2, b: m1 };
			break;
		case 4:
			res = { r: m2, g: 2 * l - m1, b: m1 };
			break;
		case 5:
			res = { r: m1, g: 2 * l - m1, b: m2 };
			break;
		default:
			res = { r: 2 * l - m1, g: 2 * l - m1, b: 2 * l - m1 };
	}
	res.mode = 'rgb';
	if (alpha !== undefined) res.alpha = alpha;
	return res;
}

// Based on: https://en.wikipedia.org/wiki/HSL_and_HSV#Formal_derivation

function convertRgbToHsl({ r, g, b, alpha }) {
	if (r === undefined) r = 0;
	if (g === undefined) g = 0;
	if (b === undefined) b = 0;
	let M = Math.max(r, g, b),
		m = Math.min(r, g, b);
	let res = {
		mode: 'hsl',
		s: M === m ? 0 : (M - m) / (1 - Math.abs(M + m - 1)),
		l: 0.5 * (M + m)
	};
	if (M - m !== 0)
		res.h =
			(M === r
				? (g - b) / (M - m) + (g < b) * 6
				: M === g
				? (b - r) / (M - m) + 2
				: (r - g) / (M - m) + 4) * 60;
	if (alpha !== undefined) res.alpha = alpha;
	return res;
}

const hueToDeg = (val, unit) => {
	switch (unit) {
		case 'deg':
			return +val;
		case 'rad':
			return (val / Math.PI) * 180;
		case 'grad':
			return (val / 10) * 9;
		case 'turn':
			return val * 360;
	}
};

/*
	hsl() regular expressions for legacy format
	Reference: https://drafts.csswg.org/css-color/#the-hsl-notation
 */
const hsl_old = new RegExp(
	`^hsla?\\(\\s*${hue$1}${c}${per}${c}${per}\\s*(?:,\\s*${num_per}\\s*)?\\)$`
);

const parseHslLegacy = color => {
	let match = color.match(hsl_old);
	if (!match) return;
	let res = { mode: 'hsl' };

	if (match[3] !== undefined) {
		res.h = +match[3];
	} else if (match[1] !== undefined && match[2] !== undefined) {
		res.h = hueToDeg(match[1], match[2]);
	}

	if (match[4] !== undefined) {
		res.s = Math.min(Math.max(0, match[4] / 100), 1);
	}

	if (match[5] !== undefined) {
		res.l = Math.min(Math.max(0, match[5] / 100), 1);
	}

	if (match[6] !== undefined) {
		res.alpha = Math.max(0, Math.min(1, match[6] / 100));
	} else if (match[7] !== undefined) {
		res.alpha = Math.max(0, Math.min(1, +match[7]));
	}
	return res;
};

function parseHsl(color, parsed) {
	if (!parsed || (parsed[0] !== 'hsl' && parsed[0] !== 'hsla')) {
		return undefined;
	}
	const res = { mode: 'hsl' };
	const [, h, s, l, alpha] = parsed;

	if (h.type !== Tok.None) {
		if (h.type === Tok.Percentage) {
			return undefined;
		}
		res.h = h.value;
	}

	if (s.type !== Tok.None) {
		if (s.type === Tok.Hue) {
			return undefined;
		}
		res.s = s.value / 100;
	}

	if (l.type !== Tok.None) {
		if (l.type === Tok.Hue) {
			return undefined;
		}
		res.l = l.value / 100;
	}

	if (alpha.type !== Tok.None) {
		res.alpha = Math.min(
			1,
			Math.max(
				0,
				alpha.type === Tok.Number ? alpha.value : alpha.value / 100
			)
		);
	}

	return res;
}

const definition$l = {
	mode: 'hsl',

	toMode: {
		rgb: convertHslToRgb
	},

	fromMode: {
		rgb: convertRgbToHsl
	},

	channels: ['h', 's', 'l', 'alpha'],

	ranges: {
		h: [0, 360]
	},

	gamut: 'rgb',

	parse: [parseHsl, parseHslLegacy],
	serialize: c =>
		`hsl(${c.h !== undefined ? c.h : 'none'} ${
			c.s !== undefined ? c.s * 100 + '%' : 'none'
		} ${c.l !== undefined ? c.l * 100 + '%' : 'none'}${
			c.alpha < 1 ? ` / ${c.alpha}` : ''
		})`,

	interpolate: {
		h: { use: interpolatorLinear, fixup: fixupHueShorter },
		s: interpolatorLinear,
		l: interpolatorLinear,
		alpha: { use: interpolatorLinear, fixup: fixupAlpha }
	},

	difference: {
		h: differenceHueSaturation
	},

	average: {
		h: averageAngle
	}
};

// Based on: https://en.wikipedia.org/wiki/HSL_and_HSV#Converting_to_RGB

function convertHsvToRgb({ h, s, v, alpha }) {
	h = normalizeHue(h !== undefined ? h : 0);
	if (s === undefined) s = 0;
	if (v === undefined) v = 0;
	let f = Math.abs(((h / 60) % 2) - 1);
	let res;
	switch (Math.floor(h / 60)) {
		case 0:
			res = { r: v, g: v * (1 - s * f), b: v * (1 - s) };
			break;
		case 1:
			res = { r: v * (1 - s * f), g: v, b: v * (1 - s) };
			break;
		case 2:
			res = { r: v * (1 - s), g: v, b: v * (1 - s * f) };
			break;
		case 3:
			res = { r: v * (1 - s), g: v * (1 - s * f), b: v };
			break;
		case 4:
			res = { r: v * (1 - s * f), g: v * (1 - s), b: v };
			break;
		case 5:
			res = { r: v, g: v * (1 - s), b: v * (1 - s * f) };
			break;
		default:
			res = { r: v * (1 - s), g: v * (1 - s), b: v * (1 - s) };
	}
	res.mode = 'rgb';
	if (alpha !== undefined) res.alpha = alpha;
	return res;
}

// Based on: https://en.wikipedia.org/wiki/HSL_and_HSV#Formal_derivation

function convertRgbToHsv({ r, g, b, alpha }) {
	if (r === undefined) r = 0;
	if (g === undefined) g = 0;
	if (b === undefined) b = 0;
	let M = Math.max(r, g, b),
		m = Math.min(r, g, b);
	let res = {
		mode: 'hsv',
		s: M === 0 ? 0 : 1 - m / M,
		v: M
	};
	if (M - m !== 0)
		res.h =
			(M === r
				? (g - b) / (M - m) + (g < b) * 6
				: M === g
				? (b - r) / (M - m) + 2
				: (r - g) / (M - m) + 4) * 60;
	if (alpha !== undefined) res.alpha = alpha;
	return res;
}

const definition$k = {
	mode: 'hsv',

	toMode: {
		rgb: convertHsvToRgb
	},

	parse: ['--hsv'],
	serialize: '--hsv',

	fromMode: {
		rgb: convertRgbToHsv
	},

	channels: ['h', 's', 'v', 'alpha'],

	ranges: {
		h: [0, 360]
	},

	gamut: 'rgb',

	interpolate: {
		h: { use: interpolatorLinear, fixup: fixupHueShorter },
		s: interpolatorLinear,
		v: interpolatorLinear,
		alpha: { use: interpolatorLinear, fixup: fixupAlpha }
	},

	difference: {
		h: differenceHueSaturation
	},

	average: {
		h: averageAngle
	}
};

/*
	HWB to RGB converter
	--------------------

	References:
		* https://drafts.csswg.org/css-color/#hwb-to-rgb
		* https://en.wikipedia.org/wiki/HWB_color_model
		* http://alvyray.com/Papers/CG/HWB_JGTv208.pdf
 */


function convertHwbToRgb({ h, w, b, alpha }) {
	if (w === undefined) w = 0;
	if (b === undefined) b = 0;
	// normalize w + b to 1
	if (w + b > 1) {
		let s = w + b;
		w /= s;
		b /= s;
	}
	return convertHsvToRgb({
		h: h,
		s: b === 1 ? 1 : 1 - w / (1 - b),
		v: 1 - b,
		alpha: alpha
	});
}

/*
	RGB to HWB converter
	--------------------

	References:
		* https://drafts.csswg.org/css-color/#hwb-to-rgb
		* https://en.wikipedia.org/wiki/HWB_color_model
		* http://alvyray.com/Papers/CG/HWB_JGTv208.pdf
 */


function convertRgbToHwb(rgba) {
	let hsv = convertRgbToHsv(rgba);
	if (hsv === undefined) return undefined;
	let s = hsv.s !== undefined ? hsv.s : 0;
	let v = hsv.v !== undefined ? hsv.v : 0;
	let res = {
		mode: 'hwb',
		w: (1 - s) * v,
		b: 1 - v
	};
	if (hsv.h !== undefined) res.h = hsv.h;
	if (hsv.alpha !== undefined) res.alpha = hsv.alpha;
	return res;
}

function ParseHwb(color, parsed) {
	if (!parsed || parsed[0] !== 'hwb') {
		return undefined;
	}
	const res = { mode: 'hwb' };
	const [, h, w, b, alpha] = parsed;

	if (h.type !== Tok.None) {
		if (h.type === Tok.Percentage) {
			return undefined;
		}
		res.h = h.value;
	}

	if (w.type !== Tok.None) {
		if (w.type === Tok.Hue) {
			return undefined;
		}
		res.w = w.value / 100;
	}

	if (b.type !== Tok.None) {
		if (b.type === Tok.Hue) {
			return undefined;
		}
		res.b = b.value / 100;
	}

	if (alpha.type !== Tok.None) {
		res.alpha = Math.min(
			1,
			Math.max(
				0,
				alpha.type === Tok.Number ? alpha.value : alpha.value / 100
			)
		);
	}

	return res;
}

const definition$j = {
	mode: 'hwb',

	toMode: {
		rgb: convertHwbToRgb
	},

	fromMode: {
		rgb: convertRgbToHwb
	},

	channels: ['h', 'w', 'b', 'alpha'],

	ranges: {
		h: [0, 360]
	},

	gamut: 'rgb',

	parse: [ParseHwb],
	serialize: c =>
		`hwb(${c.h !== undefined ? c.h : 'none'} ${
			c.w !== undefined ? c.w * 100 + '%' : 'none'
		} ${c.b !== undefined ? c.b * 100 + '%' : 'none'}${
			c.alpha < 1 ? ` / ${c.alpha}` : ''
		})`,

	interpolate: {
		h: { use: interpolatorLinear, fixup: fixupHueShorter },
		w: interpolatorLinear,
		b: interpolatorLinear,
		alpha: { use: interpolatorLinear, fixup: fixupAlpha }
	},

	difference: {
		h: differenceHueNaive
	},

	average: {
		h: averageAngle
	}
};

/*
	Relative XYZ has Y=1 for media white,
	BT.2048 says media white Y=203 (at PQ 58).
	See: https://www.itu.int/dms_pub/itu-r/opb/rep/R-REP-BT.2408-3-2019-PDF-E.pdf
*/
const YW = 203;

/*
	https://en.wikipedia.org/wiki/Transfer_functions_in_imaging
*/

const M1 = 0.1593017578125;
const M2 = 78.84375;
const C1 = 0.8359375;
const C2 = 18.8515625;
const C3 = 18.6875;

/*
	Perceptual Quantizer, as defined in Rec. BT 2100-2 (2018)

	* https://www.itu.int/rec/R-REC-BT.2100-2-201807-I/en
	* https://en.wikipedia.org/wiki/Perceptual_quantizer
*/

/* PQ EOTF, defined for `v` in [0,1]. */
function transferPqDecode(v) {
	if (v < 0) return 0;
	const c = Math.pow(v, 1 / M2);
	return 1e4 * Math.pow(Math.max(0, c - C1) / (C2 - C3 * c), 1 / M1);
}

/* PQ EOTF^-1, defined for `v` in [0, 1e4]. */
function transferPqEncode(v) {
	if (v < 0) return 0;
	const c = Math.pow(v / 1e4, M1);
	return Math.pow((C1 + C2 * c) / (1 + C3 * c), M2);
}

const toRel = c => Math.max(c / YW, 0);

const convertItpToXyz65 = ({ i, t, p, alpha }) => {
	if (i === undefined) i = 0;
	if (t === undefined) t = 0;
	if (p === undefined) p = 0;

	const l = transferPqDecode(
		i + 0.008609037037932761 * t + 0.11102962500302593 * p
	);
	const m = transferPqDecode(
		i - 0.00860903703793275 * t - 0.11102962500302599 * p
	);
	const s = transferPqDecode(
		i + 0.5600313357106791 * t - 0.32062717498731885 * p
	);

	const res = {
		mode: 'xyz65',
		x: toRel(
			2.0701522183894219 * l -
				1.3263473389671556 * m +
				0.2066510476294051 * s
		),
		y: toRel(
			0.3647385209748074 * l +
				0.680566024947227 * m -
				0.0453045459220346 * s
		),
		z: toRel(
			-0.049747207535812 * l -
				0.0492609666966138 * m +
				1.1880659249923042 * s
		)
	};

	if (alpha !== undefined) {
		res.alpha = alpha;
	}

	return res;
};

const toAbs = (c = 0) => Math.max(c * YW, 0);

const convertXyz65ToItp = ({ x, y, z, alpha }) => {
	const absX = toAbs(x);
	const absY = toAbs(y);
	const absZ = toAbs(z);
	const l = transferPqEncode(
		0.3592832590121217 * absX +
			0.6976051147779502 * absY -
			0.0358915932320289 * absZ
	);
	const m = transferPqEncode(
		-0.1920808463704995 * absX +
			1.1004767970374323 * absY +
			0.0753748658519118 * absZ
	);
	const s = transferPqEncode(
		0.0070797844607477 * absX +
			0.0748396662186366 * absY +
			0.8433265453898765 * absZ
	);

	const i = 0.5 * l + 0.5 * m;
	const t = 1.61376953125 * l - 3.323486328125 * m + 1.709716796875 * s;
	const p = 4.378173828125 * l - 4.24560546875 * m - 0.132568359375 * s;

	const res = { mode: 'itp', i, t, p };
	if (alpha !== undefined) {
		res.alpha = alpha;
	}

	return res;
};

/*
  ICtCp (or ITP) color space, as defined in ITU-R Recommendation BT.2100.

  ICtCp is drafted to be supported in CSS within
  [CSS Color HDR Module Level 1](https://drafts.csswg.org/css-color-hdr/#ICtCp) spec.
*/

const definition$i = {
	mode: 'itp',
	channels: ['i', 't', 'p', 'alpha'],
	parse: ['--ictcp'],
	serialize: '--ictcp',

	toMode: {
		xyz65: convertItpToXyz65,
		rgb: color => convertXyz65ToRgb(convertItpToXyz65(color))
	},

	fromMode: {
		xyz65: convertXyz65ToItp,
		rgb: color => convertXyz65ToItp(convertRgbToXyz65(color))
	},

	ranges: {
		i: [0, 0.581],
		t: [-0.369, 0.272],
		p: [-0.164, 0.331]
	},

	interpolate: {
		i: interpolatorLinear,
		t: interpolatorLinear,
		p: interpolatorLinear,
		alpha: { use: interpolatorLinear, fixup: fixupAlpha }
	}
};

const p$1 = 134.03437499999998; // = 1.7 * 2523 / Math.pow(2, 5);
const d0$1 = 1.6295499532821566e-11;

/* 
	The encoding function is derived from Perceptual Quantizer.
*/
const jabPqEncode = v => {
	if (v < 0) return 0;
	let vn = Math.pow(v / 10000, M1);
	return Math.pow((C1 + C2 * vn) / (1 + C3 * vn), p$1);
};

// Convert to Absolute XYZ
const abs = (v = 0) => Math.max(v * 203, 0);

const convertXyz65ToJab = ({ x, y, z, alpha }) => {
	x = abs(x);
	y = abs(y);
	z = abs(z);

	let xp = 1.15 * x - 0.15 * z;
	let yp = 0.66 * y + 0.34 * x;

	let l = jabPqEncode(0.41478972 * xp + 0.579999 * yp + 0.014648 * z);
	let m = jabPqEncode(-0.20151 * xp + 1.120649 * yp + 0.0531008 * z);
	let s = jabPqEncode(-0.0166008 * xp + 0.2648 * yp + 0.6684799 * z);

	let i = (l + m) / 2;

	let res = {
		mode: 'jab',
		j: (0.44 * i) / (1 - 0.56 * i) - d0$1,
		a: 3.524 * l - 4.066708 * m + 0.542708 * s,
		b: 0.199076 * l + 1.096799 * m - 1.295875 * s
	};

	if (alpha !== undefined) {
		res.alpha = alpha;
	}

	return res;
};

const p = 134.03437499999998; // = 1.7 * 2523 / Math.pow(2, 5);
const d0 = 1.6295499532821566e-11;

/* 
	The encoding function is derived from Perceptual Quantizer.
*/
const jabPqDecode = v => {
	if (v < 0) return 0;
	let vp = Math.pow(v, 1 / p);
	return 10000 * Math.pow((C1 - vp) / (C3 * vp - C2), 1 / M1);
};

const rel = v => v / 203;

const convertJabToXyz65 = ({ j, a, b, alpha }) => {
	if (j === undefined) j = 0;
	if (a === undefined) a = 0;
	if (b === undefined) b = 0;
	let i = (j + d0) / (0.44 + 0.56 * (j + d0));

	let l = jabPqDecode(i + 0.13860504 * a + 0.058047316 * b);
	let m = jabPqDecode(i - 0.13860504 * a - 0.058047316 * b);
	let s = jabPqDecode(i - 0.096019242 * a - 0.8118919 * b);

	let res = {
		mode: 'xyz65',
		x: rel(
			1.661373024652174 * l -
				0.914523081304348 * m +
				0.23136208173913045 * s
		),
		y: rel(
			-0.3250758611844533 * l +
				1.571847026732543 * m -
				0.21825383453227928 * s
		),
		z: rel(-0.090982811 * l - 0.31272829 * m + 1.5227666 * s)
	};

	if (alpha !== undefined) {
		res.alpha = alpha;
	}

	return res;
};

/*
	Convert sRGB to JzAzBz.

	For achromatic sRGB colors, adjust the equivalent JzAzBz color
	to be achromatic as well, insteading of having a very slight chroma.
 */


const convertRgbToJab = rgb => {
	let res = convertXyz65ToJab(convertRgbToXyz65(rgb));
	if (rgb.r === rgb.b && rgb.b === rgb.g) {
		res.a = res.b = 0;
	}
	return res;
};

const convertJabToRgb = color => convertXyz65ToRgb(convertJabToXyz65(color));

/*
	The JzAzBz color space.

	Based on:

	Muhammad Safdar, Guihua Cui, Youn Jin Kim, and Ming Ronnier Luo, 
	"Perceptually uniform color space for image signals 
	including high dynamic range and wide gamut," 
	Opt. Express 25, 15131-15151 (2017) 

	https://doi.org/10.1364/OE.25.015131
 */


const definition$h = {
	mode: 'jab',
	channels: ['j', 'a', 'b', 'alpha'],

	parse: ['--jzazbz'],
	serialize: '--jzazbz',

	fromMode: {
		rgb: convertRgbToJab,
		xyz65: convertXyz65ToJab
	},

	toMode: {
		rgb: convertJabToRgb,
		xyz65: convertJabToXyz65
	},

	ranges: {
		j: [0, 0.222],
		a: [-0.109, 0.129],
		b: [-0.185, 0.134]
	},

	interpolate: {
		j: interpolatorLinear,
		a: interpolatorLinear,
		b: interpolatorLinear,
		alpha: { use: interpolatorLinear, fixup: fixupAlpha }
	}
};

const convertJabToJch = ({ j, a, b, alpha }) => {
	if (a === undefined) a = 0;
	if (b === undefined) b = 0;
	let c = Math.sqrt(a * a + b * b);
	let res = {
		mode: 'jch',
		j,
		c
	};
	if (c) {
		res.h = normalizeHue((Math.atan2(b, a) * 180) / Math.PI);
	}
	if (alpha !== undefined) {
		res.alpha = alpha;
	}
	return res;
};

const convertJchToJab = ({ j, c, h, alpha }) => {
	if (h === undefined) h = 0;
	let res = {
		mode: 'jab',
		j,
		a: c ? c * Math.cos((h / 180) * Math.PI) : 0,
		b: c ? c * Math.sin((h / 180) * Math.PI) : 0
	};
	if (alpha !== undefined) res.alpha = alpha;
	return res;
};

const definition$g = {
	mode: 'jch',

	parse: ['--jzczhz'],
	serialize: '--jzczhz',

	toMode: {
		jab: convertJchToJab,
		rgb: c => convertJabToRgb(convertJchToJab(c))
	},

	fromMode: {
		rgb: c => convertJabToJch(convertRgbToJab(c)),
		jab: convertJabToJch
	},

	channels: ['j', 'c', 'h', 'alpha'],

	ranges: {
		j: [0, 0.221],
		c: [0, 0.19],
		h: [0, 360]
	},

	interpolate: {
		h: { use: interpolatorLinear, fixup: fixupHueShorter },
		c: interpolatorLinear,
		j: interpolatorLinear,
		alpha: { use: interpolatorLinear, fixup: fixupAlpha }
	},

	difference: {
		h: differenceHueChroma
	},

	average: {
		h: averageAngle
	}
};

const k = Math.pow(29, 3) / Math.pow(3, 3);
const e = Math.pow(6, 3) / Math.pow(29, 3);

let fn = v => (Math.pow(v, 3) > e ? Math.pow(v, 3) : (116 * v - 16) / k);

const convertLabToXyz50 = ({ l, a, b, alpha }) => {
	if (l === undefined) l = 0;
	if (a === undefined) a = 0;
	if (b === undefined) b = 0;
	let fy = (l + 16) / 116;
	let fx = a / 500 + fy;
	let fz = fy - b / 200;

	let res = {
		mode: 'xyz50',
		x: fn(fx) * D50.X,
		y: fn(fy) * D50.Y,
		z: fn(fz) * D50.Z
	};

	if (alpha !== undefined) {
		res.alpha = alpha;
	}

	return res;
};

/*
	CIE XYZ D50 values to sRGB.

	References:
		* https://drafts.csswg.org/css-color/#color-conversion-code
		* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
*/


const convertXyz50ToRgb = ({ x, y, z, alpha }) => {
	if (x === undefined) x = 0;
	if (y === undefined) y = 0;
	if (z === undefined) z = 0;
	let res = convertLrgbToRgb({
		r:
			x * 3.1341359569958707 -
			y * 1.6173863321612538 -
			0.4906619460083532 * z,
		g:
			x * -0.978795502912089 +
			y * 1.916254567259524 +
			0.03344273116131949 * z,
		b:
			x * 0.07195537988411677 -
			y * 0.2289768264158322 +
			1.405386058324125 * z
	});
	if (alpha !== undefined) {
		res.alpha = alpha;
	}
	return res;
};

const convertLabToRgb = lab => convertXyz50ToRgb(convertLabToXyz50(lab));

/*
	Convert sRGB values to CIE XYZ D50

	References:
		* https://drafts.csswg.org/css-color/#color-conversion-code
		* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
	
*/


const convertRgbToXyz50 = rgb => {
	let { r, g, b, alpha } = convertRgbToLrgb(rgb);
	let res = {
		mode: 'xyz50',
		x:
			0.436065742824811 * r +
			0.3851514688337912 * g +
			0.14307845442264197 * b,
		y:
			0.22249319175623702 * r +
			0.7168870538238823 * g +
			0.06061979053616537 * b,
		z:
			0.013923904500943465 * r +
			0.09708128566574634 * g +
			0.7140993584005155 * b
	};
	if (alpha !== undefined) {
		res.alpha = alpha;
	}
	return res;
};

const f = value => (value > e ? Math.cbrt(value) : (k * value + 16) / 116);

const convertXyz50ToLab = ({ x, y, z, alpha }) => {
	if (x === undefined) x = 0;
	if (y === undefined) y = 0;
	if (z === undefined) z = 0;
	let f0 = f(x / D50.X);
	let f1 = f(y / D50.Y);
	let f2 = f(z / D50.Z);

	let res = {
		mode: 'lab',
		l: 116 * f1 - 16,
		a: 500 * (f0 - f1),
		b: 200 * (f1 - f2)
	};

	if (alpha !== undefined) {
		res.alpha = alpha;
	}

	return res;
};

const convertRgbToLab = rgb => {
	let res = convertXyz50ToLab(convertRgbToXyz50(rgb));

	// Fixes achromatic RGB colors having a _slight_ chroma due to floating-point errors
	// and approximated computations in sRGB <-> CIELab.
	// See: https://github.com/d3/d3-color/pull/46
	if (rgb.r === rgb.b && rgb.b === rgb.g) {
		res.a = res.b = 0;
	}
	return res;
};

function parseLab(color, parsed) {
	if (!parsed || parsed[0] !== 'lab') {
		return undefined;
	}
	const res = { mode: 'lab' };
	const [, l, a, b, alpha] = parsed;
	if (l.type === Tok.Hue || a.type === Tok.Hue || b.type === Tok.Hue) {
		return undefined;
	}
	if (l.type !== Tok.None) {
		res.l = Math.min(Math.max(0, l.value), 100);
	}
	if (a.type !== Tok.None) {
		res.a = a.type === Tok.Number ? a.value : (a.value * 125) / 100;
	}
	if (b.type !== Tok.None) {
		res.b = b.type === Tok.Number ? b.value : (b.value * 125) / 100;
	}
	if (alpha.type !== Tok.None) {
		res.alpha = Math.min(
			1,
			Math.max(
				0,
				alpha.type === Tok.Number ? alpha.value : alpha.value / 100
			)
		);
	}

	return res;
}

const definition$f = {
	mode: 'lab',

	toMode: {
		xyz50: convertLabToXyz50,
		rgb: convertLabToRgb
	},

	fromMode: {
		xyz50: convertXyz50ToLab,
		rgb: convertRgbToLab
	},

	channels: ['l', 'a', 'b', 'alpha'],

	ranges: {
		l: [0, 100],
		a: [-100, 100],
		b: [-100, 100]
	},

	parse: [parseLab],
	serialize: c =>
		`lab(${c.l !== undefined ? c.l : 'none'} ${
			c.a !== undefined ? c.a : 'none'
		} ${c.b !== undefined ? c.b : 'none'}${
			c.alpha < 1 ? ` / ${c.alpha}` : ''
		})`,

	interpolate: {
		l: interpolatorLinear,
		a: interpolatorLinear,
		b: interpolatorLinear,
		alpha: { use: interpolatorLinear, fixup: fixupAlpha }
	}
};

const definition$e = {
	...definition$f,
	mode: 'lab65',

	parse: ['--lab-d65'],
	serialize: '--lab-d65',

	toMode: {
		xyz65: convertLab65ToXyz65,
		rgb: convertLab65ToRgb
	},

	fromMode: {
		xyz65: convertXyz65ToLab65,
		rgb: convertRgbToLab65
	},

	ranges: {
		l: [0, 100],
		a: [-86.182, 98.234],
		b: [-107.86, 94.477]
	}
};

function parseLch(color, parsed) {
	if (!parsed || parsed[0] !== 'lch') {
		return undefined;
	}
	const res = { mode: 'lch' };
	const [, l, c, h, alpha] = parsed;
	if (l.type !== Tok.None) {
		if (l.type === Tok.Hue) {
			return undefined;
		}
		res.l = Math.min(Math.max(0, l.value), 100);
	}
	if (c.type !== Tok.None) {
		res.c = Math.max(
			0,
			c.type === Tok.Number ? c.value : (c.value * 150) / 100
		);
	}
	if (h.type !== Tok.None) {
		if (h.type === Tok.Percentage) {
			return undefined;
		}
		res.h = h.value;
	}
	if (alpha.type !== Tok.None) {
		res.alpha = Math.min(
			1,
			Math.max(
				0,
				alpha.type === Tok.Number ? alpha.value : alpha.value / 100
			)
		);
	}

	return res;
}

const definition$d = {
	mode: 'lch',

	toMode: {
		lab: convertLchToLab,
		rgb: c => convertLabToRgb(convertLchToLab(c))
	},

	fromMode: {
		rgb: c => convertLabToLch(convertRgbToLab(c)),
		lab: convertLabToLch
	},

	channels: ['l', 'c', 'h', 'alpha'],

	ranges: {
		l: [0, 100],
		c: [0, 150],
		h: [0, 360]
	},

	parse: [parseLch],
	serialize: c =>
		`lch(${c.l !== undefined ? c.l : 'none'} ${
			c.c !== undefined ? c.c : 'none'
		} ${c.h !== undefined ? c.h : 'none'}${
			c.alpha < 1 ? ` / ${c.alpha}` : ''
		})`,

	interpolate: {
		h: { use: interpolatorLinear, fixup: fixupHueShorter },
		c: interpolatorLinear,
		l: interpolatorLinear,
		alpha: { use: interpolatorLinear, fixup: fixupAlpha }
	},

	difference: {
		h: differenceHueChroma
	},

	average: {
		h: averageAngle
	}
};

const definition$c = {
	...definition$d,
	mode: 'lch65',

	parse: ['--lch-d65'],
	serialize: '--lch-d65',

	toMode: {
		lab65: c => convertLchToLab(c, 'lab65'),
		rgb: c => convertLab65ToRgb(convertLchToLab(c, 'lab65'))
	},

	fromMode: {
		rgb: c => convertLabToLch(convertRgbToLab65(c), 'lch65'),
		lab65: c => convertLabToLch(c, 'lch65')
	},

	ranges: {
		l: [0, 100],
		c: [0, 133.807],
		h: [0, 360]
	}
};

const convertLuvToLchuv = ({ l, u, v, alpha }) => {
	if (u === undefined) u = 0;
	if (v === undefined) v = 0;
	let c = Math.sqrt(u * u + v * v);
	let res = {
		mode: 'lchuv',
		l: l,
		c: c
	};
	if (c) {
		res.h = normalizeHue((Math.atan2(v, u) * 180) / Math.PI);
	}
	if (alpha !== undefined) {
		res.alpha = alpha;
	}
	return res;
};

const convertLchuvToLuv = ({ l, c, h, alpha }) => {
	if (h === undefined) h = 0;
	let res = {
		mode: 'luv',
		l: l,
		u: c ? c * Math.cos((h / 180) * Math.PI) : 0,
		v: c ? c * Math.sin((h / 180) * Math.PI) : 0
	};
	if (alpha !== undefined) {
		res.alpha = alpha;
	}
	return res;
};

const u_fn$1 = (x, y, z) => (4 * x) / (x + 15 * y + 3 * z);
const v_fn$1 = (x, y, z) => (9 * y) / (x + 15 * y + 3 * z);

const un$1 = u_fn$1(D50.X, D50.Y, D50.Z);
const vn$1 = v_fn$1(D50.X, D50.Y, D50.Z);

const l_fn = value => (value <= e ? k * value : 116 * Math.cbrt(value) - 16);

const convertXyz50ToLuv = ({ x, y, z, alpha }) => {
	if (x === undefined) x = 0;
	if (y === undefined) y = 0;
	if (z === undefined) z = 0;
	let l = l_fn(y / D50.Y);
	let u = u_fn$1(x, y, z);
	let v = v_fn$1(x, y, z);

	// guard against NaNs produced by `xyz(0 0 0)` black
	if (!isFinite(u) || !isFinite(v)) {
		l = u = v = 0;
	} else {
		u = 13 * l * (u - un$1);
		v = 13 * l * (v - vn$1);
	}

	let res = {
		mode: 'luv',
		l,
		u,
		v
	};

	if (alpha !== undefined) {
		res.alpha = alpha;
	}

	return res;
};

const u_fn = (x, y, z) => (4 * x) / (x + 15 * y + 3 * z);
const v_fn = (x, y, z) => (9 * y) / (x + 15 * y + 3 * z);

const un = u_fn(D50.X, D50.Y, D50.Z);
const vn = v_fn(D50.X, D50.Y, D50.Z);

const convertLuvToXyz50 = ({ l, u, v, alpha }) => {
	if (l === undefined) l = 0;
	if (l === 0) {
		return { mode: 'xyz50', x: 0, y: 0, z: 0 };
	}

	if (u === undefined) u = 0;
	if (v === undefined) v = 0;

	let up = u / (13 * l) + un;
	let vp = v / (13 * l) + vn;
	let y = D50.Y * (l <= 8 ? l / k : Math.pow((l + 16) / 116, 3));
	let x = (y * (9 * up)) / (4 * vp);
	let z = (y * (12 - 3 * up - 20 * vp)) / (4 * vp);

	let res = { mode: 'xyz50', x, y, z };
	if (alpha !== undefined) {
		res.alpha = alpha;
	}

	return res;
};

/*
	CIELChuv color space
	--------------------

	Reference: 

		https://en.wikipedia.org/wiki/CIELUV
 */


const convertRgbToLchuv = rgb =>
	convertLuvToLchuv(convertXyz50ToLuv(convertRgbToXyz50(rgb)));
const convertLchuvToRgb = lchuv =>
	convertXyz50ToRgb(convertLuvToXyz50(convertLchuvToLuv(lchuv)));

const definition$b = {
	mode: 'lchuv',

	toMode: {
		luv: convertLchuvToLuv,
		rgb: convertLchuvToRgb
	},

	fromMode: {
		rgb: convertRgbToLchuv,
		luv: convertLuvToLchuv
	},

	channels: ['l', 'c', 'h', 'alpha'],

	parse: ['--lchuv'],
	serialize: '--lchuv',

	ranges: {
		l: [0, 100],
		c: [0, 176.956],
		h: [0, 360]
	},

	interpolate: {
		h: { use: interpolatorLinear, fixup: fixupHueShorter },
		c: interpolatorLinear,
		l: interpolatorLinear,
		alpha: { use: interpolatorLinear, fixup: fixupAlpha }
	},

	difference: {
		h: differenceHueChroma
	},

	average: {
		h: averageAngle
	}
};

const definition$a = {
	...definition$r,
	mode: 'lrgb',

	toMode: {
		rgb: convertLrgbToRgb
	},

	fromMode: {
		rgb: convertRgbToLrgb
	},

	parse: ['srgb-linear'],
	serialize: 'srgb-linear'
};

/*
	CIELUV color space
	------------------

	Reference: 

		https://en.wikipedia.org/wiki/CIELUV
 */


const definition$9 = {
	mode: 'luv',

	toMode: {
		xyz50: convertLuvToXyz50,
		rgb: luv => convertXyz50ToRgb(convertLuvToXyz50(luv))
	},

	fromMode: {
		xyz50: convertXyz50ToLuv,
		rgb: rgb => convertXyz50ToLuv(convertRgbToXyz50(rgb))
	},

	channels: ['l', 'u', 'v', 'alpha'],

	parse: ['--luv'],
	serialize: '--luv',

	ranges: {
		l: [0, 100],
		u: [-84.936, 175.042],
		v: [-125.882, 87.243]
	},

	interpolate: {
		l: interpolatorLinear,
		u: interpolatorLinear,
		v: interpolatorLinear,
		alpha: { use: interpolatorLinear, fixup: fixupAlpha }
	}
};

const convertLrgbToOklab = ({ r, g, b, alpha }) => {
	if (r === undefined) r = 0;
	if (g === undefined) g = 0;
	if (b === undefined) b = 0;
	let L = Math.cbrt(
		0.41222147079999993 * r + 0.5363325363 * g + 0.0514459929 * b
	);
	let M = Math.cbrt(
		0.2119034981999999 * r + 0.6806995450999999 * g + 0.1073969566 * b
	);
	let S = Math.cbrt(
		0.08830246189999998 * r + 0.2817188376 * g + 0.6299787005000002 * b
	);

	let res = {
		mode: 'oklab',
		l: 0.2104542553 * L + 0.793617785 * M - 0.0040720468 * S,
		a: 1.9779984951 * L - 2.428592205 * M + 0.4505937099 * S,
		b: 0.0259040371 * L + 0.7827717662 * M - 0.808675766 * S
	};

	if (alpha !== undefined) {
		res.alpha = alpha;
	}

	return res;
};

const convertRgbToOklab = rgb => {
	let res = convertLrgbToOklab(convertRgbToLrgb(rgb));
	if (rgb.r === rgb.b && rgb.b === rgb.g) {
		res.a = res.b = 0;
	}
	return res;
};

const convertOklabToLrgb = ({ l, a, b, alpha }) => {
	if (l === undefined) l = 0;
	if (a === undefined) a = 0;
	if (b === undefined) b = 0;
	let L = Math.pow(
		l * 0.99999999845051981432 +
			0.39633779217376785678 * a +
			0.21580375806075880339 * b,
		3
	);
	let M = Math.pow(
		l * 1.0000000088817607767 -
			0.1055613423236563494 * a -
			0.063854174771705903402 * b,
		3
	);
	let S = Math.pow(
		l * 1.0000000546724109177 -
			0.089484182094965759684 * a -
			1.2914855378640917399 * b,
		3
	);

	let res = {
		mode: 'lrgb',
		r:
			4.076741661347994 * L -
			3.307711590408193 * M +
			0.230969928729428 * S,
		g:
			-1.2684380040921763 * L +
			2.6097574006633715 * M -
			0.3413193963102197 * S,
		b:
			-0.004196086541837188 * L -
			0.7034186144594493 * M +
			1.7076147009309444 * S
	};

	if (alpha !== undefined) {
		res.alpha = alpha;
	}

	return res;
};

const convertOklabToRgb = c => convertLrgbToRgb(convertOklabToLrgb(c));

/*
	Adapted from code by Björn Ottosson,
	released under the MIT license:

	Copyright (c) 2021 Björn Ottosson

	Permission is hereby granted, free of charge, to any person obtaining a copy of
	this software and associated documentation files (the "Software"), to deal in
	the Software without restriction, including without limitation the rights to
	use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies
	of the Software, and to permit persons to whom the Software is furnished to do
	so, subject to the following conditions:

	The above copyright notice and this permission notice shall be included in all
	copies or substantial portions of the Software.

	THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
	IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
	FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
	AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
	LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
	OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
	SOFTWARE.
 */


function toe(x) {
	const k_1 = 0.206;
	const k_2 = 0.03;
	const k_3 = (1 + k_1) / (1 + k_2);
	return (
		0.5 *
		(k_3 * x -
			k_1 +
			Math.sqrt((k_3 * x - k_1) * (k_3 * x - k_1) + 4 * k_2 * k_3 * x))
	);
}

function toe_inv(x) {
	const k_1 = 0.206;
	const k_2 = 0.03;
	const k_3 = (1 + k_1) / (1 + k_2);
	return (x * x + k_1 * x) / (k_3 * (x + k_2));
}

// Finds the maximum saturation possible for a given hue that fits in sRGB
// Saturation here is defined as S = C/L
// a and b must be normalized so a^2 + b^2 == 1
function compute_max_saturation(a, b) {
	// Max saturation will be when one of r, g or b goes below zero.

	// Select different coefficients depending on which component goes below zero first
	let k0, k1, k2, k3, k4, wl, wm, ws;

	if (-1.88170328 * a - 0.80936493 * b > 1) {
		// Red component
		k0 = 1.19086277;
		k1 = 1.76576728;
		k2 = 0.59662641;
		k3 = 0.75515197;
		k4 = 0.56771245;
		wl = 4.0767416621;
		wm = -3.3077115913;
		ws = 0.2309699292;
	} else if (1.81444104 * a - 1.19445276 * b > 1) {
		// Green component
		k0 = 0.73956515;
		k1 = -0.45954404;
		k2 = 0.08285427;
		k3 = 0.1254107;
		k4 = 0.14503204;
		wl = -1.2684380046;
		wm = 2.6097574011;
		ws = -0.3413193965;
	} else {
		// Blue component
		k0 = 1.35733652;
		k1 = -915799e-8;
		k2 = -1.1513021;
		k3 = -0.50559606;
		k4 = 692167e-8;
		wl = -0.0041960863;
		wm = -0.7034186147;
		ws = 1.707614701;
	}

	// Approximate max saturation using a polynomial:
	let S = k0 + k1 * a + k2 * b + k3 * a * a + k4 * a * b;

	// Do one step Halley's method to get closer
	// this gives an error less than 10e6, except for some blue hues where the dS/dh is close to infinite
	// this should be sufficient for most applications, otherwise do two/three steps

	let k_l = 0.3963377774 * a + 0.2158037573 * b;
	let k_m = -0.1055613458 * a - 0.0638541728 * b;
	let k_s = -0.0894841775 * a - 1.291485548 * b;

	{
		let l_ = 1 + S * k_l;
		let m_ = 1 + S * k_m;
		let s_ = 1 + S * k_s;

		let l = l_ * l_ * l_;
		let m = m_ * m_ * m_;
		let s = s_ * s_ * s_;

		let l_dS = 3 * k_l * l_ * l_;
		let m_dS = 3 * k_m * m_ * m_;
		let s_dS = 3 * k_s * s_ * s_;

		let l_dS2 = 6 * k_l * k_l * l_;
		let m_dS2 = 6 * k_m * k_m * m_;
		let s_dS2 = 6 * k_s * k_s * s_;

		let f = wl * l + wm * m + ws * s;
		let f1 = wl * l_dS + wm * m_dS + ws * s_dS;
		let f2 = wl * l_dS2 + wm * m_dS2 + ws * s_dS2;

		S = S - (f * f1) / (f1 * f1 - 0.5 * f * f2);
	}

	return S;
}

function find_cusp(a, b) {
	// First, find the maximum saturation (saturation S = C/L)
	let S_cusp = compute_max_saturation(a, b);

	// Convert to linear sRGB to find the first point where at least one of r,g or b >= 1:
	let rgb = convertOklabToLrgb({ l: 1, a: S_cusp * a, b: S_cusp * b });
	let L_cusp = Math.cbrt(1 / Math.max(rgb.r, rgb.g, rgb.b));
	let C_cusp = L_cusp * S_cusp;

	return [L_cusp, C_cusp];
}

// Finds intersection of the line defined by
// L = L0 * (1 - t) + t * L1;
// C = t * C1;
// a and b must be normalized so a^2 + b^2 == 1
function find_gamut_intersection(a, b, L1, C1, L0, cusp = null) {
	if (!cusp) {
		// Find the cusp of the gamut triangle
		cusp = find_cusp(a, b);
	}

	// Find the intersection for upper and lower half seprately
	let t;
	if ((L1 - L0) * cusp[1] - (cusp[0] - L0) * C1 <= 0) {
		// Lower half

		t = (cusp[1] * L0) / (C1 * cusp[0] + cusp[1] * (L0 - L1));
	} else {
		// Upper half

		// First intersect with triangle
		t = (cusp[1] * (L0 - 1)) / (C1 * (cusp[0] - 1) + cusp[1] * (L0 - L1));

		// Then one step Halley's method
		{
			let dL = L1 - L0;
			let dC = C1;

			let k_l = 0.3963377774 * a + 0.2158037573 * b;
			let k_m = -0.1055613458 * a - 0.0638541728 * b;
			let k_s = -0.0894841775 * a - 1.291485548 * b;

			let l_dt = dL + dC * k_l;
			let m_dt = dL + dC * k_m;
			let s_dt = dL + dC * k_s;

			// If higher accuracy is required, 2 or 3 iterations of the following block can be used:
			{
				let L = L0 * (1 - t) + t * L1;
				let C = t * C1;

				let l_ = L + C * k_l;
				let m_ = L + C * k_m;
				let s_ = L + C * k_s;

				let l = l_ * l_ * l_;
				let m = m_ * m_ * m_;
				let s = s_ * s_ * s_;

				let ldt = 3 * l_dt * l_ * l_;
				let mdt = 3 * m_dt * m_ * m_;
				let sdt = 3 * s_dt * s_ * s_;

				let ldt2 = 6 * l_dt * l_dt * l_;
				let mdt2 = 6 * m_dt * m_dt * m_;
				let sdt2 = 6 * s_dt * s_dt * s_;

				let r =
					4.0767416621 * l - 3.3077115913 * m + 0.2309699292 * s - 1;
				let r1 =
					4.0767416621 * ldt -
					3.3077115913 * mdt +
					0.2309699292 * sdt;
				let r2 =
					4.0767416621 * ldt2 -
					3.3077115913 * mdt2 +
					0.2309699292 * sdt2;

				let u_r = r1 / (r1 * r1 - 0.5 * r * r2);
				let t_r = -r * u_r;

				let g =
					-1.2684380046 * l + 2.6097574011 * m - 0.3413193965 * s - 1;
				let g1 =
					-1.2684380046 * ldt +
					2.6097574011 * mdt -
					0.3413193965 * sdt;
				let g2 =
					-1.2684380046 * ldt2 +
					2.6097574011 * mdt2 -
					0.3413193965 * sdt2;

				let u_g = g1 / (g1 * g1 - 0.5 * g * g2);
				let t_g = -g * u_g;

				let b =
					-0.0041960863 * l - 0.7034186147 * m + 1.707614701 * s - 1;
				let b1 =
					-0.0041960863 * ldt -
					0.7034186147 * mdt +
					1.707614701 * sdt;
				let b2 =
					-0.0041960863 * ldt2 -
					0.7034186147 * mdt2 +
					1.707614701 * sdt2;

				let u_b = b1 / (b1 * b1 - 0.5 * b * b2);
				let t_b = -b * u_b;

				t_r = u_r >= 0 ? t_r : 10e5;
				t_g = u_g >= 0 ? t_g : 10e5;
				t_b = u_b >= 0 ? t_b : 10e5;

				t += Math.min(t_r, Math.min(t_g, t_b));
			}
		}
	}

	return t;
}

function get_ST_max(a_, b_, cusp = null) {
	if (!cusp) {
		cusp = find_cusp(a_, b_);
	}
	let L = cusp[0];
	let C = cusp[1];
	return [C / L, C / (1 - L)];
}

function get_Cs(L, a_, b_) {
	let cusp = find_cusp(a_, b_);

	let C_max = find_gamut_intersection(a_, b_, L, 1, L, cusp);
	let ST_max = get_ST_max(a_, b_, cusp);

	let S_mid =
		0.11516993 +
		1 /
			(7.4477897 +
				4.1590124 * b_ +
				a_ *
					(-2.19557347 +
						1.75198401 * b_ +
						a_ *
							(-2.13704948 -
								10.02301043 * b_ +
								a_ *
									(-4.24894561 +
										5.38770819 * b_ +
										4.69891013 * a_))));

	let T_mid =
		0.11239642 +
		1 /
			(1.6132032 -
				0.68124379 * b_ +
				a_ *
					(0.40370612 +
						0.90148123 * b_ +
						a_ *
							(-0.27087943 +
								0.6122399 * b_ +
								a_ *
									(299215e-8 -
										0.45399568 * b_ -
										0.14661872 * a_))));

	let k = C_max / Math.min(L * ST_max[0], (1 - L) * ST_max[1]);

	let C_a = L * S_mid;
	let C_b = (1 - L) * T_mid;
	let C_mid =
		0.9 *
		k *
		Math.sqrt(
			Math.sqrt(
				1 / (1 / (C_a * C_a * C_a * C_a) + 1 / (C_b * C_b * C_b * C_b))
			)
		);

	C_a = L * 0.4;
	C_b = (1 - L) * 0.8;
	let C_0 = Math.sqrt(1 / (1 / (C_a * C_a) + 1 / (C_b * C_b)));
	return [C_0, C_mid, C_max];
}

/*
	Adapted from code by Björn Ottosson,
	released under the MIT license:

	Copyright (c) 2021 Björn Ottosson

	Permission is hereby granted, free of charge, to any person obtaining a copy of
	this software and associated documentation files (the "Software"), to deal in
	the Software without restriction, including without limitation the rights to
	use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies
	of the Software, and to permit persons to whom the Software is furnished to do
	so, subject to the following conditions:

	The above copyright notice and this permission notice shall be included in all
	copies or substantial portions of the Software.

	THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
	IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
	FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
	AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
	LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
	OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
	SOFTWARE.
 */


function convertOklabToOkhsl(lab) {
	const l = lab.l !== undefined ? lab.l : 0;
	const a = lab.a !== undefined ? lab.a : 0;
	const b = lab.b !== undefined ? lab.b : 0;

	const ret = { mode: 'okhsl', l: toe(l) };

	if (lab.alpha !== undefined) {
		ret.alpha = lab.alpha;
	}
	let c = Math.sqrt(a * a + b * b);
	if (!c) {
		ret.s = 0;
		return ret;
	}
	let [C_0, C_mid, C_max] = get_Cs(l, a / c, b / c);
	let s;
	if (c < C_mid) {
		let k_0 = 0;
		let k_1 = 0.8 * C_0;
		let k_2 = 1 - k_1 / C_mid;
		let t = (c - k_0) / (k_1 + k_2 * (c - k_0));
		s = t * 0.8;
	} else {
		let k_0 = C_mid;
		let k_1 = (0.2 * C_mid * C_mid * 1.25 * 1.25) / C_0;
		let k_2 = 1 - k_1 / (C_max - C_mid);
		let t = (c - k_0) / (k_1 + k_2 * (c - k_0));
		s = 0.8 + 0.2 * t;
	}
	if (s) {
		ret.s = s;
		ret.h = normalizeHue((Math.atan2(b, a) * 180) / Math.PI);
	}
	return ret;
}

/*
	Adapted from code by Björn Ottosson,
	released under the MIT license:

	Copyright (c) 2021 Björn Ottosson

	Permission is hereby granted, free of charge, to any person obtaining a copy of
	this software and associated documentation files (the "Software"), to deal in
	the Software without restriction, including without limitation the rights to
	use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies
	of the Software, and to permit persons to whom the Software is furnished to do
	so, subject to the following conditions:

	The above copyright notice and this permission notice shall be included in all
	copies or substantial portions of the Software.

	THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
	IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
	FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
	AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
	LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
	OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
	SOFTWARE.
 */


function convertOkhslToOklab(hsl) {
	let h = hsl.h !== undefined ? hsl.h : 0;
	let s = hsl.s !== undefined ? hsl.s : 0;
	let l = hsl.l !== undefined ? hsl.l : 0;

	const ret = { mode: 'oklab', l: toe_inv(l) };

	if (hsl.alpha !== undefined) {
		ret.alpha = hsl.alpha;
	}

	if (!s || l === 1) {
		ret.a = ret.b = 0;
		return ret;
	}

	let a_ = Math.cos((h / 180) * Math.PI);
	let b_ = Math.sin((h / 180) * Math.PI);
	let [C_0, C_mid, C_max] = get_Cs(ret.l, a_, b_);
	let t, k_0, k_1, k_2;
	if (s < 0.8) {
		t = 1.25 * s;
		k_0 = 0;
		k_1 = 0.8 * C_0;
		k_2 = 1 - k_1 / C_mid;
	} else {
		t = 5 * (s - 0.8);
		k_0 = C_mid;
		k_1 = (0.2 * C_mid * C_mid * 1.25 * 1.25) / C_0;
		k_2 = 1 - k_1 / (C_max - C_mid);
	}
	let C = k_0 + (t * k_1) / (1 - k_2 * t);
	ret.a = C * a_;
	ret.b = C * b_;

	return ret;
}

const modeOkhsl = {
	...definition$l,
	mode: 'okhsl',
	channels: ['h', 's', 'l', 'alpha'],
	parse: ['--okhsl'],
	serialize: '--okhsl',
	fromMode: {
		oklab: convertOklabToOkhsl,
		rgb: c => convertOklabToOkhsl(convertRgbToOklab(c))
	},
	toMode: {
		oklab: convertOkhslToOklab,
		rgb: c => convertOklabToRgb(convertOkhslToOklab(c))
	}
};

/*
	Adapted from code by Björn Ottosson,
	released under the MIT license:

	Copyright (c) 2021 Björn Ottosson

	Permission is hereby granted, free of charge, to any person obtaining a copy of
	this software and associated documentation files (the "Software"), to deal in
	the Software without restriction, including without limitation the rights to
	use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies
	of the Software, and to permit persons to whom the Software is furnished to do
	so, subject to the following conditions:

	The above copyright notice and this permission notice shall be included in all
	copies or substantial portions of the Software.

	THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
	IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
	FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
	AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
	LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
	OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
	SOFTWARE.
 */


function convertOklabToOkhsv(lab) {
	let l = lab.l !== undefined ? lab.l : 0;
	let a = lab.a !== undefined ? lab.a : 0;
	let b = lab.b !== undefined ? lab.b : 0;

	let c = Math.sqrt(a * a + b * b);

	// TODO: c = 0
	let a_ = c ? a / c : 1;
	let b_ = c ? b / c : 1;

	let [S_max, T] = get_ST_max(a_, b_);
	let S_0 = 0.5;
	let k = 1 - S_0 / S_max;

	let t = T / (c + l * T);
	let L_v = t * l;
	let C_v = t * c;

	let L_vt = toe_inv(L_v);
	let C_vt = (C_v * L_vt) / L_v;

	let rgb_scale = convertOklabToLrgb({ l: L_vt, a: a_ * C_vt, b: b_ * C_vt });
	let scale_L = Math.cbrt(
		1 / Math.max(rgb_scale.r, rgb_scale.g, rgb_scale.b, 0)
	);

	l = l / scale_L;
	c = ((c / scale_L) * toe(l)) / l;
	l = toe(l);

	const ret = {
		mode: 'okhsv',
		s: c ? ((S_0 + T) * C_v) / (T * S_0 + T * k * C_v) : 0,
		v: l ? l / L_v : 0
	};
	if (ret.s) {
		ret.h = normalizeHue((Math.atan2(b, a) * 180) / Math.PI);
	}
	if (lab.alpha !== undefined) {
		ret.alpha = lab.alpha;
	}
	return ret;
}

/*
	Copyright (c) 2021 Björn Ottosson

	Permission is hereby granted, free of charge, to any person obtaining a copy of
	this software and associated documentation files (the "Software"), to deal in
	the Software without restriction, including without limitation the rights to
	use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies
	of the Software, and to permit persons to whom the Software is furnished to do
	so, subject to the following conditions:

	The above copyright notice and this permission notice shall be included in all
	copies or substantial portions of the Software.

	THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
	IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
	FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
	AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
	LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
	OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
	SOFTWARE.
 */


function convertOkhsvToOklab(hsv) {
	const ret = { mode: 'oklab' };
	if (hsv.alpha !== undefined) {
		ret.alpha = hsv.alpha;
	}

	const h = hsv.h !== undefined ? hsv.h : 0;
	const s = hsv.s !== undefined ? hsv.s : 0;
	const v = hsv.v !== undefined ? hsv.v : 0;

	const a_ = Math.cos((h / 180) * Math.PI);
	const b_ = Math.sin((h / 180) * Math.PI);

	const [S_max, T] = get_ST_max(a_, b_);
	const S_0 = 0.5;
	const k = 1 - S_0 / S_max;
	const L_v = 1 - (s * S_0) / (S_0 + T - T * k * s);
	const C_v = (s * T * S_0) / (S_0 + T - T * k * s);

	const L_vt = toe_inv(L_v);
	const C_vt = (C_v * L_vt) / L_v;
	const rgb_scale = convertOklabToLrgb({
		l: L_vt,
		a: a_ * C_vt,
		b: b_ * C_vt
	});
	const scale_L = Math.cbrt(
		1 / Math.max(rgb_scale.r, rgb_scale.g, rgb_scale.b, 0)
	);

	const L_new = toe_inv(v * L_v);
	const C = (C_v * L_new) / L_v;

	ret.l = L_new * scale_L;
	ret.a = C * a_ * scale_L;
	ret.b = C * b_ * scale_L;

	return ret;
}

const modeOkhsv = {
	...definition$k,
	mode: 'okhsv',
	channels: ['h', 's', 'v', 'alpha'],
	parse: ['--okhsv'],
	serialize: '--okhsv',
	fromMode: {
		oklab: convertOklabToOkhsv,
		rgb: c => convertOklabToOkhsv(convertRgbToOklab(c))
	},
	toMode: {
		oklab: convertOkhsvToOklab,
		rgb: c => convertOklabToRgb(convertOkhsvToOklab(c))
	}
};

function parseOklab(color, parsed) {
	if (!parsed || parsed[0] !== 'oklab') {
		return undefined;
	}
	const res = { mode: 'oklab' };
	const [, l, a, b, alpha] = parsed;
	if (l.type === Tok.Hue || a.type === Tok.Hue || b.type === Tok.Hue) {
		return undefined;
	}
	if (l.type !== Tok.None) {
		res.l = Math.min(
			Math.max(0, l.type === Tok.Number ? l.value : l.value / 100),
			1
		);
	}
	if (a.type !== Tok.None) {
		res.a = a.type === Tok.Number ? a.value : (a.value * 0.4) / 100;
	}
	if (b.type !== Tok.None) {
		res.b = b.type === Tok.Number ? b.value : (b.value * 0.4) / 100;
	}
	if (alpha.type !== Tok.None) {
		res.alpha = Math.min(
			1,
			Math.max(
				0,
				alpha.type === Tok.Number ? alpha.value : alpha.value / 100
			)
		);
	}

	return res;
}

/*
	Oklab, a perceptual color space for image processing by Björn Ottosson
	Reference: https://bottosson.github.io/posts/oklab/
 */

const definition$8 = {
	...definition$f,
	mode: 'oklab',

	toMode: {
		lrgb: convertOklabToLrgb,
		rgb: convertOklabToRgb
	},

	fromMode: {
		lrgb: convertLrgbToOklab,
		rgb: convertRgbToOklab
	},

	ranges: {
		l: [0, 1],
		a: [-0.4, 0.4],
		b: [-0.4, 0.4]
	},

	parse: [parseOklab],
	serialize: c =>
		`oklab(${c.l !== undefined ? c.l : 'none'} ${
			c.a !== undefined ? c.a : 'none'
		} ${c.b !== undefined ? c.b : 'none'}${
			c.alpha < 1 ? ` / ${c.alpha}` : ''
		})`
};

function parseOklch(color, parsed) {
	if (!parsed || parsed[0] !== 'oklch') {
		return undefined;
	}
	const res = { mode: 'oklch' };
	const [, l, c, h, alpha] = parsed;
	if (l.type !== Tok.None) {
		if (l.type === Tok.Hue) {
			return undefined;
		}
		res.l = Math.min(
			Math.max(0, l.type === Tok.Number ? l.value : l.value / 100),
			1
		);
	}
	if (c.type !== Tok.None) {
		res.c = Math.max(
			0,
			c.type === Tok.Number ? c.value : (c.value * 0.4) / 100
		);
	}
	if (h.type !== Tok.None) {
		if (h.type === Tok.Percentage) {
			return undefined;
		}
		res.h = h.value;
	}
	if (alpha.type !== Tok.None) {
		res.alpha = Math.min(
			1,
			Math.max(
				0,
				alpha.type === Tok.Number ? alpha.value : alpha.value / 100
			)
		);
	}

	return res;
}

const definition$7 = {
	...definition$d,
	mode: 'oklch',

	toMode: {
		oklab: c => convertLchToLab(c, 'oklab'),
		rgb: c => convertOklabToRgb(convertLchToLab(c, 'oklab'))
	},

	fromMode: {
		rgb: c => convertLabToLch(convertRgbToOklab(c), 'oklch'),
		oklab: c => convertLabToLch(c, 'oklch')
	},

	parse: [parseOklch],
	serialize: c =>
		`oklch(${c.l !== undefined ? c.l : 'none'} ${
			c.c !== undefined ? c.c : 'none'
		} ${c.h !== undefined ? c.h : 'none'}${
			c.alpha < 1 ? ` / ${c.alpha}` : ''
		})`,

	ranges: {
		l: [0, 1],
		c: [0, 0.4],
		h: [0, 360]
	}
};

/*
	Convert Display P3 values to CIE XYZ D65

	References:
		* https://drafts.csswg.org/css-color/#color-conversion-code
		* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
*/


const convertP3ToXyz65 = rgb => {
	let { r, g, b, alpha } = convertRgbToLrgb(rgb);
	let res = {
		mode: 'xyz65',
		x:
			0.486570948648216 * r +
			0.265667693169093 * g +
			0.1982172852343625 * b,
		y:
			0.2289745640697487 * r +
			0.6917385218365062 * g +
			0.079286914093745 * b,
		z: 0.0 * r + 0.0451133818589026 * g + 1.043944368900976 * b
	};
	if (alpha !== undefined) {
		res.alpha = alpha;
	}
	return res;
};

/*
	CIE XYZ D65 values to Display P3.

	References:
		* https://drafts.csswg.org/css-color/#color-conversion-code
		* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
*/


const convertXyz65ToP3 = ({ x, y, z, alpha }) => {
	if (x === undefined) x = 0;
	if (y === undefined) y = 0;
	if (z === undefined) z = 0;
	let res = convertLrgbToRgb(
		{
			r:
				x * 2.4934969119414263 -
				y * 0.9313836179191242 -
				0.402710784450717 * z,
			g:
				x * -0.8294889695615749 +
				y * 1.7626640603183465 +
				0.0236246858419436 * z,
			b:
				x * 0.0358458302437845 -
				y * 0.0761723892680418 +
				0.9568845240076871 * z
		},
		'p3'
	);
	if (alpha !== undefined) {
		res.alpha = alpha;
	}
	return res;
};

const definition$6 = {
	...definition$r,
	mode: 'p3',
	parse: ['display-p3'],
	serialize: 'display-p3',

	fromMode: {
		rgb: color => convertXyz65ToP3(convertRgbToXyz65(color)),
		xyz65: convertXyz65ToP3
	},

	toMode: {
		rgb: color => convertXyz65ToRgb(convertP3ToXyz65(color)),
		xyz65: convertP3ToXyz65
	}
};

/*
	Convert CIE XYZ D50 values to ProPhoto RGB

	References:
		* https://drafts.csswg.org/css-color/#color-conversion-code
		* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
*/

const gamma$1 = v => {
	let abs = Math.abs(v);
	if (abs >= 1 / 512) {
		return Math.sign(v) * Math.pow(abs, 1 / 1.8);
	}
	return 16 * v;
};

const convertXyz50ToProphoto = ({ x, y, z, alpha }) => {
	if (x === undefined) x = 0;
	if (y === undefined) y = 0;
	if (z === undefined) z = 0;
	let res = {
		mode: 'prophoto',
		r: gamma$1(
			x * 1.3457868816471585 -
				y * 0.2555720873797946 -
				0.0511018649755453 * z
		),
		g: gamma$1(
			x * -0.5446307051249019 +
				y * 1.5082477428451466 +
				0.0205274474364214 * z
		),
		b: gamma$1(x * 0.0 + y * 0.0 + 1.2119675456389452 * z)
	};
	if (alpha !== undefined) {
		res.alpha = alpha;
	}
	return res;
};

/*
	Convert ProPhoto RGB values to CIE XYZ D50

	References:
		* https://drafts.csswg.org/css-color/#color-conversion-code
		* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
*/

const linearize$1 = (v = 0) => {
	let abs = Math.abs(v);
	if (abs >= 16 / 512) {
		return Math.sign(v) * Math.pow(abs, 1.8);
	}
	return v / 16;
};

const convertProphotoToXyz50 = prophoto => {
	let r = linearize$1(prophoto.r);
	let g = linearize$1(prophoto.g);
	let b = linearize$1(prophoto.b);
	let res = {
		mode: 'xyz50',
		x:
			0.7977666449006423 * r +
			0.1351812974005331 * g +
			0.0313477341283922 * b,
		y:
			0.2880748288194013 * r +
			0.7118352342418731 * g +
			0.0000899369387256 * b,
		z: 0 * r + 0 * g + 0.8251046025104602 * b
	};
	if (prophoto.alpha !== undefined) {
		res.alpha = prophoto.alpha;
	}
	return res;
};

/*
	ProPhoto RGB Color space

	References:
		* https://en.wikipedia.org/wiki/ProPhoto_RGB_color_space
 */

const definition$5 = {
	...definition$r,
	mode: 'prophoto',
	parse: ['prophoto-rgb'],
	serialize: 'prophoto-rgb',

	fromMode: {
		xyz50: convertXyz50ToProphoto,
		rgb: color => convertXyz50ToProphoto(convertRgbToXyz50(color))
	},

	toMode: {
		xyz50: convertProphotoToXyz50,
		rgb: color => convertXyz50ToRgb(convertProphotoToXyz50(color))
	}
};

/*
	Convert CIE XYZ D65 values to Rec. 2020

	References:
		* https://drafts.csswg.org/css-color/#color-conversion-code
		* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
		* https://www.itu.int/rec/R-REC-BT.2020/en
*/

const α$1 = 1.09929682680944;
const β$1 = 0.018053968510807;
const gamma = v => {
	const abs = Math.abs(v);
	if (abs > β$1) {
		return (Math.sign(v) || 1) * (α$1 * Math.pow(abs, 0.45) - (α$1 - 1));
	}
	return 4.5 * v;
};

const convertXyz65ToRec2020 = ({ x, y, z, alpha }) => {
	if (x === undefined) x = 0;
	if (y === undefined) y = 0;
	if (z === undefined) z = 0;
	let res = {
		mode: 'rec2020',
		r: gamma(
			x * 1.7166511879712683 -
				y * 0.3556707837763925 -
				0.2533662813736599 * z
		),
		g: gamma(
			x * -0.6666843518324893 +
				y * 1.6164812366349395 +
				0.0157685458139111 * z
		),
		b: gamma(
			x * 0.0176398574453108 -
				y * 0.0427706132578085 +
				0.9421031212354739 * z
		)
	};
	if (alpha !== undefined) {
		res.alpha = alpha;
	}
	return res;
};

/*
	Convert Rec. 2020 values to CIE XYZ D65

	References:
		* https://drafts.csswg.org/css-color/#color-conversion-code
		* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
		* https://www.itu.int/rec/R-REC-BT.2020/en
*/

const α = 1.09929682680944;
const β = 0.018053968510807;

const linearize = (v = 0) => {
	let abs = Math.abs(v);
	if (abs < β * 4.5) {
		return v / 4.5;
	}
	return (Math.sign(v) || 1) * Math.pow((abs + α - 1) / α, 1 / 0.45);
};

const convertRec2020ToXyz65 = rec2020 => {
	let r = linearize(rec2020.r);
	let g = linearize(rec2020.g);
	let b = linearize(rec2020.b);
	let res = {
		mode: 'xyz65',
		x:
			0.6369580483012911 * r +
			0.1446169035862083 * g +
			0.1688809751641721 * b,
		y:
			0.262700212011267 * r +
			0.6779980715188708 * g +
			0.059301716469862 * b,
		z: 0 * r + 0.0280726930490874 * g + 1.0609850577107909 * b
	};
	if (rec2020.alpha !== undefined) {
		res.alpha = rec2020.alpha;
	}
	return res;
};

const definition$4 = {
	...definition$r,
	mode: 'rec2020',

	fromMode: {
		xyz65: convertXyz65ToRec2020,
		rgb: color => convertXyz65ToRec2020(convertRgbToXyz65(color))
	},

	toMode: {
		xyz65: convertRec2020ToXyz65,
		rgb: color => convertXyz65ToRgb(convertRec2020ToXyz65(color))
	},

	parse: ['rec2020'],
	serialize: 'rec2020'
};

const bias = 0.00379307325527544933;
const bias_cbrt = Math.cbrt(bias);

const transfer$1 = v => Math.cbrt(v) - bias_cbrt;

const convertRgbToXyb = color => {
	const { r, g, b, alpha } = convertRgbToLrgb(color);
	const l = transfer$1(0.3 * r + 0.622 * g + 0.078 * b + bias);
	const m = transfer$1(0.23 * r + 0.692 * g + 0.078 * b + bias);
	const s = transfer$1(
		0.24342268924547819 * r +
			0.20476744424496821 * g +
			0.**************** * b +
			bias
	);
	const res = {
		mode: 'xyb',
		x: (l - m) / 2,
		y: (l + m) / 2,
		/* Apply default chroma from luma (subtract Y from B) */
		b: s - (l + m) / 2
	};
	if (alpha !== undefined) res.alpha = alpha;
	return res;
};

const transfer = v => Math.pow(v + bias_cbrt, 3);

const convertXybToRgb = ({ x, y, b, alpha }) => {
	if (x === undefined) x = 0;
	if (y === undefined) y = 0;
	if (b === undefined) b = 0;
	const l = transfer(x + y) - bias;
	const m = transfer(y - x) - bias;
	/* Account for chroma from luma: add Y back to B */
	const s = transfer(b + y) - bias;

	const res = convertLrgbToRgb({
		r:
			11.*************** * l -
			9.*************** * m -
			0.***************** * s,
		g:
			-3.**************** * l +
			4.*************** * m -
			0.***************** * s,
		b:
			-3.**************** * l +
			2.**************** * m +
			1.**************** * s
	});
	if (alpha !== undefined) res.alpha = alpha;
	return res;
};

/*
	The XYB color space, used in JPEG XL.
	Reference: https://ds.jpeg.org/whitepapers/jpeg-xl-whitepaper.pdf
*/

const definition$3 = {
	mode: 'xyb',
	channels: ['x', 'y', 'b', 'alpha'],
	parse: ['--xyb'],
	serialize: '--xyb',

	toMode: {
		rgb: convertXybToRgb
	},

	fromMode: {
		rgb: convertRgbToXyb
	},

	ranges: {
		x: [-0.0154, 0.0281],
		y: [0, 0.8453],
		b: [-0.2778, 0.388]
	},

	interpolate: {
		x: interpolatorLinear,
		y: interpolatorLinear,
		b: interpolatorLinear,
		alpha: { use: interpolatorLinear, fixup: fixupAlpha }
	}
};

/*
	The XYZ D50 color space
	-----------------------
 */


const definition$2 = {
	mode: 'xyz50',
	parse: ['xyz-d50'],
	serialize: 'xyz-d50',

	toMode: {
		rgb: convertXyz50ToRgb,
		lab: convertXyz50ToLab
	},

	fromMode: {
		rgb: convertRgbToXyz50,
		lab: convertLabToXyz50
	},

	channels: ['x', 'y', 'z', 'alpha'],

	ranges: {
		x: [0, 0.964],
		y: [0, 0.999],
		z: [0, 0.825]
	},

	interpolate: {
		x: interpolatorLinear,
		y: interpolatorLinear,
		z: interpolatorLinear,
		alpha: { use: interpolatorLinear, fixup: fixupAlpha }
	}
};

/*
	Chromatic adaptation of CIE XYZ from D65 to D50 white point
	using the Bradford method.

	References:
		* https://drafts.csswg.org/css-color/#color-conversion-code
		* http://www.brucelindbloom.com/index.html?Eqn_ChromAdapt.html	
*/

const convertXyz65ToXyz50 = xyz65 => {
	let { x, y, z, alpha } = xyz65;
	if (x === undefined) x = 0;
	if (y === undefined) y = 0;
	if (z === undefined) z = 0;
	let res = {
		mode: 'xyz50',
		x:
			1.0479298208405488 * x +
			0.0229467933410191 * y -
			0.0501922295431356 * z,
		y:
			0.0296278156881593 * x +
			0.990434484573249 * y -
			0.0170738250293851 * z,
		z:
			-0.0092430581525912 * x +
			0.0150551448965779 * y +
			0.7518742899580008 * z
	};
	if (alpha !== undefined) {
		res.alpha = alpha;
	}
	return res;
};

/*
	Chromatic adaptation of CIE XYZ from D50 to D65 white point
	using the Bradford method.

	References:
		* https://drafts.csswg.org/css-color/#color-conversion-code
		* http://www.brucelindbloom.com/index.html?Eqn_ChromAdapt.html	
*/

const convertXyz50ToXyz65 = xyz50 => {
	let { x, y, z, alpha } = xyz50;
	if (x === undefined) x = 0;
	if (y === undefined) y = 0;
	if (z === undefined) z = 0;
	let res = {
		mode: 'xyz65',
		x:
			0.9554734527042182 * x -
			0.0230985368742614 * y +
			0.0632593086610217 * z,
		y:
			-0.0283697069632081 * x +
			1.0099954580058226 * y +
			0.021041398966943 * z,
		z:
			0.0123140016883199 * x -
			0.0205076964334779 * y +
			1.3303659366080753 * z
	};
	if (alpha !== undefined) {
		res.alpha = alpha;
	}
	return res;
};

/*
	The XYZ D65 color space
	-----------------------
 */


const definition$1 = {
	mode: 'xyz65',

	toMode: {
		rgb: convertXyz65ToRgb,
		xyz50: convertXyz65ToXyz50
	},

	fromMode: {
		rgb: convertRgbToXyz65,
		xyz50: convertXyz50ToXyz65
	},

	ranges: {
		x: [0, 0.95],
		y: [0, 1],
		z: [0, 1.088]
	},

	channels: ['x', 'y', 'z', 'alpha'],

	parse: ['xyz', 'xyz-d65'],
	serialize: 'xyz-d65',

	interpolate: {
		x: interpolatorLinear,
		y: interpolatorLinear,
		z: interpolatorLinear,
		alpha: { use: interpolatorLinear, fixup: fixupAlpha }
	}
};

const convertRgbToYiq = ({ r, g, b, alpha }) => {
	if (r === undefined) r = 0;
	if (g === undefined) g = 0;
	if (b === undefined) b = 0;
	const res = {
		mode: 'yiq',
		y: 0.29889531 * r + 0.58662247 * g + 0.11448223 * b,
		i: 0.59597799 * r - 0.2741761 * g - 0.32180189 * b,
		q: 0.21147017 * r - 0.52261711 * g + 0.31114694 * b
	};
	if (alpha !== undefined) res.alpha = alpha;
	return res;
};

const convertYiqToRgb = ({ y, i, q, alpha }) => {
	if (y === undefined) y = 0;
	if (i === undefined) i = 0;
	if (q === undefined) q = 0;
	const res = {
		mode: 'rgb',
		r: y + 0.95608445 * i + 0.6208885 * q,
		g: y - 0.27137664 * i - 0.6486059 * q,
		b: y - 1.10561724 * i + 1.70250126 * q
	};
	if (alpha !== undefined) res.alpha = alpha;
	return res;
};

/*
	YIQ Color Space

	References
	----------

	Wikipedia:
		https://en.wikipedia.org/wiki/YIQ

	"Measuring perceived color difference using YIQ NTSC
	transmission color space in mobile applications"
		
		by Yuriy Kotsarenko, Fernando Ramos in:
		Programación Matemática y Software (2010) 

	Available at:
		
		http://www.progmat.uaem.mx:8080/artVol2Num2/Articulo3Vol2Num2.pdf
 */

const definition = {
	mode: 'yiq',

	toMode: {
		rgb: convertYiqToRgb
	},

	fromMode: {
		rgb: convertRgbToYiq
	},

	channels: ['y', 'i', 'q', 'alpha'],

	parse: ['--yiq'],
	serialize: '--yiq',

	ranges: {
		i: [-0.595, 0.595],
		q: [-0.522, 0.522]
	},

	interpolate: {
		y: interpolatorLinear,
		i: interpolatorLinear,
		q: interpolatorLinear,
		alpha: { use: interpolatorLinear, fixup: fixupAlpha }
	}
};

// Color space definitions

useMode(definition$q);
useMode(definition$p);
useMode(definition$o);
useMode(definition$n);
useMode(definition$m);
useMode(definition$l);
useMode(definition$k);
useMode(definition$j);
useMode(definition$i);
useMode(definition$h);
useMode(definition$g);
useMode(definition$f);
useMode(definition$e);
useMode(definition$d);
useMode(definition$c);
useMode(definition$b);
useMode(definition$a);
useMode(definition$9);
useMode(modeOkhsl);
useMode(modeOkhsv);
useMode(definition$8);
useMode(definition$7);
useMode(definition$6);
useMode(definition$5);
useMode(definition$4);
useMode(definition$r);
useMode(definition$3);
useMode(definition$2);
useMode(definition$1);
useMode(definition);

/** Detect free variable `global` from Node.js. */
var freeGlobal = typeof global == 'object' && global && global.Object === Object && global;

/** Detect free variable `self`. */
var freeSelf = typeof self == 'object' && self && self.Object === Object && self;

/** Used as a reference to the global object. */
var root = freeGlobal || freeSelf || Function('return this')();

/** Built-in value references. */
var Symbol$1 = root.Symbol;

/** Used for built-in method references. */
var objectProto$9 = Object.prototype;

/** Used to check objects for own properties. */
var hasOwnProperty$7 = objectProto$9.hasOwnProperty;

/**
 * Used to resolve the
 * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)
 * of values.
 */
var nativeObjectToString$1 = objectProto$9.toString;

/** Built-in value references. */
var symToStringTag$1 = Symbol$1 ? Symbol$1.toStringTag : undefined;

/**
 * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.
 *
 * @private
 * @param {*} value The value to query.
 * @returns {string} Returns the raw `toStringTag`.
 */
function getRawTag(value) {
  var isOwn = hasOwnProperty$7.call(value, symToStringTag$1),
      tag = value[symToStringTag$1];

  try {
    value[symToStringTag$1] = undefined;
    var unmasked = true;
  } catch (e) {}

  var result = nativeObjectToString$1.call(value);
  if (unmasked) {
    if (isOwn) {
      value[symToStringTag$1] = tag;
    } else {
      delete value[symToStringTag$1];
    }
  }
  return result;
}

/** Used for built-in method references. */
var objectProto$8 = Object.prototype;

/**
 * Used to resolve the
 * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)
 * of values.
 */
var nativeObjectToString = objectProto$8.toString;

/**
 * Converts `value` to a string using `Object.prototype.toString`.
 *
 * @private
 * @param {*} value The value to convert.
 * @returns {string} Returns the converted string.
 */
function objectToString(value) {
  return nativeObjectToString.call(value);
}

/** `Object#toString` result references. */
var nullTag = '[object Null]',
    undefinedTag = '[object Undefined]';

/** Built-in value references. */
var symToStringTag = Symbol$1 ? Symbol$1.toStringTag : undefined;

/**
 * The base implementation of `getTag` without fallbacks for buggy environments.
 *
 * @private
 * @param {*} value The value to query.
 * @returns {string} Returns the `toStringTag`.
 */
function baseGetTag(value) {
  if (value == null) {
    return value === undefined ? undefinedTag : nullTag;
  }
  return (symToStringTag && symToStringTag in Object(value))
    ? getRawTag(value)
    : objectToString(value);
}

/**
 * Checks if `value` is object-like. A value is object-like if it's not `null`
 * and has a `typeof` result of "object".
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is object-like, else `false`.
 * @example
 *
 * _.isObjectLike({});
 * // => true
 *
 * _.isObjectLike([1, 2, 3]);
 * // => true
 *
 * _.isObjectLike(_.noop);
 * // => false
 *
 * _.isObjectLike(null);
 * // => false
 */
function isObjectLike(value) {
  return value != null && typeof value == 'object';
}

/**
 * Checks if `value` is classified as an `Array` object.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is an array, else `false`.
 * @example
 *
 * _.isArray([1, 2, 3]);
 * // => true
 *
 * _.isArray(document.body.children);
 * // => false
 *
 * _.isArray('abc');
 * // => false
 *
 * _.isArray(_.noop);
 * // => false
 */
var isArray = Array.isArray;

/**
 * Checks if `value` is the
 * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)
 * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is an object, else `false`.
 * @example
 *
 * _.isObject({});
 * // => true
 *
 * _.isObject([1, 2, 3]);
 * // => true
 *
 * _.isObject(_.noop);
 * // => true
 *
 * _.isObject(null);
 * // => false
 */
function isObject(value) {
  var type = typeof value;
  return value != null && (type == 'object' || type == 'function');
}

/**
 * This method returns the first argument it receives.
 *
 * @static
 * @since 0.1.0
 * @memberOf _
 * @category Util
 * @param {*} value Any value.
 * @returns {*} Returns `value`.
 * @example
 *
 * var object = { 'a': 1 };
 *
 * console.log(_.identity(object) === object);
 * // => true
 */
function identity(value) {
  return value;
}

/** `Object#toString` result references. */
var asyncTag = '[object AsyncFunction]',
    funcTag$1 = '[object Function]',
    genTag = '[object GeneratorFunction]',
    proxyTag = '[object Proxy]';

/**
 * Checks if `value` is classified as a `Function` object.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a function, else `false`.
 * @example
 *
 * _.isFunction(_);
 * // => true
 *
 * _.isFunction(/abc/);
 * // => false
 */
function isFunction(value) {
  if (!isObject(value)) {
    return false;
  }
  // The use of `Object#toString` avoids issues with the `typeof` operator
  // in Safari 9 which returns 'object' for typed arrays and other constructors.
  var tag = baseGetTag(value);
  return tag == funcTag$1 || tag == genTag || tag == asyncTag || tag == proxyTag;
}

/** Used to detect overreaching core-js shims. */
var coreJsData = root['__core-js_shared__'];

/** Used to detect methods masquerading as native. */
var maskSrcKey = (function() {
  var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');
  return uid ? ('Symbol(src)_1.' + uid) : '';
}());

/**
 * Checks if `func` has its source masked.
 *
 * @private
 * @param {Function} func The function to check.
 * @returns {boolean} Returns `true` if `func` is masked, else `false`.
 */
function isMasked(func) {
  return !!maskSrcKey && (maskSrcKey in func);
}

/** Used for built-in method references. */
var funcProto$2 = Function.prototype;

/** Used to resolve the decompiled source of functions. */
var funcToString$2 = funcProto$2.toString;

/**
 * Converts `func` to its source code.
 *
 * @private
 * @param {Function} func The function to convert.
 * @returns {string} Returns the source code.
 */
function toSource(func) {
  if (func != null) {
    try {
      return funcToString$2.call(func);
    } catch (e) {}
    try {
      return (func + '');
    } catch (e) {}
  }
  return '';
}

/**
 * Used to match `RegExp`
 * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).
 */
var reRegExpChar = /[\\^$.*+?()[\]{}|]/g;

/** Used to detect host constructors (Safari). */
var reIsHostCtor = /^\[object .+?Constructor\]$/;

/** Used for built-in method references. */
var funcProto$1 = Function.prototype,
    objectProto$7 = Object.prototype;

/** Used to resolve the decompiled source of functions. */
var funcToString$1 = funcProto$1.toString;

/** Used to check objects for own properties. */
var hasOwnProperty$6 = objectProto$7.hasOwnProperty;

/** Used to detect if a method is native. */
var reIsNative = RegExp('^' +
  funcToString$1.call(hasOwnProperty$6).replace(reRegExpChar, '\\$&')
  .replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, '$1.*?') + '$'
);

/**
 * The base implementation of `_.isNative` without bad shim checks.
 *
 * @private
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a native function,
 *  else `false`.
 */
function baseIsNative(value) {
  if (!isObject(value) || isMasked(value)) {
    return false;
  }
  var pattern = isFunction(value) ? reIsNative : reIsHostCtor;
  return pattern.test(toSource(value));
}

/**
 * Gets the value at `key` of `object`.
 *
 * @private
 * @param {Object} [object] The object to query.
 * @param {string} key The key of the property to get.
 * @returns {*} Returns the property value.
 */
function getValue(object, key) {
  return object == null ? undefined : object[key];
}

/**
 * Gets the native function at `key` of `object`.
 *
 * @private
 * @param {Object} object The object to query.
 * @param {string} key The key of the method to get.
 * @returns {*} Returns the function if it's native, else `undefined`.
 */
function getNative(object, key) {
  var value = getValue(object, key);
  return baseIsNative(value) ? value : undefined;
}

/** Built-in value references. */
var objectCreate = Object.create;

/**
 * The base implementation of `_.create` without support for assigning
 * properties to the created object.
 *
 * @private
 * @param {Object} proto The object to inherit from.
 * @returns {Object} Returns the new object.
 */
var baseCreate = (function() {
  function object() {}
  return function(proto) {
    if (!isObject(proto)) {
      return {};
    }
    if (objectCreate) {
      return objectCreate(proto);
    }
    object.prototype = proto;
    var result = new object;
    object.prototype = undefined;
    return result;
  };
}());

/**
 * A faster alternative to `Function#apply`, this function invokes `func`
 * with the `this` binding of `thisArg` and the arguments of `args`.
 *
 * @private
 * @param {Function} func The function to invoke.
 * @param {*} thisArg The `this` binding of `func`.
 * @param {Array} args The arguments to invoke `func` with.
 * @returns {*} Returns the result of `func`.
 */
function apply(func, thisArg, args) {
  switch (args.length) {
    case 0: return func.call(thisArg);
    case 1: return func.call(thisArg, args[0]);
    case 2: return func.call(thisArg, args[0], args[1]);
    case 3: return func.call(thisArg, args[0], args[1], args[2]);
  }
  return func.apply(thisArg, args);
}

/**
 * Copies the values of `source` to `array`.
 *
 * @private
 * @param {Array} source The array to copy values from.
 * @param {Array} [array=[]] The array to copy values to.
 * @returns {Array} Returns `array`.
 */
function copyArray(source, array) {
  var index = -1,
      length = source.length;

  array || (array = Array(length));
  while (++index < length) {
    array[index] = source[index];
  }
  return array;
}

/** Used to detect hot functions by number of calls within a span of milliseconds. */
var HOT_COUNT = 800,
    HOT_SPAN = 16;

/* Built-in method references for those with the same name as other `lodash` methods. */
var nativeNow = Date.now;

/**
 * Creates a function that'll short out and invoke `identity` instead
 * of `func` when it's called `HOT_COUNT` or more times in `HOT_SPAN`
 * milliseconds.
 *
 * @private
 * @param {Function} func The function to restrict.
 * @returns {Function} Returns the new shortable function.
 */
function shortOut(func) {
  var count = 0,
      lastCalled = 0;

  return function() {
    var stamp = nativeNow(),
        remaining = HOT_SPAN - (stamp - lastCalled);

    lastCalled = stamp;
    if (remaining > 0) {
      if (++count >= HOT_COUNT) {
        return arguments[0];
      }
    } else {
      count = 0;
    }
    return func.apply(undefined, arguments);
  };
}

/**
 * Creates a function that returns `value`.
 *
 * @static
 * @memberOf _
 * @since 2.4.0
 * @category Util
 * @param {*} value The value to return from the new function.
 * @returns {Function} Returns the new constant function.
 * @example
 *
 * var objects = _.times(2, _.constant({ 'a': 1 }));
 *
 * console.log(objects);
 * // => [{ 'a': 1 }, { 'a': 1 }]
 *
 * console.log(objects[0] === objects[1]);
 * // => true
 */
function constant(value) {
  return function() {
    return value;
  };
}

var defineProperty = (function() {
  try {
    var func = getNative(Object, 'defineProperty');
    func({}, '', {});
    return func;
  } catch (e) {}
}());

/**
 * The base implementation of `setToString` without support for hot loop shorting.
 *
 * @private
 * @param {Function} func The function to modify.
 * @param {Function} string The `toString` result.
 * @returns {Function} Returns `func`.
 */
var baseSetToString = !defineProperty ? identity : function(func, string) {
  return defineProperty(func, 'toString', {
    'configurable': true,
    'enumerable': false,
    'value': constant(string),
    'writable': true
  });
};

/**
 * Sets the `toString` method of `func` to return `string`.
 *
 * @private
 * @param {Function} func The function to modify.
 * @param {Function} string The `toString` result.
 * @returns {Function} Returns `func`.
 */
var setToString = shortOut(baseSetToString);

/** Used as references for various `Number` constants. */
var MAX_SAFE_INTEGER$1 = 9007199254740991;

/** Used to detect unsigned integer values. */
var reIsUint = /^(?:0|[1-9]\d*)$/;

/**
 * Checks if `value` is a valid array-like index.
 *
 * @private
 * @param {*} value The value to check.
 * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.
 * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.
 */
function isIndex(value, length) {
  var type = typeof value;
  length = length == null ? MAX_SAFE_INTEGER$1 : length;

  return !!length &&
    (type == 'number' ||
      (type != 'symbol' && reIsUint.test(value))) &&
        (value > -1 && value % 1 == 0 && value < length);
}

/**
 * The base implementation of `assignValue` and `assignMergeValue` without
 * value checks.
 *
 * @private
 * @param {Object} object The object to modify.
 * @param {string} key The key of the property to assign.
 * @param {*} value The value to assign.
 */
function baseAssignValue(object, key, value) {
  if (key == '__proto__' && defineProperty) {
    defineProperty(object, key, {
      'configurable': true,
      'enumerable': true,
      'value': value,
      'writable': true
    });
  } else {
    object[key] = value;
  }
}

/**
 * Performs a
 * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)
 * comparison between two values to determine if they are equivalent.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to compare.
 * @param {*} other The other value to compare.
 * @returns {boolean} Returns `true` if the values are equivalent, else `false`.
 * @example
 *
 * var object = { 'a': 1 };
 * var other = { 'a': 1 };
 *
 * _.eq(object, object);
 * // => true
 *
 * _.eq(object, other);
 * // => false
 *
 * _.eq('a', 'a');
 * // => true
 *
 * _.eq('a', Object('a'));
 * // => false
 *
 * _.eq(NaN, NaN);
 * // => true
 */
function eq(value, other) {
  return value === other || (value !== value && other !== other);
}

/** Used for built-in method references. */
var objectProto$6 = Object.prototype;

/** Used to check objects for own properties. */
var hasOwnProperty$5 = objectProto$6.hasOwnProperty;

/**
 * Assigns `value` to `key` of `object` if the existing value is not equivalent
 * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)
 * for equality comparisons.
 *
 * @private
 * @param {Object} object The object to modify.
 * @param {string} key The key of the property to assign.
 * @param {*} value The value to assign.
 */
function assignValue(object, key, value) {
  var objValue = object[key];
  if (!(hasOwnProperty$5.call(object, key) && eq(objValue, value)) ||
      (value === undefined && !(key in object))) {
    baseAssignValue(object, key, value);
  }
}

/**
 * Copies properties of `source` to `object`.
 *
 * @private
 * @param {Object} source The object to copy properties from.
 * @param {Array} props The property identifiers to copy.
 * @param {Object} [object={}] The object to copy properties to.
 * @param {Function} [customizer] The function to customize copied values.
 * @returns {Object} Returns `object`.
 */
function copyObject(source, props, object, customizer) {
  var isNew = !object;
  object || (object = {});

  var index = -1,
      length = props.length;

  while (++index < length) {
    var key = props[index];

    var newValue = undefined;

    if (newValue === undefined) {
      newValue = source[key];
    }
    if (isNew) {
      baseAssignValue(object, key, newValue);
    } else {
      assignValue(object, key, newValue);
    }
  }
  return object;
}

/* Built-in method references for those with the same name as other `lodash` methods. */
var nativeMax = Math.max;

/**
 * A specialized version of `baseRest` which transforms the rest array.
 *
 * @private
 * @param {Function} func The function to apply a rest parameter to.
 * @param {number} [start=func.length-1] The start position of the rest parameter.
 * @param {Function} transform The rest array transform.
 * @returns {Function} Returns the new function.
 */
function overRest(func, start, transform) {
  start = nativeMax(start === undefined ? (func.length - 1) : start, 0);
  return function() {
    var args = arguments,
        index = -1,
        length = nativeMax(args.length - start, 0),
        array = Array(length);

    while (++index < length) {
      array[index] = args[start + index];
    }
    index = -1;
    var otherArgs = Array(start + 1);
    while (++index < start) {
      otherArgs[index] = args[index];
    }
    otherArgs[start] = transform(array);
    return apply(func, this, otherArgs);
  };
}

/**
 * The base implementation of `_.rest` which doesn't validate or coerce arguments.
 *
 * @private
 * @param {Function} func The function to apply a rest parameter to.
 * @param {number} [start=func.length-1] The start position of the rest parameter.
 * @returns {Function} Returns the new function.
 */
function baseRest(func, start) {
  return setToString(overRest(func, start, identity), func + '');
}

/** Used as references for various `Number` constants. */
var MAX_SAFE_INTEGER = 9007199254740991;

/**
 * Checks if `value` is a valid array-like length.
 *
 * **Note:** This method is loosely based on
 * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.
 * @example
 *
 * _.isLength(3);
 * // => true
 *
 * _.isLength(Number.MIN_VALUE);
 * // => false
 *
 * _.isLength(Infinity);
 * // => false
 *
 * _.isLength('3');
 * // => false
 */
function isLength(value) {
  return typeof value == 'number' &&
    value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;
}

/**
 * Checks if `value` is array-like. A value is considered array-like if it's
 * not a function and has a `value.length` that's an integer greater than or
 * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is array-like, else `false`.
 * @example
 *
 * _.isArrayLike([1, 2, 3]);
 * // => true
 *
 * _.isArrayLike(document.body.children);
 * // => true
 *
 * _.isArrayLike('abc');
 * // => true
 *
 * _.isArrayLike(_.noop);
 * // => false
 */
function isArrayLike(value) {
  return value != null && isLength(value.length) && !isFunction(value);
}

/**
 * Checks if the given arguments are from an iteratee call.
 *
 * @private
 * @param {*} value The potential iteratee value argument.
 * @param {*} index The potential iteratee index or key argument.
 * @param {*} object The potential iteratee object argument.
 * @returns {boolean} Returns `true` if the arguments are from an iteratee call,
 *  else `false`.
 */
function isIterateeCall(value, index, object) {
  if (!isObject(object)) {
    return false;
  }
  var type = typeof index;
  if (type == 'number'
        ? (isArrayLike(object) && isIndex(index, object.length))
        : (type == 'string' && index in object)
      ) {
    return eq(object[index], value);
  }
  return false;
}

/**
 * Creates a function like `_.assign`.
 *
 * @private
 * @param {Function} assigner The function to assign values.
 * @returns {Function} Returns the new assigner function.
 */
function createAssigner(assigner) {
  return baseRest(function(object, sources) {
    var index = -1,
        length = sources.length,
        customizer = length > 1 ? sources[length - 1] : undefined,
        guard = length > 2 ? sources[2] : undefined;

    customizer = (assigner.length > 3 && typeof customizer == 'function')
      ? (length--, customizer)
      : undefined;

    if (guard && isIterateeCall(sources[0], sources[1], guard)) {
      customizer = length < 3 ? undefined : customizer;
      length = 1;
    }
    object = Object(object);
    while (++index < length) {
      var source = sources[index];
      if (source) {
        assigner(object, source, index, customizer);
      }
    }
    return object;
  });
}

/** Used for built-in method references. */
var objectProto$5 = Object.prototype;

/**
 * Checks if `value` is likely a prototype object.
 *
 * @private
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.
 */
function isPrototype(value) {
  var Ctor = value && value.constructor,
      proto = (typeof Ctor == 'function' && Ctor.prototype) || objectProto$5;

  return value === proto;
}

/**
 * The base implementation of `_.times` without support for iteratee shorthands
 * or max array length checks.
 *
 * @private
 * @param {number} n The number of times to invoke `iteratee`.
 * @param {Function} iteratee The function invoked per iteration.
 * @returns {Array} Returns the array of results.
 */
function baseTimes(n, iteratee) {
  var index = -1,
      result = Array(n);

  while (++index < n) {
    result[index] = iteratee(index);
  }
  return result;
}

/** `Object#toString` result references. */
var argsTag$1 = '[object Arguments]';

/**
 * The base implementation of `_.isArguments`.
 *
 * @private
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is an `arguments` object,
 */
function baseIsArguments(value) {
  return isObjectLike(value) && baseGetTag(value) == argsTag$1;
}

/** Used for built-in method references. */
var objectProto$4 = Object.prototype;

/** Used to check objects for own properties. */
var hasOwnProperty$4 = objectProto$4.hasOwnProperty;

/** Built-in value references. */
var propertyIsEnumerable = objectProto$4.propertyIsEnumerable;

/**
 * Checks if `value` is likely an `arguments` object.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is an `arguments` object,
 *  else `false`.
 * @example
 *
 * _.isArguments(function() { return arguments; }());
 * // => true
 *
 * _.isArguments([1, 2, 3]);
 * // => false
 */
var isArguments = baseIsArguments(function() { return arguments; }()) ? baseIsArguments : function(value) {
  return isObjectLike(value) && hasOwnProperty$4.call(value, 'callee') &&
    !propertyIsEnumerable.call(value, 'callee');
};

/**
 * This method returns `false`.
 *
 * @static
 * @memberOf _
 * @since 4.13.0
 * @category Util
 * @returns {boolean} Returns `false`.
 * @example
 *
 * _.times(2, _.stubFalse);
 * // => [false, false]
 */
function stubFalse() {
  return false;
}

/** Detect free variable `exports`. */
var freeExports$2 = typeof exports == 'object' && exports && !exports.nodeType && exports;

/** Detect free variable `module`. */
var freeModule$2 = freeExports$2 && typeof module == 'object' && module && !module.nodeType && module;

/** Detect the popular CommonJS extension `module.exports`. */
var moduleExports$2 = freeModule$2 && freeModule$2.exports === freeExports$2;

/** Built-in value references. */
var Buffer$1 = moduleExports$2 ? root.Buffer : undefined;

/* Built-in method references for those with the same name as other `lodash` methods. */
var nativeIsBuffer = Buffer$1 ? Buffer$1.isBuffer : undefined;

/**
 * Checks if `value` is a buffer.
 *
 * @static
 * @memberOf _
 * @since 4.3.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a buffer, else `false`.
 * @example
 *
 * _.isBuffer(new Buffer(2));
 * // => true
 *
 * _.isBuffer(new Uint8Array(2));
 * // => false
 */
var isBuffer = nativeIsBuffer || stubFalse;

/** `Object#toString` result references. */
var argsTag = '[object Arguments]',
    arrayTag = '[object Array]',
    boolTag = '[object Boolean]',
    dateTag = '[object Date]',
    errorTag = '[object Error]',
    funcTag = '[object Function]',
    mapTag = '[object Map]',
    numberTag = '[object Number]',
    objectTag$1 = '[object Object]',
    regexpTag = '[object RegExp]',
    setTag = '[object Set]',
    stringTag = '[object String]',
    weakMapTag = '[object WeakMap]';

var arrayBufferTag = '[object ArrayBuffer]',
    dataViewTag = '[object DataView]',
    float32Tag = '[object Float32Array]',
    float64Tag = '[object Float64Array]',
    int8Tag = '[object Int8Array]',
    int16Tag = '[object Int16Array]',
    int32Tag = '[object Int32Array]',
    uint8Tag = '[object Uint8Array]',
    uint8ClampedTag = '[object Uint8ClampedArray]',
    uint16Tag = '[object Uint16Array]',
    uint32Tag = '[object Uint32Array]';

/** Used to identify `toStringTag` values of typed arrays. */
var typedArrayTags = {};
typedArrayTags[float32Tag] = typedArrayTags[float64Tag] =
typedArrayTags[int8Tag] = typedArrayTags[int16Tag] =
typedArrayTags[int32Tag] = typedArrayTags[uint8Tag] =
typedArrayTags[uint8ClampedTag] = typedArrayTags[uint16Tag] =
typedArrayTags[uint32Tag] = true;
typedArrayTags[argsTag] = typedArrayTags[arrayTag] =
typedArrayTags[arrayBufferTag] = typedArrayTags[boolTag] =
typedArrayTags[dataViewTag] = typedArrayTags[dateTag] =
typedArrayTags[errorTag] = typedArrayTags[funcTag] =
typedArrayTags[mapTag] = typedArrayTags[numberTag] =
typedArrayTags[objectTag$1] = typedArrayTags[regexpTag] =
typedArrayTags[setTag] = typedArrayTags[stringTag] =
typedArrayTags[weakMapTag] = false;

/**
 * The base implementation of `_.isTypedArray` without Node.js optimizations.
 *
 * @private
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.
 */
function baseIsTypedArray(value) {
  return isObjectLike(value) &&
    isLength(value.length) && !!typedArrayTags[baseGetTag(value)];
}

/**
 * The base implementation of `_.unary` without support for storing metadata.
 *
 * @private
 * @param {Function} func The function to cap arguments for.
 * @returns {Function} Returns the new capped function.
 */
function baseUnary(func) {
  return function(value) {
    return func(value);
  };
}

/** Detect free variable `exports`. */
var freeExports$1 = typeof exports == 'object' && exports && !exports.nodeType && exports;

/** Detect free variable `module`. */
var freeModule$1 = freeExports$1 && typeof module == 'object' && module && !module.nodeType && module;

/** Detect the popular CommonJS extension `module.exports`. */
var moduleExports$1 = freeModule$1 && freeModule$1.exports === freeExports$1;

/** Detect free variable `process` from Node.js. */
var freeProcess = moduleExports$1 && freeGlobal.process;

/** Used to access faster Node.js helpers. */
var nodeUtil = (function() {
  try {
    // Use `util.types` for Node.js 10+.
    var types = freeModule$1 && freeModule$1.require && freeModule$1.require('util').types;

    if (types) {
      return types;
    }

    // Legacy `process.binding('util')` for Node.js < 10.
    return freeProcess && freeProcess.binding && freeProcess.binding('util');
  } catch (e) {}
}());

/* Node.js helper references. */
var nodeIsTypedArray = nodeUtil && nodeUtil.isTypedArray;

/**
 * Checks if `value` is classified as a typed array.
 *
 * @static
 * @memberOf _
 * @since 3.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.
 * @example
 *
 * _.isTypedArray(new Uint8Array);
 * // => true
 *
 * _.isTypedArray([]);
 * // => false
 */
var isTypedArray = nodeIsTypedArray ? baseUnary(nodeIsTypedArray) : baseIsTypedArray;

/**
 * Creates an array of the enumerable property names of the array-like `value`.
 *
 * @private
 * @param {*} value The value to query.
 * @param {boolean} inherited Specify returning inherited property names.
 * @returns {Array} Returns the array of property names.
 */
function arrayLikeKeys(value, inherited) {
  var isArr = isArray(value),
      isArg = !isArr && isArguments(value),
      isBuff = !isArr && !isArg && isBuffer(value),
      isType = !isArr && !isArg && !isBuff && isTypedArray(value),
      skipIndexes = isArr || isArg || isBuff || isType,
      result = skipIndexes ? baseTimes(value.length, String) : [],
      length = result.length;

  for (var key in value) {
    if (!(skipIndexes && (
           // Safari 9 has enumerable `arguments.length` in strict mode.
           key == 'length' ||
           // Node.js 0.10 has enumerable non-index properties on buffers.
           (isBuff && (key == 'offset' || key == 'parent')) ||
           // PhantomJS 2 has enumerable non-index properties on typed arrays.
           (isType && (key == 'buffer' || key == 'byteLength' || key == 'byteOffset')) ||
           // Skip index properties.
           isIndex(key, length)
        ))) {
      result.push(key);
    }
  }
  return result;
}

/**
 * Creates a unary function that invokes `func` with its argument transformed.
 *
 * @private
 * @param {Function} func The function to wrap.
 * @param {Function} transform The argument transform.
 * @returns {Function} Returns the new function.
 */
function overArg(func, transform) {
  return function(arg) {
    return func(transform(arg));
  };
}

/**
 * This function is like
 * [`Object.keys`](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)
 * except that it includes inherited enumerable properties.
 *
 * @private
 * @param {Object} object The object to query.
 * @returns {Array} Returns the array of property names.
 */
function nativeKeysIn(object) {
  var result = [];
  if (object != null) {
    for (var key in Object(object)) {
      result.push(key);
    }
  }
  return result;
}

/** Used for built-in method references. */
var objectProto$3 = Object.prototype;

/** Used to check objects for own properties. */
var hasOwnProperty$3 = objectProto$3.hasOwnProperty;

/**
 * The base implementation of `_.keysIn` which doesn't treat sparse arrays as dense.
 *
 * @private
 * @param {Object} object The object to query.
 * @returns {Array} Returns the array of property names.
 */
function baseKeysIn(object) {
  if (!isObject(object)) {
    return nativeKeysIn(object);
  }
  var isProto = isPrototype(object),
      result = [];

  for (var key in object) {
    if (!(key == 'constructor' && (isProto || !hasOwnProperty$3.call(object, key)))) {
      result.push(key);
    }
  }
  return result;
}

/**
 * Creates an array of the own and inherited enumerable property names of `object`.
 *
 * **Note:** Non-object values are coerced to objects.
 *
 * @static
 * @memberOf _
 * @since 3.0.0
 * @category Object
 * @param {Object} object The object to query.
 * @returns {Array} Returns the array of property names.
 * @example
 *
 * function Foo() {
 *   this.a = 1;
 *   this.b = 2;
 * }
 *
 * Foo.prototype.c = 3;
 *
 * _.keysIn(new Foo);
 * // => ['a', 'b', 'c'] (iteration order is not guaranteed)
 */
function keysIn(object) {
  return isArrayLike(object) ? arrayLikeKeys(object) : baseKeysIn(object);
}

/* Built-in method references that are verified to be native. */
var nativeCreate = getNative(Object, 'create');

/**
 * Removes all key-value entries from the hash.
 *
 * @private
 * @name clear
 * @memberOf Hash
 */
function hashClear() {
  this.__data__ = nativeCreate ? nativeCreate(null) : {};
  this.size = 0;
}

/**
 * Removes `key` and its value from the hash.
 *
 * @private
 * @name delete
 * @memberOf Hash
 * @param {Object} hash The hash to modify.
 * @param {string} key The key of the value to remove.
 * @returns {boolean} Returns `true` if the entry was removed, else `false`.
 */
function hashDelete(key) {
  var result = this.has(key) && delete this.__data__[key];
  this.size -= result ? 1 : 0;
  return result;
}

/** Used to stand-in for `undefined` hash values. */
var HASH_UNDEFINED$1 = '__lodash_hash_undefined__';

/** Used for built-in method references. */
var objectProto$2 = Object.prototype;

/** Used to check objects for own properties. */
var hasOwnProperty$2 = objectProto$2.hasOwnProperty;

/**
 * Gets the hash value for `key`.
 *
 * @private
 * @name get
 * @memberOf Hash
 * @param {string} key The key of the value to get.
 * @returns {*} Returns the entry value.
 */
function hashGet(key) {
  var data = this.__data__;
  if (nativeCreate) {
    var result = data[key];
    return result === HASH_UNDEFINED$1 ? undefined : result;
  }
  return hasOwnProperty$2.call(data, key) ? data[key] : undefined;
}

/** Used for built-in method references. */
var objectProto$1 = Object.prototype;

/** Used to check objects for own properties. */
var hasOwnProperty$1 = objectProto$1.hasOwnProperty;

/**
 * Checks if a hash value for `key` exists.
 *
 * @private
 * @name has
 * @memberOf Hash
 * @param {string} key The key of the entry to check.
 * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
 */
function hashHas(key) {
  var data = this.__data__;
  return nativeCreate ? (data[key] !== undefined) : hasOwnProperty$1.call(data, key);
}

/** Used to stand-in for `undefined` hash values. */
var HASH_UNDEFINED = '__lodash_hash_undefined__';

/**
 * Sets the hash `key` to `value`.
 *
 * @private
 * @name set
 * @memberOf Hash
 * @param {string} key The key of the value to set.
 * @param {*} value The value to set.
 * @returns {Object} Returns the hash instance.
 */
function hashSet(key, value) {
  var data = this.__data__;
  this.size += this.has(key) ? 0 : 1;
  data[key] = (nativeCreate && value === undefined) ? HASH_UNDEFINED : value;
  return this;
}

/**
 * Creates a hash object.
 *
 * @private
 * @constructor
 * @param {Array} [entries] The key-value pairs to cache.
 */
function Hash(entries) {
  var index = -1,
      length = entries == null ? 0 : entries.length;

  this.clear();
  while (++index < length) {
    var entry = entries[index];
    this.set(entry[0], entry[1]);
  }
}

// Add methods to `Hash`.
Hash.prototype.clear = hashClear;
Hash.prototype['delete'] = hashDelete;
Hash.prototype.get = hashGet;
Hash.prototype.has = hashHas;
Hash.prototype.set = hashSet;

/**
 * Removes all key-value entries from the list cache.
 *
 * @private
 * @name clear
 * @memberOf ListCache
 */
function listCacheClear() {
  this.__data__ = [];
  this.size = 0;
}

/**
 * Gets the index at which the `key` is found in `array` of key-value pairs.
 *
 * @private
 * @param {Array} array The array to inspect.
 * @param {*} key The key to search for.
 * @returns {number} Returns the index of the matched value, else `-1`.
 */
function assocIndexOf(array, key) {
  var length = array.length;
  while (length--) {
    if (eq(array[length][0], key)) {
      return length;
    }
  }
  return -1;
}

/** Used for built-in method references. */
var arrayProto = Array.prototype;

/** Built-in value references. */
var splice = arrayProto.splice;

/**
 * Removes `key` and its value from the list cache.
 *
 * @private
 * @name delete
 * @memberOf ListCache
 * @param {string} key The key of the value to remove.
 * @returns {boolean} Returns `true` if the entry was removed, else `false`.
 */
function listCacheDelete(key) {
  var data = this.__data__,
      index = assocIndexOf(data, key);

  if (index < 0) {
    return false;
  }
  var lastIndex = data.length - 1;
  if (index == lastIndex) {
    data.pop();
  } else {
    splice.call(data, index, 1);
  }
  --this.size;
  return true;
}

/**
 * Gets the list cache value for `key`.
 *
 * @private
 * @name get
 * @memberOf ListCache
 * @param {string} key The key of the value to get.
 * @returns {*} Returns the entry value.
 */
function listCacheGet(key) {
  var data = this.__data__,
      index = assocIndexOf(data, key);

  return index < 0 ? undefined : data[index][1];
}

/**
 * Checks if a list cache value for `key` exists.
 *
 * @private
 * @name has
 * @memberOf ListCache
 * @param {string} key The key of the entry to check.
 * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
 */
function listCacheHas(key) {
  return assocIndexOf(this.__data__, key) > -1;
}

/**
 * Sets the list cache `key` to `value`.
 *
 * @private
 * @name set
 * @memberOf ListCache
 * @param {string} key The key of the value to set.
 * @param {*} value The value to set.
 * @returns {Object} Returns the list cache instance.
 */
function listCacheSet(key, value) {
  var data = this.__data__,
      index = assocIndexOf(data, key);

  if (index < 0) {
    ++this.size;
    data.push([key, value]);
  } else {
    data[index][1] = value;
  }
  return this;
}

/**
 * Creates an list cache object.
 *
 * @private
 * @constructor
 * @param {Array} [entries] The key-value pairs to cache.
 */
function ListCache(entries) {
  var index = -1,
      length = entries == null ? 0 : entries.length;

  this.clear();
  while (++index < length) {
    var entry = entries[index];
    this.set(entry[0], entry[1]);
  }
}

// Add methods to `ListCache`.
ListCache.prototype.clear = listCacheClear;
ListCache.prototype['delete'] = listCacheDelete;
ListCache.prototype.get = listCacheGet;
ListCache.prototype.has = listCacheHas;
ListCache.prototype.set = listCacheSet;

/* Built-in method references that are verified to be native. */
var Map = getNative(root, 'Map');

/**
 * Removes all key-value entries from the map.
 *
 * @private
 * @name clear
 * @memberOf MapCache
 */
function mapCacheClear() {
  this.size = 0;
  this.__data__ = {
    'hash': new Hash,
    'map': new (Map || ListCache),
    'string': new Hash
  };
}

/**
 * Checks if `value` is suitable for use as unique object key.
 *
 * @private
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is suitable, else `false`.
 */
function isKeyable(value) {
  var type = typeof value;
  return (type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean')
    ? (value !== '__proto__')
    : (value === null);
}

/**
 * Gets the data for `map`.
 *
 * @private
 * @param {Object} map The map to query.
 * @param {string} key The reference key.
 * @returns {*} Returns the map data.
 */
function getMapData(map, key) {
  var data = map.__data__;
  return isKeyable(key)
    ? data[typeof key == 'string' ? 'string' : 'hash']
    : data.map;
}

/**
 * Removes `key` and its value from the map.
 *
 * @private
 * @name delete
 * @memberOf MapCache
 * @param {string} key The key of the value to remove.
 * @returns {boolean} Returns `true` if the entry was removed, else `false`.
 */
function mapCacheDelete(key) {
  var result = getMapData(this, key)['delete'](key);
  this.size -= result ? 1 : 0;
  return result;
}

/**
 * Gets the map value for `key`.
 *
 * @private
 * @name get
 * @memberOf MapCache
 * @param {string} key The key of the value to get.
 * @returns {*} Returns the entry value.
 */
function mapCacheGet(key) {
  return getMapData(this, key).get(key);
}

/**
 * Checks if a map value for `key` exists.
 *
 * @private
 * @name has
 * @memberOf MapCache
 * @param {string} key The key of the entry to check.
 * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
 */
function mapCacheHas(key) {
  return getMapData(this, key).has(key);
}

/**
 * Sets the map `key` to `value`.
 *
 * @private
 * @name set
 * @memberOf MapCache
 * @param {string} key The key of the value to set.
 * @param {*} value The value to set.
 * @returns {Object} Returns the map cache instance.
 */
function mapCacheSet(key, value) {
  var data = getMapData(this, key),
      size = data.size;

  data.set(key, value);
  this.size += data.size == size ? 0 : 1;
  return this;
}

/**
 * Creates a map cache object to store key-value pairs.
 *
 * @private
 * @constructor
 * @param {Array} [entries] The key-value pairs to cache.
 */
function MapCache(entries) {
  var index = -1,
      length = entries == null ? 0 : entries.length;

  this.clear();
  while (++index < length) {
    var entry = entries[index];
    this.set(entry[0], entry[1]);
  }
}

// Add methods to `MapCache`.
MapCache.prototype.clear = mapCacheClear;
MapCache.prototype['delete'] = mapCacheDelete;
MapCache.prototype.get = mapCacheGet;
MapCache.prototype.has = mapCacheHas;
MapCache.prototype.set = mapCacheSet;

/** Built-in value references. */
var getPrototype = overArg(Object.getPrototypeOf, Object);

/** `Object#toString` result references. */
var objectTag = '[object Object]';

/** Used for built-in method references. */
var funcProto = Function.prototype,
    objectProto = Object.prototype;

/** Used to resolve the decompiled source of functions. */
var funcToString = funcProto.toString;

/** Used to check objects for own properties. */
var hasOwnProperty = objectProto.hasOwnProperty;

/** Used to infer the `Object` constructor. */
var objectCtorString = funcToString.call(Object);

/**
 * Checks if `value` is a plain object, that is, an object created by the
 * `Object` constructor or one with a `[[Prototype]]` of `null`.
 *
 * @static
 * @memberOf _
 * @since 0.8.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a plain object, else `false`.
 * @example
 *
 * function Foo() {
 *   this.a = 1;
 * }
 *
 * _.isPlainObject(new Foo);
 * // => false
 *
 * _.isPlainObject([1, 2, 3]);
 * // => false
 *
 * _.isPlainObject({ 'x': 0, 'y': 0 });
 * // => true
 *
 * _.isPlainObject(Object.create(null));
 * // => true
 */
function isPlainObject(value) {
  if (!isObjectLike(value) || baseGetTag(value) != objectTag) {
    return false;
  }
  var proto = getPrototype(value);
  if (proto === null) {
    return true;
  }
  var Ctor = hasOwnProperty.call(proto, 'constructor') && proto.constructor;
  return typeof Ctor == 'function' && Ctor instanceof Ctor &&
    funcToString.call(Ctor) == objectCtorString;
}

/**
 * Removes all key-value entries from the stack.
 *
 * @private
 * @name clear
 * @memberOf Stack
 */
function stackClear() {
  this.__data__ = new ListCache;
  this.size = 0;
}

/**
 * Removes `key` and its value from the stack.
 *
 * @private
 * @name delete
 * @memberOf Stack
 * @param {string} key The key of the value to remove.
 * @returns {boolean} Returns `true` if the entry was removed, else `false`.
 */
function stackDelete(key) {
  var data = this.__data__,
      result = data['delete'](key);

  this.size = data.size;
  return result;
}

/**
 * Gets the stack value for `key`.
 *
 * @private
 * @name get
 * @memberOf Stack
 * @param {string} key The key of the value to get.
 * @returns {*} Returns the entry value.
 */
function stackGet(key) {
  return this.__data__.get(key);
}

/**
 * Checks if a stack value for `key` exists.
 *
 * @private
 * @name has
 * @memberOf Stack
 * @param {string} key The key of the entry to check.
 * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
 */
function stackHas(key) {
  return this.__data__.has(key);
}

/** Used as the size to enable large array optimizations. */
var LARGE_ARRAY_SIZE = 200;

/**
 * Sets the stack `key` to `value`.
 *
 * @private
 * @name set
 * @memberOf Stack
 * @param {string} key The key of the value to set.
 * @param {*} value The value to set.
 * @returns {Object} Returns the stack cache instance.
 */
function stackSet(key, value) {
  var data = this.__data__;
  if (data instanceof ListCache) {
    var pairs = data.__data__;
    if (!Map || (pairs.length < LARGE_ARRAY_SIZE - 1)) {
      pairs.push([key, value]);
      this.size = ++data.size;
      return this;
    }
    data = this.__data__ = new MapCache(pairs);
  }
  data.set(key, value);
  this.size = data.size;
  return this;
}

/**
 * Creates a stack cache object to store key-value pairs.
 *
 * @private
 * @constructor
 * @param {Array} [entries] The key-value pairs to cache.
 */
function Stack(entries) {
  var data = this.__data__ = new ListCache(entries);
  this.size = data.size;
}

// Add methods to `Stack`.
Stack.prototype.clear = stackClear;
Stack.prototype['delete'] = stackDelete;
Stack.prototype.get = stackGet;
Stack.prototype.has = stackHas;
Stack.prototype.set = stackSet;

/** Detect free variable `exports`. */
var freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;

/** Detect free variable `module`. */
var freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;

/** Detect the popular CommonJS extension `module.exports`. */
var moduleExports = freeModule && freeModule.exports === freeExports;

/** Built-in value references. */
var Buffer = moduleExports ? root.Buffer : undefined;
    Buffer ? Buffer.allocUnsafe : undefined;

/**
 * Creates a clone of  `buffer`.
 *
 * @private
 * @param {Buffer} buffer The buffer to clone.
 * @param {boolean} [isDeep] Specify a deep clone.
 * @returns {Buffer} Returns the cloned buffer.
 */
function cloneBuffer(buffer, isDeep) {
  {
    return buffer.slice();
  }
}

/** Built-in value references. */
var Uint8Array = root.Uint8Array;

/**
 * Creates a clone of `arrayBuffer`.
 *
 * @private
 * @param {ArrayBuffer} arrayBuffer The array buffer to clone.
 * @returns {ArrayBuffer} Returns the cloned array buffer.
 */
function cloneArrayBuffer(arrayBuffer) {
  var result = new arrayBuffer.constructor(arrayBuffer.byteLength);
  new Uint8Array(result).set(new Uint8Array(arrayBuffer));
  return result;
}

/**
 * Creates a clone of `typedArray`.
 *
 * @private
 * @param {Object} typedArray The typed array to clone.
 * @param {boolean} [isDeep] Specify a deep clone.
 * @returns {Object} Returns the cloned typed array.
 */
function cloneTypedArray(typedArray, isDeep) {
  var buffer = cloneArrayBuffer(typedArray.buffer) ;
  return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);
}

/**
 * Initializes an object clone.
 *
 * @private
 * @param {Object} object The object to clone.
 * @returns {Object} Returns the initialized clone.
 */
function initCloneObject(object) {
  return (typeof object.constructor == 'function' && !isPrototype(object))
    ? baseCreate(getPrototype(object))
    : {};
}

/**
 * Creates a base function for methods like `_.forIn` and `_.forOwn`.
 *
 * @private
 * @param {boolean} [fromRight] Specify iterating from right to left.
 * @returns {Function} Returns the new base function.
 */
function createBaseFor(fromRight) {
  return function(object, iteratee, keysFunc) {
    var index = -1,
        iterable = Object(object),
        props = keysFunc(object),
        length = props.length;

    while (length--) {
      var key = props[++index];
      if (iteratee(iterable[key], key, iterable) === false) {
        break;
      }
    }
    return object;
  };
}

/**
 * The base implementation of `baseForOwn` which iterates over `object`
 * properties returned by `keysFunc` and invokes `iteratee` for each property.
 * Iteratee functions may exit iteration early by explicitly returning `false`.
 *
 * @private
 * @param {Object} object The object to iterate over.
 * @param {Function} iteratee The function invoked per iteration.
 * @param {Function} keysFunc The function to get the keys of `object`.
 * @returns {Object} Returns `object`.
 */
var baseFor = createBaseFor();

/**
 * This function is like `assignValue` except that it doesn't assign
 * `undefined` values.
 *
 * @private
 * @param {Object} object The object to modify.
 * @param {string} key The key of the property to assign.
 * @param {*} value The value to assign.
 */
function assignMergeValue(object, key, value) {
  if ((value !== undefined && !eq(object[key], value)) ||
      (value === undefined && !(key in object))) {
    baseAssignValue(object, key, value);
  }
}

/**
 * This method is like `_.isArrayLike` except that it also checks if `value`
 * is an object.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is an array-like object,
 *  else `false`.
 * @example
 *
 * _.isArrayLikeObject([1, 2, 3]);
 * // => true
 *
 * _.isArrayLikeObject(document.body.children);
 * // => true
 *
 * _.isArrayLikeObject('abc');
 * // => false
 *
 * _.isArrayLikeObject(_.noop);
 * // => false
 */
function isArrayLikeObject(value) {
  return isObjectLike(value) && isArrayLike(value);
}

/**
 * Gets the value at `key`, unless `key` is "__proto__" or "constructor".
 *
 * @private
 * @param {Object} object The object to query.
 * @param {string} key The key of the property to get.
 * @returns {*} Returns the property value.
 */
function safeGet(object, key) {
  if (key === 'constructor' && typeof object[key] === 'function') {
    return;
  }

  if (key == '__proto__') {
    return;
  }

  return object[key];
}

/**
 * Converts `value` to a plain object flattening inherited enumerable string
 * keyed properties of `value` to own properties of the plain object.
 *
 * @static
 * @memberOf _
 * @since 3.0.0
 * @category Lang
 * @param {*} value The value to convert.
 * @returns {Object} Returns the converted plain object.
 * @example
 *
 * function Foo() {
 *   this.b = 2;
 * }
 *
 * Foo.prototype.c = 3;
 *
 * _.assign({ 'a': 1 }, new Foo);
 * // => { 'a': 1, 'b': 2 }
 *
 * _.assign({ 'a': 1 }, _.toPlainObject(new Foo));
 * // => { 'a': 1, 'b': 2, 'c': 3 }
 */
function toPlainObject(value) {
  return copyObject(value, keysIn(value));
}

/**
 * A specialized version of `baseMerge` for arrays and objects which performs
 * deep merges and tracks traversed objects enabling objects with circular
 * references to be merged.
 *
 * @private
 * @param {Object} object The destination object.
 * @param {Object} source The source object.
 * @param {string} key The key of the value to merge.
 * @param {number} srcIndex The index of `source`.
 * @param {Function} mergeFunc The function to merge values.
 * @param {Function} [customizer] The function to customize assigned values.
 * @param {Object} [stack] Tracks traversed source values and their merged
 *  counterparts.
 */
function baseMergeDeep(object, source, key, srcIndex, mergeFunc, customizer, stack) {
  var objValue = safeGet(object, key),
      srcValue = safeGet(source, key),
      stacked = stack.get(srcValue);

  if (stacked) {
    assignMergeValue(object, key, stacked);
    return;
  }
  var newValue = customizer
    ? customizer(objValue, srcValue, (key + ''), object, source, stack)
    : undefined;

  var isCommon = newValue === undefined;

  if (isCommon) {
    var isArr = isArray(srcValue),
        isBuff = !isArr && isBuffer(srcValue),
        isTyped = !isArr && !isBuff && isTypedArray(srcValue);

    newValue = srcValue;
    if (isArr || isBuff || isTyped) {
      if (isArray(objValue)) {
        newValue = objValue;
      }
      else if (isArrayLikeObject(objValue)) {
        newValue = copyArray(objValue);
      }
      else if (isBuff) {
        isCommon = false;
        newValue = cloneBuffer(srcValue);
      }
      else if (isTyped) {
        isCommon = false;
        newValue = cloneTypedArray(srcValue);
      }
      else {
        newValue = [];
      }
    }
    else if (isPlainObject(srcValue) || isArguments(srcValue)) {
      newValue = objValue;
      if (isArguments(objValue)) {
        newValue = toPlainObject(objValue);
      }
      else if (!isObject(objValue) || isFunction(objValue)) {
        newValue = initCloneObject(srcValue);
      }
    }
    else {
      isCommon = false;
    }
  }
  if (isCommon) {
    // Recursively merge objects and arrays (susceptible to call stack limits).
    stack.set(srcValue, newValue);
    mergeFunc(newValue, srcValue, srcIndex, customizer, stack);
    stack['delete'](srcValue);
  }
  assignMergeValue(object, key, newValue);
}

/**
 * The base implementation of `_.merge` without support for multiple sources.
 *
 * @private
 * @param {Object} object The destination object.
 * @param {Object} source The source object.
 * @param {number} srcIndex The index of `source`.
 * @param {Function} [customizer] The function to customize merged values.
 * @param {Object} [stack] Tracks traversed source values and their merged
 *  counterparts.
 */
function baseMerge(object, source, srcIndex, customizer, stack) {
  if (object === source) {
    return;
  }
  baseFor(source, function(srcValue, key) {
    stack || (stack = new Stack);
    if (isObject(srcValue)) {
      baseMergeDeep(object, source, key, srcIndex, baseMerge, customizer, stack);
    }
    else {
      var newValue = customizer
        ? customizer(safeGet(object, key), srcValue, (key + ''), object, source, stack)
        : undefined;

      if (newValue === undefined) {
        newValue = srcValue;
      }
      assignMergeValue(object, key, newValue);
    }
  }, keysIn);
}

/**
 * Used by `_.defaultsDeep` to customize its `_.merge` use to merge source
 * objects into destination objects that are passed thru.
 *
 * @private
 * @param {*} objValue The destination value.
 * @param {*} srcValue The source value.
 * @param {string} key The key of the property to merge.
 * @param {Object} object The parent object of `objValue`.
 * @param {Object} source The parent object of `srcValue`.
 * @param {Object} [stack] Tracks traversed source values and their merged
 *  counterparts.
 * @returns {*} Returns the value to assign.
 */
function customDefaultsMerge(objValue, srcValue, key, object, source, stack) {
  if (isObject(objValue) && isObject(srcValue)) {
    // Recursively merge objects and arrays (susceptible to call stack limits).
    stack.set(srcValue, objValue);
    baseMerge(objValue, srcValue, undefined, customDefaultsMerge, stack);
    stack['delete'](srcValue);
  }
  return objValue;
}

/**
 * This method is like `_.merge` except that it accepts `customizer` which
 * is invoked to produce the merged values of the destination and source
 * properties. If `customizer` returns `undefined`, merging is handled by the
 * method instead. The `customizer` is invoked with six arguments:
 * (objValue, srcValue, key, object, source, stack).
 *
 * **Note:** This method mutates `object`.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Object
 * @param {Object} object The destination object.
 * @param {...Object} sources The source objects.
 * @param {Function} customizer The function to customize assigned values.
 * @returns {Object} Returns `object`.
 * @example
 *
 * function customizer(objValue, srcValue) {
 *   if (_.isArray(objValue)) {
 *     return objValue.concat(srcValue);
 *   }
 * }
 *
 * var object = { 'a': [1], 'b': [2] };
 * var other = { 'a': [3], 'b': [4] };
 *
 * _.mergeWith(object, other, customizer);
 * // => { 'a': [1, 3], 'b': [2, 4] }
 */
var mergeWith = createAssigner(function(object, source, srcIndex, customizer) {
  baseMerge(object, source, srcIndex, customizer);
});

/**
 * This method is like `_.defaults` except that it recursively assigns
 * default properties.
 *
 * **Note:** This method mutates `object`.
 *
 * @static
 * @memberOf _
 * @since 3.10.0
 * @category Object
 * @param {Object} object The destination object.
 * @param {...Object} [sources] The source objects.
 * @returns {Object} Returns `object`.
 * @see _.defaults
 * @example
 *
 * _.defaultsDeep({ 'a': { 'b': 2 } }, { 'a': { 'b': 1, 'c': 3 } });
 * // => { 'a': { 'b': 2, 'c': 3 } }
 */
var defaultsDeep = baseRest(function(args) {
  args.push(undefined, customDefaultsMerge);
  return apply(mergeWith, undefined, args);
});

var PeriodType;
(function (PeriodType) {
    PeriodType[PeriodType["Custom"] = 1] = "Custom";
    PeriodType[PeriodType["Day"] = 10] = "Day";
    PeriodType[PeriodType["DayTime"] = 11] = "DayTime";
    PeriodType[PeriodType["TimeOnly"] = 15] = "TimeOnly";
    PeriodType[PeriodType["Week"] = 20] = "Week";
    PeriodType[PeriodType["WeekSun"] = 21] = "WeekSun";
    PeriodType[PeriodType["WeekMon"] = 22] = "WeekMon";
    PeriodType[PeriodType["WeekTue"] = 23] = "WeekTue";
    PeriodType[PeriodType["WeekWed"] = 24] = "WeekWed";
    PeriodType[PeriodType["WeekThu"] = 25] = "WeekThu";
    PeriodType[PeriodType["WeekFri"] = 26] = "WeekFri";
    PeriodType[PeriodType["WeekSat"] = 27] = "WeekSat";
    PeriodType[PeriodType["Month"] = 30] = "Month";
    PeriodType[PeriodType["MonthYear"] = 31] = "MonthYear";
    PeriodType[PeriodType["Quarter"] = 40] = "Quarter";
    PeriodType[PeriodType["CalendarYear"] = 50] = "CalendarYear";
    PeriodType[PeriodType["FiscalYearOctober"] = 60] = "FiscalYearOctober";
    PeriodType[PeriodType["BiWeek1"] = 70] = "BiWeek1";
    PeriodType[PeriodType["BiWeek1Sun"] = 71] = "BiWeek1Sun";
    PeriodType[PeriodType["BiWeek1Mon"] = 72] = "BiWeek1Mon";
    PeriodType[PeriodType["BiWeek1Tue"] = 73] = "BiWeek1Tue";
    PeriodType[PeriodType["BiWeek1Wed"] = 74] = "BiWeek1Wed";
    PeriodType[PeriodType["BiWeek1Thu"] = 75] = "BiWeek1Thu";
    PeriodType[PeriodType["BiWeek1Fri"] = 76] = "BiWeek1Fri";
    PeriodType[PeriodType["BiWeek1Sat"] = 77] = "BiWeek1Sat";
    PeriodType[PeriodType["BiWeek2"] = 80] = "BiWeek2";
    PeriodType[PeriodType["BiWeek2Sun"] = 81] = "BiWeek2Sun";
    PeriodType[PeriodType["BiWeek2Mon"] = 82] = "BiWeek2Mon";
    PeriodType[PeriodType["BiWeek2Tue"] = 83] = "BiWeek2Tue";
    PeriodType[PeriodType["BiWeek2Wed"] = 84] = "BiWeek2Wed";
    PeriodType[PeriodType["BiWeek2Thu"] = 85] = "BiWeek2Thu";
    PeriodType[PeriodType["BiWeek2Fri"] = 86] = "BiWeek2Fri";
    PeriodType[PeriodType["BiWeek2Sat"] = 87] = "BiWeek2Sat";
})(PeriodType || (PeriodType = {}));
const periodTypeMappings = {
    [PeriodType.Custom]: 'custom',
    [PeriodType.Day]: 'day',
    [PeriodType.DayTime]: 'daytime',
    [PeriodType.TimeOnly]: 'time',
    [PeriodType.WeekSun]: 'week-sun',
    [PeriodType.WeekMon]: 'week-mon',
    [PeriodType.WeekTue]: 'week-tue',
    [PeriodType.WeekWed]: 'week-wed',
    [PeriodType.WeekThu]: 'week-thu',
    [PeriodType.WeekFri]: 'week-fri',
    [PeriodType.WeekSat]: 'week-sat',
    [PeriodType.Week]: 'week',
    [PeriodType.Month]: 'month',
    [PeriodType.MonthYear]: 'month-year',
    [PeriodType.Quarter]: 'quarter',
    [PeriodType.CalendarYear]: 'year',
    [PeriodType.FiscalYearOctober]: 'fiscal-year-october',
    [PeriodType.BiWeek1Sun]: 'biweek1-sun',
    [PeriodType.BiWeek1Mon]: 'biweek1-mon',
    [PeriodType.BiWeek1Tue]: 'biweek1-tue',
    [PeriodType.BiWeek1Wed]: 'biweek1-wed',
    [PeriodType.BiWeek1Thu]: 'biweek1-thu',
    [PeriodType.BiWeek1Fri]: 'biweek1-fri',
    [PeriodType.BiWeek1Sat]: 'biweek1-sat',
    [PeriodType.BiWeek1]: 'biweek1',
    [PeriodType.BiWeek2Sun]: 'biweek2-sun',
    [PeriodType.BiWeek2Mon]: 'biweek2-mon',
    [PeriodType.BiWeek2Tue]: 'biweek2-tue',
    [PeriodType.BiWeek2Wed]: 'biweek2-wed',
    [PeriodType.BiWeek2Thu]: 'biweek2-thu',
    [PeriodType.BiWeek2Fri]: 'biweek2-fri',
    [PeriodType.BiWeek2Sat]: 'biweek2-sat',
    [PeriodType.BiWeek2]: 'biweek2',
};
var DayOfWeek;
(function (DayOfWeek) {
    DayOfWeek[DayOfWeek["Sunday"] = 0] = "Sunday";
    DayOfWeek[DayOfWeek["Monday"] = 1] = "Monday";
    DayOfWeek[DayOfWeek["Tuesday"] = 2] = "Tuesday";
    DayOfWeek[DayOfWeek["Wednesday"] = 3] = "Wednesday";
    DayOfWeek[DayOfWeek["Thursday"] = 4] = "Thursday";
    DayOfWeek[DayOfWeek["Friday"] = 5] = "Friday";
    DayOfWeek[DayOfWeek["Saturday"] = 6] = "Saturday";
})(DayOfWeek || (DayOfWeek = {}));
var DateToken;
(function (DateToken) {
    /** `1982, 1986, 2024` */
    DateToken["Year_numeric"] = "yyy";
    /** `82, 86, 24` */
    DateToken["Year_2Digit"] = "yy";
    /** `January, February, ..., December` */
    DateToken["Month_long"] = "MMMM";
    /** `Jan, Feb, ..., Dec` */
    DateToken["Month_short"] = "MMM";
    /** `01, 02, ..., 12` */
    DateToken["Month_2Digit"] = "MM";
    /** `1, 2, ..., 12` */
    DateToken["Month_numeric"] = "M";
    /** `1, 2, ..., 11, 12` */
    DateToken["Hour_numeric"] = "h";
    /** `01, 02, ..., 11, 12` */
    DateToken["Hour_2Digit"] = "hh";
    /** You should probably not use this. Force with AM/PM (and the good locale), not specifying this will automatically take the good local */
    DateToken["Hour_wAMPM"] = "a";
    /** You should probably not use this. Force without AM/PM (and the good locale), not specifying this will automatically take the good local */
    DateToken["Hour_woAMPM"] = "aaaaaa";
    /** `0, 1, ..., 59` */
    DateToken["Minute_numeric"] = "m";
    /** `00, 01, ..., 59` */
    DateToken["Minute_2Digit"] = "mm";
    /** `0, 1, ..., 59` */
    DateToken["Second_numeric"] = "s";
    /** `00, 01, ..., 59` */
    DateToken["Second_2Digit"] = "ss";
    /** `000, 001, ..., 999` */
    DateToken["MiliSecond_3"] = "SSS";
    /** Minimize digit: `1, 2, 11, ...` */
    DateToken["DayOfMonth_numeric"] = "d";
    /** `01, 02, 11, ...` */
    DateToken["DayOfMonth_2Digit"] = "dd";
    /** `1st, 2nd, 11th, ...` You can have your local ordinal by passing `ordinalSuffixes` in options / settings */
    DateToken["DayOfMonth_withOrdinal"] = "do";
    /** `M, T, W, T, F, S, S` */
    DateToken["DayOfWeek_narrow"] = "eeeee";
    /** `Monday, Tuesday, ..., Sunday` */
    DateToken["DayOfWeek_long"] = "eeee";
    /** `Mon, Tue, Wed, ..., Sun` */
    DateToken["DayOfWeek_short"] = "eee";
})(DateToken || (DateToken = {}));

function getWeekStartsOnFromIntl(locales) {
    if (!locales) {
        return DayOfWeek.Sunday;
    }
    const locale = new Intl.Locale(locales);
    // @ts-expect-error
    const weekInfo = locale.weekInfo ?? locale.getWeekInfo?.();
    return (weekInfo?.firstDay ?? 0) % 7; // (in Intl, sunday is 7 not 0, so we need to mod 7)
}

const defaultLocaleSettings = {
    locale: 'en',
    dictionary: {
        Ok: 'Ok',
        Cancel: 'Cancel',
        Date: {
            Start: 'Start',
            End: 'End',
            Empty: 'Empty',
            Day: 'Day',
            DayTime: 'Day Time',
            Time: 'Time',
            Week: 'Week',
            BiWeek: 'Bi-Week',
            Month: 'Month',
            Quarter: 'Quarter',
            CalendarYear: 'Calendar Year',
            FiscalYearOct: 'Fiscal Year (Oct)',
            PeriodDay: {
                Current: 'Today',
                Last: 'Yesterday',
                LastX: 'Last {0} days',
            },
            PeriodWeek: {
                Current: 'This week',
                Last: 'Last week',
                LastX: 'Last {0} weeks',
            },
            PeriodBiWeek: {
                Current: 'This bi-week',
                Last: 'Last bi-week',
                LastX: 'Last {0} bi-weeks',
            },
            PeriodMonth: {
                Current: 'This month',
                Last: 'Last month',
                LastX: 'Last {0} months',
            },
            PeriodQuarter: {
                Current: 'This quarter',
                Last: 'Last quarter',
                LastX: 'Last {0} quarters',
            },
            PeriodQuarterSameLastyear: 'Same quarter last year',
            PeriodYear: {
                Current: 'This year',
                Last: 'Last year',
                LastX: 'Last {0} years',
            },
            PeriodFiscalYear: {
                Current: 'This fiscal year',
                Last: 'Last fiscal year',
                LastX: 'Last {0} fiscal years',
            },
        },
    },
    formats: {
        numbers: {
            defaults: {
                currency: 'USD',
                fractionDigits: 2,
                currencyDisplay: 'symbol',
            },
        },
        dates: {
            baseParsing: 'MM/dd/yyyy',
            weekStartsOn: DayOfWeek.Sunday,
            ordinalSuffixes: {
                one: 'st',
                two: 'nd',
                few: 'rd',
                other: 'th',
            },
            presets: {
                day: {
                    short: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric],
                    default: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric, DateToken.Year_numeric],
                    long: [DateToken.DayOfMonth_numeric, DateToken.Month_short, DateToken.Year_numeric],
                },
                dayTime: {
                    short: [
                        DateToken.DayOfMonth_numeric,
                        DateToken.Month_numeric,
                        DateToken.Year_numeric,
                        DateToken.Hour_numeric,
                        DateToken.Minute_numeric,
                    ],
                    default: [
                        DateToken.DayOfMonth_numeric,
                        DateToken.Month_numeric,
                        DateToken.Year_numeric,
                        DateToken.Hour_2Digit,
                        DateToken.Minute_2Digit,
                    ],
                    long: [
                        DateToken.DayOfMonth_numeric,
                        DateToken.Month_numeric,
                        DateToken.Year_numeric,
                        DateToken.Hour_2Digit,
                        DateToken.Minute_2Digit,
                        DateToken.Second_2Digit,
                    ],
                },
                timeOnly: {
                    short: [DateToken.Hour_numeric, DateToken.Minute_numeric],
                    default: [DateToken.Hour_2Digit, DateToken.Minute_2Digit, DateToken.Second_2Digit],
                    long: [
                        DateToken.Hour_2Digit,
                        DateToken.Minute_2Digit,
                        DateToken.Second_2Digit,
                        DateToken.MiliSecond_3,
                    ],
                },
                week: {
                    short: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric],
                    default: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric, DateToken.Year_numeric],
                    long: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric, DateToken.Year_numeric],
                },
                month: {
                    short: DateToken.Month_short,
                    default: DateToken.Month_long,
                    long: [DateToken.Month_long, DateToken.Year_numeric],
                },
                monthsYear: {
                    short: [DateToken.Month_short, DateToken.Year_2Digit],
                    default: [DateToken.Month_long, DateToken.Year_numeric],
                    long: [DateToken.Month_long, DateToken.Year_numeric],
                },
                year: {
                    short: DateToken.Year_2Digit,
                    default: DateToken.Year_numeric,
                    long: DateToken.Year_numeric,
                },
            },
        },
    },
};
/** Creates a locale settings object, using the `base` locale settings as defaults.
 * If omitted, the `en` locale is used as the base. */
function createLocaleSettings(localeSettings, base = defaultLocaleSettings) {
    // if ordinalSuffixes is specified, we want to make sure that all are empty first
    if (localeSettings.formats?.dates?.ordinalSuffixes) {
        localeSettings.formats.dates.ordinalSuffixes = {
            one: '',
            two: '',
            few: '',
            other: '',
            zero: '',
            many: '',
            ...localeSettings.formats.dates.ordinalSuffixes,
        };
    }
    // if weekStartsOn is not specified, let's default to the local one
    if (localeSettings.formats?.dates?.weekStartsOn === undefined) {
        localeSettings = defaultsDeep(localeSettings, {
            formats: { dates: { weekStartsOn: getWeekStartsOnFromIntl(localeSettings.locale) } },
        });
    }
    return defaultsDeep(localeSettings, base);
}
const defaultLocale = createLocaleSettings({ locale: 'en' });

var DurationUnits;
(function (DurationUnits) {
    DurationUnits[DurationUnits["Year"] = 0] = "Year";
    DurationUnits[DurationUnits["Day"] = 1] = "Day";
    DurationUnits[DurationUnits["Hour"] = 2] = "Hour";
    DurationUnits[DurationUnits["Minute"] = 3] = "Minute";
    DurationUnits[DurationUnits["Second"] = 4] = "Second";
    DurationUnits[DurationUnits["Millisecond"] = 5] = "Millisecond";
})(DurationUnits || (DurationUnits = {}));
class Duration {
    #milliseconds = 0;
    #seconds = 0;
    #minutes = 0;
    #hours = 0;
    #days = 0;
    #years = 0;
    constructor(options = {}) {
        const startDate = typeof options.start === 'string' ? parseISO(options.start) : options.start;
        const endDate = typeof options.end === 'string' ? parseISO(options.end) : options.end;
        const differenceInMs = startDate
            ? Math.abs(Number(endDate || new Date()) - Number(startDate))
            : undefined;
        if (!Number.isFinite(differenceInMs) && options.duration == null) {
            return;
        }
        this.#milliseconds = options.duration?.milliseconds ?? differenceInMs ?? 0;
        this.#seconds = options.duration?.seconds ?? 0;
        this.#minutes = options.duration?.minutes ?? 0;
        this.#hours = options.duration?.hours ?? 0;
        this.#days = options.duration?.days ?? 0;
        this.#years = options.duration?.years ?? 0;
        if (this.#milliseconds >= 1000) {
            const carrySeconds = (this.#milliseconds - (this.#milliseconds % 1000)) / 1000;
            this.#seconds += carrySeconds;
            this.#milliseconds = this.#milliseconds - carrySeconds * 1000;
        }
        if (this.#seconds >= 60) {
            const carryMinutes = (this.#seconds - (this.#seconds % 60)) / 60;
            this.#minutes += carryMinutes;
            this.#seconds = this.#seconds - carryMinutes * 60;
        }
        if (this.#minutes >= 60) {
            const carryHours = (this.#minutes - (this.#minutes % 60)) / 60;
            this.#hours += carryHours;
            this.#minutes = this.#minutes - carryHours * 60;
        }
        if (this.#hours >= 24) {
            const carryDays = (this.#hours - (this.#hours % 24)) / 24;
            this.#days += carryDays;
            this.#hours = this.#hours - carryDays * 24;
        }
        if (this.#days >= 365) {
            const carryYears = (this.#days - (this.#days % 365)) / 365;
            this.#years += carryYears;
            this.#days = this.#days - carryYears * 365;
        }
    }
    get years() {
        return this.#years;
    }
    get days() {
        return this.#days;
    }
    get hours() {
        return this.#hours;
    }
    get minutes() {
        return this.#minutes;
    }
    get seconds() {
        return this.#seconds;
    }
    get milliseconds() {
        return this.#milliseconds;
    }
    valueOf() {
        return (this.#milliseconds +
            this.#seconds * 1000 +
            this.#minutes * 60 * 1000 +
            this.#hours * 60 * 60 * 1000 +
            this.#days * 24 * 60 * 60 * 1000 +
            this.#years * 365 * 24 * 60 * 60 * 1000);
    }
    toJSON() {
        return {
            years: this.#years,
            days: this.#days,
            hours: this.#hours,
            minutes: this.#minutes,
            seconds: this.#seconds,
            milliseconds: this.#milliseconds,
        };
    }
    format(options = {}) {
        const { minUnits, totalUnits = 99, variant = 'short' } = options;
        var sentenceArr = [];
        var unitNames = variant === 'short'
            ? ['y', 'd', 'h', 'm', 's', 'ms']
            : ['years', 'days', 'hours', 'minutes', 'seconds', 'milliseconds'];
        var unitNums = [
            this.years,
            this.days,
            this.hours,
            this.minutes,
            this.seconds,
            this.milliseconds,
        ].filter((x, i) => i <= (minUnits ?? 99));
        // Combine unit numbers and names
        for (var i in unitNums) {
            if (sentenceArr.length >= totalUnits) {
                break;
            }
            const unitNum = unitNums[i];
            let unitName = unitNames[i];
            // Hide `0` values unless last unit (and none shown before)
            if (unitNum !== 0 || (sentenceArr.length === 0 && Number(i) === unitNums.length - 1)) {
                switch (variant) {
                    case 'short':
                        sentenceArr.push(unitNum + unitName);
                        break;
                    case 'long':
                        if (unitNum === 1) {
                            // Trim off plural `s`
                            unitName = unitName.slice(0, -1);
                        }
                        sentenceArr.push(unitNum + ' ' + unitName);
                        break;
                }
            }
        }
        const sentence = sentenceArr.join(variant === 'long' ? ' and ' : ' ');
        return sentence;
    }
    toString() {
        return this.format();
    }
}

[50, ...range(100, 1000, 100)];

/**
 * Wrapper around `tailwind-merge` and `clsx`
 */
const twMerge = extendTailwindMerge({
    extend: {
        classGroups: {
            shadow: [
                'shadow-border-l',
                'shadow-border-r',
                'shadow-border-t',
                'shadow-border-b',
                'elevation-none',
                ...range(1, 25).map((x) => `elevation-${x}`),
            ],
        },
    },
});
const cls = (...inputs) => twMerge(clsx(...inputs));

function ownKeys(object, enumerableOnly) {
  var keys = Object.keys(object);

  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);

    if (enumerableOnly) {
      symbols = symbols.filter(function (sym) {
        return Object.getOwnPropertyDescriptor(object, sym).enumerable;
      });
    }

    keys.push.apply(keys, symbols);
  }

  return keys;
}

function _objectSpread2(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? arguments[i] : {};

    if (i % 2) {
      ownKeys(Object(source), true).forEach(function (key) {
        _defineProperty(target, key, source[key]);
      });
    } else if (Object.getOwnPropertyDescriptors) {
      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
      ownKeys(Object(source)).forEach(function (key) {
        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
      });
    }
  }

  return target;
}

function _typeof(obj) {
  "@babel/helpers - typeof";

  if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") {
    _typeof = function (obj) {
      return typeof obj;
    };
  } else {
    _typeof = function (obj) {
      return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj;
    };
  }

  return _typeof(obj);
}

function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value: value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }

  return obj;
}

function _extends() {
  _extends = Object.assign || function (target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];

      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }

    return target;
  };

  return _extends.apply(this, arguments);
}

function _unsupportedIterableToArray(o, minLen) {
  if (!o) return;
  if (typeof o === "string") return _arrayLikeToArray(o, minLen);
  var n = Object.prototype.toString.call(o).slice(8, -1);
  if (n === "Object" && o.constructor) n = o.constructor.name;
  if (n === "Map" || n === "Set") return Array.from(o);
  if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);
}

function _arrayLikeToArray(arr, len) {
  if (len == null || len > arr.length) len = arr.length;

  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];

  return arr2;
}

function _createForOfIteratorHelper(o, allowArrayLike) {
  var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"];

  if (!it) {
    if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike) {
      if (it) o = it;
      var i = 0;

      var F = function () {};

      return {
        s: F,
        n: function () {
          if (i >= o.length) return {
            done: true
          };
          return {
            done: false,
            value: o[i++]
          };
        },
        e: function (e) {
          throw e;
        },
        f: F
      };
    }

    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }

  var normalCompletion = true,
      didErr = false,
      err;
  return {
    s: function () {
      it = it.call(o);
    },
    n: function () {
      var step = it.next();
      normalCompletion = step.done;
      return step;
    },
    e: function (e) {
      didErr = true;
      err = e;
    },
    f: function () {
      try {
        if (!normalCompletion && it.return != null) it.return();
      } finally {
        if (didErr) throw err;
      }
    }
  };
}

/**
 * de Casteljau's algorithm for drawing and splitting bezier curves.
 * Inspired by https://pomax.github.io/bezierinfo/
 *
 * @param {Number[][]} points Array of [x,y] points: [start, control1, control2, ..., end]
 *   The original segment to split.
 * @param {Number} t Where to split the curve (value between [0, 1])
 * @return {Object} An object { left, right } where left is the segment from 0..t and
 *   right is the segment from t..1.
 */
function decasteljau(points, t) {
  var left = [];
  var right = [];

  function decasteljauRecurse(points, t) {
    if (points.length === 1) {
      left.push(points[0]);
      right.push(points[0]);
    } else {
      var newPoints = Array(points.length - 1);

      for (var i = 0; i < newPoints.length; i++) {
        if (i === 0) {
          left.push(points[0]);
        }

        if (i === newPoints.length - 1) {
          right.push(points[i + 1]);
        }

        newPoints[i] = [(1 - t) * points[i][0] + t * points[i + 1][0], (1 - t) * points[i][1] + t * points[i + 1][1]];
      }

      decasteljauRecurse(newPoints, t);
    }
  }

  if (points.length) {
    decasteljauRecurse(points, t);
  }

  return {
    left: left,
    right: right.reverse()
  };
}
/**
 * Convert segments represented as points back into a command object
 *
 * @param {Number[][]} points Array of [x,y] points: [start, control1, control2, ..., end]
 *   Represents a segment
 * @return {Object} A command object representing the segment.
 */


function pointsToCommand(points) {
  var command = {};

  if (points.length === 4) {
    command.x2 = points[2][0];
    command.y2 = points[2][1];
  }

  if (points.length >= 3) {
    command.x1 = points[1][0];
    command.y1 = points[1][1];
  }

  command.x = points[points.length - 1][0];
  command.y = points[points.length - 1][1];

  if (points.length === 4) {
    // start, control1, control2, end
    command.type = 'C';
  } else if (points.length === 3) {
    // start, control, end
    command.type = 'Q';
  } else {
    // start, end
    command.type = 'L';
  }

  return command;
}
/**
 * Runs de Casteljau's algorithm enough times to produce the desired number of segments.
 *
 * @param {Number[][]} points Array of [x,y] points for de Casteljau (the initial segment to split)
 * @param {Number} segmentCount Number of segments to split the original into
 * @return {Number[][][]} Array of segments
 */


function splitCurveAsPoints(points, segmentCount) {
  segmentCount = segmentCount || 2;
  var segments = [];
  var remainingCurve = points;
  var tIncrement = 1 / segmentCount; // x-----x-----x-----x
  // t=  0.33   0.66   1
  // x-----o-----------x
  // r=  0.33
  //       x-----o-----x
  // r=         0.5  (0.33 / (1 - 0.33))  === tIncrement / (1 - (tIncrement * (i - 1))
  // x-----x-----x-----x----x
  // t=  0.25   0.5   0.75  1
  // x-----o----------------x
  // r=  0.25
  //       x-----o----------x
  // r=         0.33  (0.25 / (1 - 0.25))
  //             x-----o----x
  // r=         0.5  (0.25 / (1 - 0.5))

  for (var i = 0; i < segmentCount - 1; i++) {
    var tRelative = tIncrement / (1 - tIncrement * i);
    var split = decasteljau(remainingCurve, tRelative);
    segments.push(split.left);
    remainingCurve = split.right;
  } // last segment is just to the end from the last point


  segments.push(remainingCurve);
  return segments;
}
/**
 * Convert command objects to arrays of points, run de Casteljau's algorithm on it
 * to split into to the desired number of segments.
 *
 * @param {Object} commandStart The start command object
 * @param {Object} commandEnd The end command object
 * @param {Number} segmentCount The number of segments to create
 * @return {Object[]} An array of commands representing the segments in sequence
 */


function splitCurve(commandStart, commandEnd, segmentCount) {
  var points = [[commandStart.x, commandStart.y]];

  if (commandEnd.x1 != null) {
    points.push([commandEnd.x1, commandEnd.y1]);
  }

  if (commandEnd.x2 != null) {
    points.push([commandEnd.x2, commandEnd.y2]);
  }

  points.push([commandEnd.x, commandEnd.y]);
  return splitCurveAsPoints(points, segmentCount).map(pointsToCommand);
}

var commandTokenRegex = /[MLCSTQAHVZmlcstqahv]|-?[\d.e+-]+/g;
/**
 * List of params for each command type in a path `d` attribute
 */

var typeMap = {
  M: ['x', 'y'],
  L: ['x', 'y'],
  H: ['x'],
  V: ['y'],
  C: ['x1', 'y1', 'x2', 'y2', 'x', 'y'],
  S: ['x2', 'y2', 'x', 'y'],
  Q: ['x1', 'y1', 'x', 'y'],
  T: ['x', 'y'],
  A: ['rx', 'ry', 'xAxisRotation', 'largeArcFlag', 'sweepFlag', 'x', 'y'],
  Z: []
}; // Add lower case entries too matching uppercase (e.g. 'm' == 'M')

Object.keys(typeMap).forEach(function (key) {
  typeMap[key.toLowerCase()] = typeMap[key];
});

function arrayOfLength(length, value) {
  var array = Array(length);

  for (var i = 0; i < length; i++) {
    array[i] = value;
  }

  return array;
}
/**
 * Converts a command object to a string to be used in a `d` attribute
 * @param {Object} command A command object
 * @return {String} The string for the `d` attribute
 */


function commandToString(command) {
  return "".concat(command.type).concat(typeMap[command.type].map(function (p) {
    return command[p];
  }).join(','));
}
/**
 * Converts command A to have the same type as command B.
 *
 * e.g., L0,5 -> C0,5,0,5,0,5
 *
 * Uses these rules:
 * x1 <- x
 * x2 <- x
 * y1 <- y
 * y2 <- y
 * rx <- 0
 * ry <- 0
 * xAxisRotation <- read from B
 * largeArcFlag <- read from B
 * sweepflag <- read from B
 *
 * @param {Object} aCommand Command object from path `d` attribute
 * @param {Object} bCommand Command object from path `d` attribute to match against
 * @return {Object} aCommand converted to type of bCommand
 */


function convertToSameType(aCommand, bCommand) {
  var conversionMap = {
    x1: 'x',
    y1: 'y',
    x2: 'x',
    y2: 'y'
  };
  var readFromBKeys = ['xAxisRotation', 'largeArcFlag', 'sweepFlag']; // convert (but ignore M types)

  if (aCommand.type !== bCommand.type && bCommand.type.toUpperCase() !== 'M') {
    var aConverted = {};
    Object.keys(bCommand).forEach(function (bKey) {
      var bValue = bCommand[bKey]; // first read from the A command

      var aValue = aCommand[bKey]; // if it is one of these values, read from B no matter what

      if (aValue === undefined) {
        if (readFromBKeys.includes(bKey)) {
          aValue = bValue;
        } else {
          // if it wasn't in the A command, see if an equivalent was
          if (aValue === undefined && conversionMap[bKey]) {
            aValue = aCommand[conversionMap[bKey]];
          } // if it doesn't have a converted value, use 0


          if (aValue === undefined) {
            aValue = 0;
          }
        }
      }

      aConverted[bKey] = aValue;
    }); // update the type to match B

    aConverted.type = bCommand.type;
    aCommand = aConverted;
  }

  return aCommand;
}
/**
 * Interpolate between command objects commandStart and commandEnd segmentCount times.
 * If the types are L, Q, or C then the curves are split as per de Casteljau's algorithm.
 * Otherwise we just copy commandStart segmentCount - 1 times, finally ending with commandEnd.
 *
 * @param {Object} commandStart Command object at the beginning of the segment
 * @param {Object} commandEnd Command object at the end of the segment
 * @param {Number} segmentCount The number of segments to split this into. If only 1
 *   Then [commandEnd] is returned.
 * @return {Object[]} Array of ~segmentCount command objects between commandStart and
 *   commandEnd. (Can be segmentCount+1 objects if commandStart is type M).
 */


function splitSegment(commandStart, commandEnd, segmentCount) {
  var segments = []; // line, quadratic bezier, or cubic bezier

  if (commandEnd.type === 'L' || commandEnd.type === 'Q' || commandEnd.type === 'C') {
    segments = segments.concat(splitCurve(commandStart, commandEnd, segmentCount)); // general case - just copy the same point
  } else {
    var copyCommand = _extends({}, commandStart); // convert M to L


    if (copyCommand.type === 'M') {
      copyCommand.type = 'L';
    }

    segments = segments.concat(arrayOfLength(segmentCount - 1).map(function () {
      return copyCommand;
    }));
    segments.push(commandEnd);
  }

  return segments;
}
/**
 * Extends an array of commandsToExtend to the length of the referenceCommands by
 * splitting segments until the number of commands match. Ensures all the actual
 * points of commandsToExtend are in the extended array.
 *
 * @param {Object[]} commandsToExtend The command object array to extend
 * @param {Object[]} referenceCommands The command object array to match in length
 * @param {Function} excludeSegment a function that takes a start command object and
 *   end command object and returns true if the segment should be excluded from splitting.
 * @return {Object[]} The extended commandsToExtend array
 */


function extend(commandsToExtend, referenceCommands, excludeSegment) {
  // compute insertion points:
  // number of segments in the path to extend
  var numSegmentsToExtend = commandsToExtend.length - 1; // number of segments in the reference path.

  var numReferenceSegments = referenceCommands.length - 1; // this value is always between [0, 1].

  var segmentRatio = numSegmentsToExtend / numReferenceSegments; // create a map, mapping segments in referenceCommands to how many points
  // should be added in that segment (should always be >= 1 since we need each
  // point itself).
  // 0 = segment 0-1, 1 = segment 1-2, n-1 = last vertex

  var countPointsPerSegment = arrayOfLength(numReferenceSegments).reduce(function (accum, d, i) {
    var insertIndex = Math.floor(segmentRatio * i); // handle excluding segments

    if (excludeSegment && insertIndex < commandsToExtend.length - 1 && excludeSegment(commandsToExtend[insertIndex], commandsToExtend[insertIndex + 1])) {
      // set the insertIndex to the segment that this point should be added to:
      // round the insertIndex essentially so we split half and half on
      // neighbouring segments. hence the segmentRatio * i < 0.5
      var addToPriorSegment = segmentRatio * i % 1 < 0.5; // only skip segment if we already have 1 point in it (can't entirely remove a segment)

      if (accum[insertIndex]) {
        // TODO - Note this is a naive algorithm that should work for most d3-area use cases
        // but if two adjacent segments are supposed to be skipped, this will not perform as
        // expected. Could be updated to search for nearest segment to place the point in, but
        // will only do that if necessary.
        // add to the prior segment
        if (addToPriorSegment) {
          if (insertIndex > 0) {
            insertIndex -= 1; // not possible to add to previous so adding to next
          } else if (insertIndex < commandsToExtend.length - 1) {
            insertIndex += 1;
          } // add to next segment

        } else if (insertIndex < commandsToExtend.length - 1) {
          insertIndex += 1; // not possible to add to next so adding to previous
        } else if (insertIndex > 0) {
          insertIndex -= 1;
        }
      }
    }

    accum[insertIndex] = (accum[insertIndex] || 0) + 1;
    return accum;
  }, []); // extend each segment to have the correct number of points for a smooth interpolation

  var extended = countPointsPerSegment.reduce(function (extended, segmentCount, i) {
    // if last command, just add `segmentCount` number of times
    if (i === commandsToExtend.length - 1) {
      var lastCommandCopies = arrayOfLength(segmentCount, _extends({}, commandsToExtend[commandsToExtend.length - 1])); // convert M to L

      if (lastCommandCopies[0].type === 'M') {
        lastCommandCopies.forEach(function (d) {
          d.type = 'L';
        });
      }

      return extended.concat(lastCommandCopies);
    } // otherwise, split the segment segmentCount times.


    return extended.concat(splitSegment(commandsToExtend[i], commandsToExtend[i + 1], segmentCount));
  }, []); // add in the very first point since splitSegment only adds in the ones after it

  extended.unshift(commandsToExtend[0]);
  return extended;
}
/**
 * Takes a path `d` string and converts it into an array of command
 * objects. Drops the `Z` character.
 *
 * @param {String|null} d A path `d` string
 */


function pathCommandsFromString(d) {
  // split into valid tokens
  var tokens = (d || '').match(commandTokenRegex) || [];
  var commands = [];
  var commandArgs;
  var command; // iterate over each token, checking if we are at a new command
  // by presence in the typeMap

  for (var i = 0; i < tokens.length; ++i) {
    commandArgs = typeMap[tokens[i]]; // new command found:

    if (commandArgs) {
      command = {
        type: tokens[i]
      }; // add each of the expected args for this command:

      for (var a = 0; a < commandArgs.length; ++a) {
        command[commandArgs[a]] = +tokens[i + a + 1];
      } // need to increment our token index appropriately since
      // we consumed token args


      i += commandArgs.length;
      commands.push(command);
    }
  }

  return commands;
}
/**
 * Interpolate from A to B by extending A and B during interpolation to have
 * the same number of points. This allows for a smooth transition when they
 * have a different number of points.
 *
 * Ignores the `Z` command in paths unless both A and B end with it.
 *
 * This function works directly with arrays of command objects instead of with
 * path `d` strings (see interpolatePath for working with `d` strings).
 *
 * @param {Object[]} aCommandsInput Array of path commands
 * @param {Object[]} bCommandsInput Array of path commands
 * @param {(Function|Object)} interpolateOptions
 * @param {Function} interpolateOptions.excludeSegment a function that takes a start command object and
 *   end command object and returns true if the segment should be excluded from splitting.
 * @param {Boolean} interpolateOptions.snapEndsToInput a boolean indicating whether end of input should
 *   be sourced from input argument or computed.
 * @returns {Function} Interpolation function that maps t ([0, 1]) to an array of path commands.
 */

function interpolatePathCommands(aCommandsInput, bCommandsInput, interpolateOptions) {
  // make a copy so we don't mess with the input arrays
  var aCommands = aCommandsInput == null ? [] : aCommandsInput.slice();
  var bCommands = bCommandsInput == null ? [] : bCommandsInput.slice();

  var _ref = _typeof(interpolateOptions) === 'object' ? interpolateOptions : {
    excludeSegment: interpolateOptions,
    snapEndsToInput: true
  },
      excludeSegment = _ref.excludeSegment,
      snapEndsToInput = _ref.snapEndsToInput; // both input sets are empty, so we don't interpolate


  if (!aCommands.length && !bCommands.length) {
    return function nullInterpolator() {
      return [];
    };
  } // do we add Z during interpolation? yes if both have it. (we'd expect both to have it or not)


  var addZ = (aCommands.length === 0 || aCommands[aCommands.length - 1].type === 'Z') && (bCommands.length === 0 || bCommands[bCommands.length - 1].type === 'Z'); // we temporarily remove Z

  if (aCommands.length > 0 && aCommands[aCommands.length - 1].type === 'Z') {
    aCommands.pop();
  }

  if (bCommands.length > 0 && bCommands[bCommands.length - 1].type === 'Z') {
    bCommands.pop();
  } // if A is empty, treat it as if it used to contain just the first point
  // of B. This makes it so the line extends out of from that first point.


  if (!aCommands.length) {
    aCommands.push(bCommands[0]); // otherwise if B is empty, treat it as if it contains the first point
    // of A. This makes it so the line retracts into the first point.
  } else if (!bCommands.length) {
    bCommands.push(aCommands[0]);
  } // extend to match equal size


  var numPointsToExtend = Math.abs(bCommands.length - aCommands.length);

  if (numPointsToExtend !== 0) {
    // B has more points than A, so add points to A before interpolating
    if (bCommands.length > aCommands.length) {
      aCommands = extend(aCommands, bCommands, excludeSegment); // else if A has more points than B, add more points to B
    } else if (bCommands.length < aCommands.length) {
      bCommands = extend(bCommands, aCommands, excludeSegment);
    }
  } // commands have same length now.
  // convert commands in A to the same type as those in B


  aCommands = aCommands.map(function (aCommand, i) {
    return convertToSameType(aCommand, bCommands[i]);
  }); // create mutable interpolated command objects

  var interpolatedCommands = aCommands.map(function (aCommand) {
    return _objectSpread2({}, aCommand);
  });

  if (addZ) {
    interpolatedCommands.push({
      type: 'Z'
    });
    aCommands.push({
      type: 'Z'
    }); // required for when returning at t == 0
  }

  return function pathCommandInterpolator(t) {
    // at 1 return the final value without the extensions used during interpolation
    if (t === 1 && snapEndsToInput) {
      return bCommandsInput == null ? [] : bCommandsInput;
    } // work with aCommands directly since interpolatedCommands are mutated


    if (t === 0) {
      return aCommands;
    } // interpolate the commands using the mutable interpolated command objs


    for (var i = 0; i < interpolatedCommands.length; ++i) {
      // if (interpolatedCommands[i].type === 'Z') continue;
      var aCommand = aCommands[i];
      var bCommand = bCommands[i];
      var interpolatedCommand = interpolatedCommands[i];

      var _iterator = _createForOfIteratorHelper(typeMap[interpolatedCommand.type]),
          _step;

      try {
        for (_iterator.s(); !(_step = _iterator.n()).done;) {
          var arg = _step.value;
          interpolatedCommand[arg] = (1 - t) * aCommand[arg] + t * bCommand[arg]; // do not use floats for flags (#27), round to integer

          if (arg === 'largeArcFlag' || arg === 'sweepFlag') {
            interpolatedCommand[arg] = Math.round(interpolatedCommand[arg]);
          }
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
    }

    return interpolatedCommands;
  };
}
/** @typedef InterpolateOptions  */

/**
 * Interpolate from A to B by extending A and B during interpolation to have
 * the same number of points. This allows for a smooth transition when they
 * have a different number of points.
 *
 * Ignores the `Z` character in paths unless both A and B end with it.
 *
 * @param {String} a The `d` attribute for a path
 * @param {String} b The `d` attribute for a path
 * @param {((command1, command2) => boolean|{
 *   excludeSegment?: (command1, command2) => boolean;
 *   snapEndsToInput?: boolean
 * })} interpolateOptions The excludeSegment function or an options object
 *    - interpolateOptions.excludeSegment a function that takes a start command object and
 *      end command object and returns true if the segment should be excluded from splitting.
 *    - interpolateOptions.snapEndsToInput a boolean indicating whether end of input should
 *      be sourced from input argument or computed.
 * @returns {Function} Interpolation function that maps t ([0, 1]) to a path `d` string.
 */

function interpolatePath(a, b, interpolateOptions) {
  var aCommands = pathCommandsFromString(a);
  var bCommands = pathCommandsFromString(b);

  var _ref2 = _typeof(interpolateOptions) === 'object' ? interpolateOptions : {
    excludeSegment: interpolateOptions,
    snapEndsToInput: true
  },
      excludeSegment = _ref2.excludeSegment,
      snapEndsToInput = _ref2.snapEndsToInput;

  if (!aCommands.length && !bCommands.length) {
    return function nullInterpolator() {
      return '';
    };
  }

  var commandInterpolator = interpolatePathCommands(aCommands, bCommands, {
    excludeSegment: excludeSegment,
    snapEndsToInput: snapEndsToInput
  });
  return function pathStringInterpolator(t) {
    // at 1 return the final value without the extensions used during interpolation
    if (t === 1 && snapEndsToInput) {
      return b == null ? '' : b;
    }

    var interpolatedCommands = commandInterpolator(t); // convert to a string (fastest concat: https://jsperf.com/join-concat/150)

    var interpolatedString = '';

    var _iterator2 = _createForOfIteratorHelper(interpolatedCommands),
        _step2;

    try {
      for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
        var interpolatedCommand = _step2.value;
        interpolatedString += commandToString(interpolatedCommand);
      }
    } catch (err) {
      _iterator2.e(err);
    } finally {
      _iterator2.f();
    }

    return interpolatedString;
  };
}

var graph;
var hasRequiredGraph;

function requireGraph () {
	if (hasRequiredGraph) return graph;
	hasRequiredGraph = 1;

	var DEFAULT_EDGE_NAME = "\x00";
	var GRAPH_NODE = "\x00";
	var EDGE_KEY_DELIM = "\x01";

	// Implementation notes:
	//
	//  * Node id query functions should return string ids for the nodes
	//  * Edge id query functions should return an "edgeObj", edge object, that is
	//    composed of enough information to uniquely identify an edge: {v, w, name}.
	//  * Internally we use an "edgeId", a stringified form of the edgeObj, to
	//    reference edges. This is because we need a performant way to look these
	//    edges up and, object properties, which have string keys, are the closest
	//    we're going to get to a performant hashtable in JavaScript.

	class Graph {
	  _isDirected = true;
	  _isMultigraph = false;
	  _isCompound = false;

	  // Label for the graph itself
	  _label;

	  // Defaults to be set when creating a new node
	  _defaultNodeLabelFn = () => undefined;

	  // Defaults to be set when creating a new edge
	  _defaultEdgeLabelFn = () => undefined;

	  // v -> label
	  _nodes = {};

	  // v -> edgeObj
	  _in = {};

	  // u -> v -> Number
	  _preds = {};

	  // v -> edgeObj
	  _out = {};

	  // v -> w -> Number
	  _sucs = {};

	  // e -> edgeObj
	  _edgeObjs = {};

	  // e -> label
	  _edgeLabels = {};

	  /* Number of nodes in the graph. Should only be changed by the implementation. */
	  _nodeCount = 0;

	  /* Number of edges in the graph. Should only be changed by the implementation. */
	  _edgeCount = 0;

	  _parent;

	  _children;

	  constructor(opts) {
	    if (opts) {
	      this._isDirected = Object.hasOwn(opts, "directed") ? opts.directed : true;
	      this._isMultigraph = Object.hasOwn(opts, "multigraph") ? opts.multigraph : false;
	      this._isCompound = Object.hasOwn(opts, "compound") ? opts.compound : false;
	    }

	    if (this._isCompound) {
	      // v -> parent
	      this._parent = {};

	      // v -> children
	      this._children = {};
	      this._children[GRAPH_NODE] = {};
	    }
	  }

	  /* === Graph functions ========= */

	  /**
	   * Whether graph was created with 'directed' flag set to true or not.
	   */
	  isDirected() {
	    return this._isDirected;
	  }

	  /**
	   * Whether graph was created with 'multigraph' flag set to true or not.
	   */
	  isMultigraph() {
	    return this._isMultigraph;
	  }

	  /**
	   * Whether graph was created with 'compound' flag set to true or not.
	   */
	  isCompound() {
	    return this._isCompound;
	  }

	  /**
	   * Sets the label of the graph.
	   */
	  setGraph(label) {
	    this._label = label;
	    return this;
	  }

	  /**
	   * Gets the graph label.
	   */
	  graph() {
	    return this._label;
	  }


	  /* === Node functions ========== */

	  /**
	   * Sets the default node label. If newDefault is a function, it will be
	   * invoked ach time when setting a label for a node. Otherwise, this label
	   * will be assigned as default label in case if no label was specified while
	   * setting a node.
	   * Complexity: O(1).
	   */
	  setDefaultNodeLabel(newDefault) {
	    this._defaultNodeLabelFn = newDefault;
	    if (typeof newDefault !== 'function') {
	      this._defaultNodeLabelFn = () => newDefault;
	    }

	    return this;
	  }

	  /**
	   * Gets the number of nodes in the graph.
	   * Complexity: O(1).
	   */
	  nodeCount() {
	    return this._nodeCount;
	  }

	  /**
	   * Gets all nodes of the graph. Note, the in case of compound graph subnodes are
	   * not included in list.
	   * Complexity: O(1).
	   */
	  nodes() {
	    return Object.keys(this._nodes);
	  }

	  /**
	   * Gets list of nodes without in-edges.
	   * Complexity: O(|V|).
	   */
	  sources() {
	    var self = this;
	    return this.nodes().filter(v => Object.keys(self._in[v]).length === 0);
	  }

	  /**
	   * Gets list of nodes without out-edges.
	   * Complexity: O(|V|).
	   */
	  sinks() {
	    var self = this;
	    return this.nodes().filter(v => Object.keys(self._out[v]).length === 0);
	  }

	  /**
	   * Invokes setNode method for each node in names list.
	   * Complexity: O(|names|).
	   */
	  setNodes(vs, value) {
	    var args = arguments;
	    var self = this;
	    vs.forEach(function(v) {
	      if (args.length > 1) {
	        self.setNode(v, value);
	      } else {
	        self.setNode(v);
	      }
	    });
	    return this;
	  }

	  /**
	   * Creates or updates the value for the node v in the graph. If label is supplied
	   * it is set as the value for the node. If label is not supplied and the node was
	   * created by this call then the default node label will be assigned.
	   * Complexity: O(1).
	   */
	  setNode(v, value) {
	    if (Object.hasOwn(this._nodes, v)) {
	      if (arguments.length > 1) {
	        this._nodes[v] = value;
	      }
	      return this;
	    }

	    this._nodes[v] = arguments.length > 1 ? value : this._defaultNodeLabelFn(v);
	    if (this._isCompound) {
	      this._parent[v] = GRAPH_NODE;
	      this._children[v] = {};
	      this._children[GRAPH_NODE][v] = true;
	    }
	    this._in[v] = {};
	    this._preds[v] = {};
	    this._out[v] = {};
	    this._sucs[v] = {};
	    ++this._nodeCount;
	    return this;
	  }

	  /**
	   * Gets the label of node with specified name.
	   * Complexity: O(|V|).
	   */
	  node(v) {
	    return this._nodes[v];
	  }

	  /**
	   * Detects whether graph has a node with specified name or not.
	   */
	  hasNode(v) {
	    return Object.hasOwn(this._nodes, v);
	  }

	  /**
	   * Remove the node with the name from the graph or do nothing if the node is not in
	   * the graph. If the node was removed this function also removes any incident
	   * edges.
	   * Complexity: O(1).
	   */
	  removeNode(v) {
	    var self = this;
	    if (Object.hasOwn(this._nodes, v)) {
	      var removeEdge = e => self.removeEdge(self._edgeObjs[e]);
	      delete this._nodes[v];
	      if (this._isCompound) {
	        this._removeFromParentsChildList(v);
	        delete this._parent[v];
	        this.children(v).forEach(function(child) {
	          self.setParent(child);
	        });
	        delete this._children[v];
	      }
	      Object.keys(this._in[v]).forEach(removeEdge);
	      delete this._in[v];
	      delete this._preds[v];
	      Object.keys(this._out[v]).forEach(removeEdge);
	      delete this._out[v];
	      delete this._sucs[v];
	      --this._nodeCount;
	    }
	    return this;
	  }

	  /**
	   * Sets node p as a parent for node v if it is defined, or removes the
	   * parent for v if p is undefined. Method throws an exception in case of
	   * invoking it in context of noncompound graph.
	   * Average-case complexity: O(1).
	   */
	  setParent(v, parent) {
	    if (!this._isCompound) {
	      throw new Error("Cannot set parent in a non-compound graph");
	    }

	    if (parent === undefined) {
	      parent = GRAPH_NODE;
	    } else {
	      // Coerce parent to string
	      parent += "";
	      for (var ancestor = parent; ancestor !== undefined; ancestor = this.parent(ancestor)) {
	        if (ancestor === v) {
	          throw new Error("Setting " + parent+ " as parent of " + v +
	              " would create a cycle");
	        }
	      }

	      this.setNode(parent);
	    }

	    this.setNode(v);
	    this._removeFromParentsChildList(v);
	    this._parent[v] = parent;
	    this._children[parent][v] = true;
	    return this;
	  }

	  _removeFromParentsChildList(v) {
	    delete this._children[this._parent[v]][v];
	  }

	  /**
	   * Gets parent node for node v.
	   * Complexity: O(1).
	   */
	  parent(v) {
	    if (this._isCompound) {
	      var parent = this._parent[v];
	      if (parent !== GRAPH_NODE) {
	        return parent;
	      }
	    }
	  }

	  /**
	   * Gets list of direct children of node v.
	   * Complexity: O(1).
	   */
	  children(v = GRAPH_NODE) {
	    if (this._isCompound) {
	      var children = this._children[v];
	      if (children) {
	        return Object.keys(children);
	      }
	    } else if (v === GRAPH_NODE) {
	      return this.nodes();
	    } else if (this.hasNode(v)) {
	      return [];
	    }
	  }

	  /**
	   * Return all nodes that are predecessors of the specified node or undefined if node v is not in
	   * the graph. Behavior is undefined for undirected graphs - use neighbors instead.
	   * Complexity: O(|V|).
	   */
	  predecessors(v) {
	    var predsV = this._preds[v];
	    if (predsV) {
	      return Object.keys(predsV);
	    }
	  }

	  /**
	   * Return all nodes that are successors of the specified node or undefined if node v is not in
	   * the graph. Behavior is undefined for undirected graphs - use neighbors instead.
	   * Complexity: O(|V|).
	   */
	  successors(v) {
	    var sucsV = this._sucs[v];
	    if (sucsV) {
	      return Object.keys(sucsV);
	    }
	  }

	  /**
	   * Return all nodes that are predecessors or successors of the specified node or undefined if
	   * node v is not in the graph.
	   * Complexity: O(|V|).
	   */
	  neighbors(v) {
	    var preds = this.predecessors(v);
	    if (preds) {
	      const union = new Set(preds);
	      for (var succ of this.successors(v)) {
	        union.add(succ);
	      }

	      return Array.from(union.values());
	    }
	  }

	  isLeaf(v) {
	    var neighbors;
	    if (this.isDirected()) {
	      neighbors = this.successors(v);
	    } else {
	      neighbors = this.neighbors(v);
	    }
	    return neighbors.length === 0;
	  }

	  /**
	   * Creates new graph with nodes filtered via filter. Edges incident to rejected node
	   * are also removed. In case of compound graph, if parent is rejected by filter,
	   * than all its children are rejected too.
	   * Average-case complexity: O(|E|+|V|).
	   */
	  filterNodes(filter) {
	    var copy = new this.constructor({
	      directed: this._isDirected,
	      multigraph: this._isMultigraph,
	      compound: this._isCompound
	    });

	    copy.setGraph(this.graph());

	    var self = this;
	    Object.entries(this._nodes).forEach(function([v, value]) {
	      if (filter(v)) {
	        copy.setNode(v, value);
	      }
	    });

	    Object.values(this._edgeObjs).forEach(function(e) {
	      if (copy.hasNode(e.v) && copy.hasNode(e.w)) {
	        copy.setEdge(e, self.edge(e));
	      }
	    });

	    var parents = {};
	    function findParent(v) {
	      var parent = self.parent(v);
	      if (parent === undefined || copy.hasNode(parent)) {
	        parents[v] = parent;
	        return parent;
	      } else if (parent in parents) {
	        return parents[parent];
	      } else {
	        return findParent(parent);
	      }
	    }

	    if (this._isCompound) {
	      copy.nodes().forEach(v => copy.setParent(v, findParent(v)));
	    }

	    return copy;
	  }

	  /* === Edge functions ========== */

	  /**
	   * Sets the default edge label or factory function. This label will be
	   * assigned as default label in case if no label was specified while setting
	   * an edge or this function will be invoked each time when setting an edge
	   * with no label specified and returned value * will be used as a label for edge.
	   * Complexity: O(1).
	   */
	  setDefaultEdgeLabel(newDefault) {
	    this._defaultEdgeLabelFn = newDefault;
	    if (typeof newDefault !== 'function') {
	      this._defaultEdgeLabelFn = () => newDefault;
	    }

	    return this;
	  }

	  /**
	   * Gets the number of edges in the graph.
	   * Complexity: O(1).
	   */
	  edgeCount() {
	    return this._edgeCount;
	  }

	  /**
	   * Gets edges of the graph. In case of compound graph subgraphs are not considered.
	   * Complexity: O(|E|).
	   */
	  edges() {
	    return Object.values(this._edgeObjs);
	  }

	  /**
	   * Establish an edges path over the nodes in nodes list. If some edge is already
	   * exists, it will update its label, otherwise it will create an edge between pair
	   * of nodes with label provided or default label if no label provided.
	   * Complexity: O(|nodes|).
	   */
	  setPath(vs, value) {
	    var self = this;
	    var args = arguments;
	    vs.reduce(function(v, w) {
	      if (args.length > 1) {
	        self.setEdge(v, w, value);
	      } else {
	        self.setEdge(v, w);
	      }
	      return w;
	    });
	    return this;
	  }

	  /**
	   * Creates or updates the label for the edge (v, w) with the optionally supplied
	   * name. If label is supplied it is set as the value for the edge. If label is not
	   * supplied and the edge was created by this call then the default edge label will
	   * be assigned. The name parameter is only useful with multigraphs.
	   */
	  setEdge() {
	    var v, w, name, value;
	    var valueSpecified = false;
	    var arg0 = arguments[0];

	    if (typeof arg0 === "object" && arg0 !== null && "v" in arg0) {
	      v = arg0.v;
	      w = arg0.w;
	      name = arg0.name;
	      if (arguments.length === 2) {
	        value = arguments[1];
	        valueSpecified = true;
	      }
	    } else {
	      v = arg0;
	      w = arguments[1];
	      name = arguments[3];
	      if (arguments.length > 2) {
	        value = arguments[2];
	        valueSpecified = true;
	      }
	    }

	    v = "" + v;
	    w = "" + w;
	    if (name !== undefined) {
	      name = "" + name;
	    }

	    var e = edgeArgsToId(this._isDirected, v, w, name);
	    if (Object.hasOwn(this._edgeLabels, e)) {
	      if (valueSpecified) {
	        this._edgeLabels[e] = value;
	      }
	      return this;
	    }

	    if (name !== undefined && !this._isMultigraph) {
	      throw new Error("Cannot set a named edge when isMultigraph = false");
	    }

	    // It didn't exist, so we need to create it.
	    // First ensure the nodes exist.
	    this.setNode(v);
	    this.setNode(w);

	    this._edgeLabels[e] = valueSpecified ? value : this._defaultEdgeLabelFn(v, w, name);

	    var edgeObj = edgeArgsToObj(this._isDirected, v, w, name);
	    // Ensure we add undirected edges in a consistent way.
	    v = edgeObj.v;
	    w = edgeObj.w;

	    Object.freeze(edgeObj);
	    this._edgeObjs[e] = edgeObj;
	    incrementOrInitEntry(this._preds[w], v);
	    incrementOrInitEntry(this._sucs[v], w);
	    this._in[w][e] = edgeObj;
	    this._out[v][e] = edgeObj;
	    this._edgeCount++;
	    return this;
	  }

	  /**
	   * Gets the label for the specified edge.
	   * Complexity: O(1).
	   */
	  edge(v, w, name) {
	    var e = (arguments.length === 1
	      ? edgeObjToId(this._isDirected, arguments[0])
	      : edgeArgsToId(this._isDirected, v, w, name));
	    return this._edgeLabels[e];
	  }

	  /**
	   * Gets the label for the specified edge and converts it to an object.
	   * Complexity: O(1)
	   */
	  edgeAsObj() {
	    const edge = this.edge(...arguments);
	    if (typeof edge !== "object") {
	      return {label: edge};
	    }

	    return edge;
	  }

	  /**
	   * Detects whether the graph contains specified edge or not. No subgraphs are considered.
	   * Complexity: O(1).
	   */
	  hasEdge(v, w, name) {
	    var e = (arguments.length === 1
	      ? edgeObjToId(this._isDirected, arguments[0])
	      : edgeArgsToId(this._isDirected, v, w, name));
	    return Object.hasOwn(this._edgeLabels, e);
	  }

	  /**
	   * Removes the specified edge from the graph. No subgraphs are considered.
	   * Complexity: O(1).
	   */
	  removeEdge(v, w, name) {
	    var e = (arguments.length === 1
	      ? edgeObjToId(this._isDirected, arguments[0])
	      : edgeArgsToId(this._isDirected, v, w, name));
	    var edge = this._edgeObjs[e];
	    if (edge) {
	      v = edge.v;
	      w = edge.w;
	      delete this._edgeLabels[e];
	      delete this._edgeObjs[e];
	      decrementOrRemoveEntry(this._preds[w], v);
	      decrementOrRemoveEntry(this._sucs[v], w);
	      delete this._in[w][e];
	      delete this._out[v][e];
	      this._edgeCount--;
	    }
	    return this;
	  }

	  /**
	   * Return all edges that point to the node v. Optionally filters those edges down to just those
	   * coming from node u. Behavior is undefined for undirected graphs - use nodeEdges instead.
	   * Complexity: O(|E|).
	   */
	  inEdges(v, u) {
	    var inV = this._in[v];
	    if (inV) {
	      var edges = Object.values(inV);
	      if (!u) {
	        return edges;
	      }
	      return edges.filter(edge => edge.v === u);
	    }
	  }

	  /**
	   * Return all edges that are pointed at by node v. Optionally filters those edges down to just
	   * those point to w. Behavior is undefined for undirected graphs - use nodeEdges instead.
	   * Complexity: O(|E|).
	   */
	  outEdges(v, w) {
	    var outV = this._out[v];
	    if (outV) {
	      var edges = Object.values(outV);
	      if (!w) {
	        return edges;
	      }
	      return edges.filter(edge => edge.w === w);
	    }
	  }

	  /**
	   * Returns all edges to or from node v regardless of direction. Optionally filters those edges
	   * down to just those between nodes v and w regardless of direction.
	   * Complexity: O(|E|).
	   */
	  nodeEdges(v, w) {
	    var inEdges = this.inEdges(v, w);
	    if (inEdges) {
	      return inEdges.concat(this.outEdges(v, w));
	    }
	  }
	}

	function incrementOrInitEntry(map, k) {
	  if (map[k]) {
	    map[k]++;
	  } else {
	    map[k] = 1;
	  }
	}

	function decrementOrRemoveEntry(map, k) {
	  if (!--map[k]) { delete map[k]; }
	}

	function edgeArgsToId(isDirected, v_, w_, name) {
	  var v = "" + v_;
	  var w = "" + w_;
	  if (!isDirected && v > w) {
	    var tmp = v;
	    v = w;
	    w = tmp;
	  }
	  return v + EDGE_KEY_DELIM + w + EDGE_KEY_DELIM +
	             (name === undefined ? DEFAULT_EDGE_NAME : name);
	}

	function edgeArgsToObj(isDirected, v_, w_, name) {
	  var v = "" + v_;
	  var w = "" + w_;
	  if (!isDirected && v > w) {
	    var tmp = v;
	    v = w;
	    w = tmp;
	  }
	  var edgeObj =  { v: v, w: w };
	  if (name) {
	    edgeObj.name = name;
	  }
	  return edgeObj;
	}

	function edgeObjToId(isDirected, edgeObj) {
	  return edgeArgsToId(isDirected, edgeObj.v, edgeObj.w, edgeObj.name);
	}

	graph = Graph;
	return graph;
}

var version$1;
var hasRequiredVersion$1;

function requireVersion$1 () {
	if (hasRequiredVersion$1) return version$1;
	hasRequiredVersion$1 = 1;
	version$1 = '2.2.4';
	return version$1;
}

var lib;
var hasRequiredLib;

function requireLib () {
	if (hasRequiredLib) return lib;
	hasRequiredLib = 1;
	// Includes only the "core" of graphlib
	lib = {
	  Graph: requireGraph(),
	  version: requireVersion$1()
	};
	return lib;
}

var json;
var hasRequiredJson;

function requireJson () {
	if (hasRequiredJson) return json;
	hasRequiredJson = 1;
	var Graph = requireGraph();

	json = {
	  write: write,
	  read: read
	};

	/**
	 * Creates a JSON representation of the graph that can be serialized to a string with
	 * JSON.stringify. The graph can later be restored using json.read.
	 */
	function write(g) {
	  var json = {
	    options: {
	      directed: g.isDirected(),
	      multigraph: g.isMultigraph(),
	      compound: g.isCompound()
	    },
	    nodes: writeNodes(g),
	    edges: writeEdges(g)
	  };

	  if (g.graph() !== undefined) {
	    json.value = structuredClone(g.graph());
	  }
	  return json;
	}

	function writeNodes(g) {
	  return g.nodes().map(function(v) {
	    var nodeValue = g.node(v);
	    var parent = g.parent(v);
	    var node = { v: v };
	    if (nodeValue !== undefined) {
	      node.value = nodeValue;
	    }
	    if (parent !== undefined) {
	      node.parent = parent;
	    }
	    return node;
	  });
	}

	function writeEdges(g) {
	  return g.edges().map(function(e) {
	    var edgeValue = g.edge(e);
	    var edge = { v: e.v, w: e.w };
	    if (e.name !== undefined) {
	      edge.name = e.name;
	    }
	    if (edgeValue !== undefined) {
	      edge.value = edgeValue;
	    }
	    return edge;
	  });
	}

	/**
	 * Takes JSON as input and returns the graph representation.
	 *
	 * @example
	 * var g2 = graphlib.json.read(JSON.parse(str));
	 * g2.nodes();
	 * // ['a', 'b']
	 * g2.edges()
	 * // [ { v: 'a', w: 'b' } ]
	 */
	function read(json) {
	  var g = new Graph(json.options).setGraph(json.value);
	  json.nodes.forEach(function(entry) {
	    g.setNode(entry.v, entry.value);
	    if (entry.parent) {
	      g.setParent(entry.v, entry.parent);
	    }
	  });
	  json.edges.forEach(function(entry) {
	    g.setEdge({ v: entry.v, w: entry.w, name: entry.name }, entry.value);
	  });
	  return g;
	}
	return json;
}

var components_1;
var hasRequiredComponents;

function requireComponents () {
	if (hasRequiredComponents) return components_1;
	hasRequiredComponents = 1;
	components_1 = components;

	function components(g) {
	  var visited = {};
	  var cmpts = [];
	  var cmpt;

	  function dfs(v) {
	    if (Object.hasOwn(visited, v)) return;
	    visited[v] = true;
	    cmpt.push(v);
	    g.successors(v).forEach(dfs);
	    g.predecessors(v).forEach(dfs);
	  }

	  g.nodes().forEach(function(v) {
	    cmpt = [];
	    dfs(v);
	    if (cmpt.length) {
	      cmpts.push(cmpt);
	    }
	  });

	  return cmpts;
	}
	return components_1;
}

/**
 * A min-priority queue data structure. This algorithm is derived from Cormen,
 * et al., "Introduction to Algorithms". The basic idea of a min-priority
 * queue is that you can efficiently (in O(1) time) get the smallest key in
 * the queue. Adding and removing elements takes O(log n) time. A key can
 * have its priority decreased in O(log n) time.
 */

var priorityQueue;
var hasRequiredPriorityQueue;

function requirePriorityQueue () {
	if (hasRequiredPriorityQueue) return priorityQueue;
	hasRequiredPriorityQueue = 1;
	class PriorityQueue {
	  _arr = [];
	  _keyIndices = {};

	  /**
	   * Returns the number of elements in the queue. Takes `O(1)` time.
	   */
	  size() {
	    return this._arr.length;
	  }

	  /**
	   * Returns the keys that are in the queue. Takes `O(n)` time.
	   */
	  keys() {
	    return this._arr.map(function(x) { return x.key; });
	  }

	  /**
	   * Returns `true` if **key** is in the queue and `false` if not.
	   */
	  has(key) {
	    return Object.hasOwn(this._keyIndices, key);
	  }

	  /**
	   * Returns the priority for **key**. If **key** is not present in the queue
	   * then this function returns `undefined`. Takes `O(1)` time.
	   *
	   * @param {Object} key
	   */
	  priority(key) {
	    var index = this._keyIndices[key];
	    if (index !== undefined) {
	      return this._arr[index].priority;
	    }
	  }

	  /**
	   * Returns the key for the minimum element in this queue. If the queue is
	   * empty this function throws an Error. Takes `O(1)` time.
	   */
	  min() {
	    if (this.size() === 0) {
	      throw new Error("Queue underflow");
	    }
	    return this._arr[0].key;
	  }

	  /**
	   * Inserts a new key into the priority queue. If the key already exists in
	   * the queue this function returns `false`; otherwise it will return `true`.
	   * Takes `O(n)` time.
	   *
	   * @param {Object} key the key to add
	   * @param {Number} priority the initial priority for the key
	   */
	  add(key, priority) {
	    var keyIndices = this._keyIndices;
	    key = String(key);
	    if (!Object.hasOwn(keyIndices, key)) {
	      var arr = this._arr;
	      var index = arr.length;
	      keyIndices[key] = index;
	      arr.push({key: key, priority: priority});
	      this._decrease(index);
	      return true;
	    }
	    return false;
	  }

	  /**
	   * Removes and returns the smallest key in the queue. Takes `O(log n)` time.
	   */
	  removeMin() {
	    this._swap(0, this._arr.length - 1);
	    var min = this._arr.pop();
	    delete this._keyIndices[min.key];
	    this._heapify(0);
	    return min.key;
	  }

	  /**
	   * Decreases the priority for **key** to **priority**. If the new priority is
	   * greater than the previous priority, this function will throw an Error.
	   *
	   * @param {Object} key the key for which to raise priority
	   * @param {Number} priority the new priority for the key
	   */
	  decrease(key, priority) {
	    var index = this._keyIndices[key];
	    if (priority > this._arr[index].priority) {
	      throw new Error("New priority is greater than current priority. " +
	          "Key: " + key + " Old: " + this._arr[index].priority + " New: " + priority);
	    }
	    this._arr[index].priority = priority;
	    this._decrease(index);
	  }

	  _heapify(i) {
	    var arr = this._arr;
	    var l = 2 * i;
	    var r = l + 1;
	    var largest = i;
	    if (l < arr.length) {
	      largest = arr[l].priority < arr[largest].priority ? l : largest;
	      if (r < arr.length) {
	        largest = arr[r].priority < arr[largest].priority ? r : largest;
	      }
	      if (largest !== i) {
	        this._swap(i, largest);
	        this._heapify(largest);
	      }
	    }
	  }

	  _decrease(index) {
	    var arr = this._arr;
	    var priority = arr[index].priority;
	    var parent;
	    while (index !== 0) {
	      parent = index >> 1;
	      if (arr[parent].priority < priority) {
	        break;
	      }
	      this._swap(index, parent);
	      index = parent;
	    }
	  }

	  _swap(i, j) {
	    var arr = this._arr;
	    var keyIndices = this._keyIndices;
	    var origArrI = arr[i];
	    var origArrJ = arr[j];
	    arr[i] = origArrJ;
	    arr[j] = origArrI;
	    keyIndices[origArrJ.key] = i;
	    keyIndices[origArrI.key] = j;
	  }
	}

	priorityQueue = PriorityQueue;
	return priorityQueue;
}

var dijkstra_1;
var hasRequiredDijkstra;

function requireDijkstra () {
	if (hasRequiredDijkstra) return dijkstra_1;
	hasRequiredDijkstra = 1;
	var PriorityQueue = requirePriorityQueue();

	dijkstra_1 = dijkstra;

	var DEFAULT_WEIGHT_FUNC = () => 1;

	function dijkstra(g, source, weightFn, edgeFn) {
	  return runDijkstra(g, String(source),
	    weightFn || DEFAULT_WEIGHT_FUNC,
	    edgeFn || function(v) { return g.outEdges(v); });
	}

	function runDijkstra(g, source, weightFn, edgeFn) {
	  var results = {};
	  var pq = new PriorityQueue();
	  var v, vEntry;

	  var updateNeighbors = function(edge) {
	    var w = edge.v !== v ? edge.v : edge.w;
	    var wEntry = results[w];
	    var weight = weightFn(edge);
	    var distance = vEntry.distance + weight;

	    if (weight < 0) {
	      throw new Error("dijkstra does not allow negative edge weights. " +
	                      "Bad edge: " + edge + " Weight: " + weight);
	    }

	    if (distance < wEntry.distance) {
	      wEntry.distance = distance;
	      wEntry.predecessor = v;
	      pq.decrease(w, distance);
	    }
	  };

	  g.nodes().forEach(function(v) {
	    var distance = v === source ? 0 : Number.POSITIVE_INFINITY;
	    results[v] = { distance: distance };
	    pq.add(v, distance);
	  });

	  while (pq.size() > 0) {
	    v = pq.removeMin();
	    vEntry = results[v];
	    if (vEntry.distance === Number.POSITIVE_INFINITY) {
	      break;
	    }

	    edgeFn(v).forEach(updateNeighbors);
	  }

	  return results;
	}
	return dijkstra_1;
}

var dijkstraAll_1;
var hasRequiredDijkstraAll;

function requireDijkstraAll () {
	if (hasRequiredDijkstraAll) return dijkstraAll_1;
	hasRequiredDijkstraAll = 1;
	var dijkstra = requireDijkstra();

	dijkstraAll_1 = dijkstraAll;

	function dijkstraAll(g, weightFunc, edgeFunc) {
	  return g.nodes().reduce(function(acc, v) {
	    acc[v] = dijkstra(g, v, weightFunc, edgeFunc);
	    return acc;
	  }, {});
	}
	return dijkstraAll_1;
}

var tarjan_1;
var hasRequiredTarjan;

function requireTarjan () {
	if (hasRequiredTarjan) return tarjan_1;
	hasRequiredTarjan = 1;
	tarjan_1 = tarjan;

	function tarjan(g) {
	  var index = 0;
	  var stack = [];
	  var visited = {}; // node id -> { onStack, lowlink, index }
	  var results = [];

	  function dfs(v) {
	    var entry = visited[v] = {
	      onStack: true,
	      lowlink: index,
	      index: index++
	    };
	    stack.push(v);

	    g.successors(v).forEach(function(w) {
	      if (!Object.hasOwn(visited, w)) {
	        dfs(w);
	        entry.lowlink = Math.min(entry.lowlink, visited[w].lowlink);
	      } else if (visited[w].onStack) {
	        entry.lowlink = Math.min(entry.lowlink, visited[w].index);
	      }
	    });

	    if (entry.lowlink === entry.index) {
	      var cmpt = [];
	      var w;
	      do {
	        w = stack.pop();
	        visited[w].onStack = false;
	        cmpt.push(w);
	      } while (v !== w);
	      results.push(cmpt);
	    }
	  }

	  g.nodes().forEach(function(v) {
	    if (!Object.hasOwn(visited, v)) {
	      dfs(v);
	    }
	  });

	  return results;
	}
	return tarjan_1;
}

var findCycles_1;
var hasRequiredFindCycles;

function requireFindCycles () {
	if (hasRequiredFindCycles) return findCycles_1;
	hasRequiredFindCycles = 1;
	var tarjan = requireTarjan();

	findCycles_1 = findCycles;

	function findCycles(g) {
	  return tarjan(g).filter(function(cmpt) {
	    return cmpt.length > 1 || (cmpt.length === 1 && g.hasEdge(cmpt[0], cmpt[0]));
	  });
	}
	return findCycles_1;
}

var floydWarshall_1;
var hasRequiredFloydWarshall;

function requireFloydWarshall () {
	if (hasRequiredFloydWarshall) return floydWarshall_1;
	hasRequiredFloydWarshall = 1;
	floydWarshall_1 = floydWarshall;

	var DEFAULT_WEIGHT_FUNC = () => 1;

	function floydWarshall(g, weightFn, edgeFn) {
	  return runFloydWarshall(g,
	    weightFn || DEFAULT_WEIGHT_FUNC,
	    edgeFn || function(v) { return g.outEdges(v); });
	}

	function runFloydWarshall(g, weightFn, edgeFn) {
	  var results = {};
	  var nodes = g.nodes();

	  nodes.forEach(function(v) {
	    results[v] = {};
	    results[v][v] = { distance: 0 };
	    nodes.forEach(function(w) {
	      if (v !== w) {
	        results[v][w] = { distance: Number.POSITIVE_INFINITY };
	      }
	    });
	    edgeFn(v).forEach(function(edge) {
	      var w = edge.v === v ? edge.w : edge.v;
	      var d = weightFn(edge);
	      results[v][w] = { distance: d, predecessor: v };
	    });
	  });

	  nodes.forEach(function(k) {
	    var rowK = results[k];
	    nodes.forEach(function(i) {
	      var rowI = results[i];
	      nodes.forEach(function(j) {
	        var ik = rowI[k];
	        var kj = rowK[j];
	        var ij = rowI[j];
	        var altDistance = ik.distance + kj.distance;
	        if (altDistance < ij.distance) {
	          ij.distance = altDistance;
	          ij.predecessor = kj.predecessor;
	        }
	      });
	    });
	  });

	  return results;
	}
	return floydWarshall_1;
}

var topsort_1;
var hasRequiredTopsort;

function requireTopsort () {
	if (hasRequiredTopsort) return topsort_1;
	hasRequiredTopsort = 1;
	function topsort(g) {
	  var visited = {};
	  var stack = {};
	  var results = [];

	  function visit(node) {
	    if (Object.hasOwn(stack, node)) {
	      throw new CycleException();
	    }

	    if (!Object.hasOwn(visited, node)) {
	      stack[node] = true;
	      visited[node] = true;
	      g.predecessors(node).forEach(visit);
	      delete stack[node];
	      results.push(node);
	    }
	  }

	  g.sinks().forEach(visit);

	  if (Object.keys(visited).length !== g.nodeCount()) {
	    throw new CycleException();
	  }

	  return results;
	}

	class CycleException extends Error {
	  constructor() {
	    super(...arguments);
	  }
	}

	topsort_1 = topsort;
	topsort.CycleException = CycleException;
	return topsort_1;
}

var isAcyclic_1;
var hasRequiredIsAcyclic;

function requireIsAcyclic () {
	if (hasRequiredIsAcyclic) return isAcyclic_1;
	hasRequiredIsAcyclic = 1;
	var topsort = requireTopsort();

	isAcyclic_1 = isAcyclic;

	function isAcyclic(g) {
	  try {
	    topsort(g);
	  } catch (e) {
	    if (e instanceof topsort.CycleException) {
	      return false;
	    }
	    throw e;
	  }
	  return true;
	}
	return isAcyclic_1;
}

var dfs_1;
var hasRequiredDfs;

function requireDfs () {
	if (hasRequiredDfs) return dfs_1;
	hasRequiredDfs = 1;
	dfs_1 = dfs;

	/*
	 * A helper that preforms a pre- or post-order traversal on the input graph
	 * and returns the nodes in the order they were visited. If the graph is
	 * undirected then this algorithm will navigate using neighbors. If the graph
	 * is directed then this algorithm will navigate using successors.
	 *
	 * If the order is not "post", it will be treated as "pre".
	 */
	function dfs(g, vs, order) {
	  if (!Array.isArray(vs)) {
	    vs = [vs];
	  }

	  var navigation = g.isDirected() ? v => g.successors(v) : v => g.neighbors(v);
	  var orderFunc = order === "post" ? postOrderDfs : preOrderDfs;

	  var acc = [];
	  var visited = {};
	  vs.forEach(v => {
	    if (!g.hasNode(v)) {
	      throw new Error("Graph does not have node: " + v);
	    }

	    orderFunc(v, navigation, visited, acc);
	  });

	  return acc;
	}

	function postOrderDfs(v, navigation, visited, acc) {
	  var stack = [[v, false]];
	  while (stack.length > 0) {
	    var curr = stack.pop();
	    if (curr[1]) {
	      acc.push(curr[0]);
	    } else {
	      if (!Object.hasOwn(visited, curr[0])) {
	        visited[curr[0]] = true;
	        stack.push([curr[0], true]);
	        forEachRight(navigation(curr[0]), w => stack.push([w, false]));
	      }
	    }
	  }
	}

	function preOrderDfs(v, navigation, visited, acc) {
	  var stack = [v];
	  while (stack.length > 0) {
	    var curr = stack.pop();
	    if (!Object.hasOwn(visited, curr)) {
	      visited[curr] = true;
	      acc.push(curr);
	      forEachRight(navigation(curr), w => stack.push(w));
	    }
	  }
	}

	function forEachRight(array, iteratee) {
	  var length = array.length;
	  while (length--) {
	    iteratee(array[length], length, array);
	  }

	  return array;
	}
	return dfs_1;
}

var postorder_1;
var hasRequiredPostorder;

function requirePostorder () {
	if (hasRequiredPostorder) return postorder_1;
	hasRequiredPostorder = 1;
	var dfs = requireDfs();

	postorder_1 = postorder;

	function postorder(g, vs) {
	  return dfs(g, vs, "post");
	}
	return postorder_1;
}

var preorder_1;
var hasRequiredPreorder;

function requirePreorder () {
	if (hasRequiredPreorder) return preorder_1;
	hasRequiredPreorder = 1;
	var dfs = requireDfs();

	preorder_1 = preorder;

	function preorder(g, vs) {
	  return dfs(g, vs, "pre");
	}
	return preorder_1;
}

var prim_1;
var hasRequiredPrim;

function requirePrim () {
	if (hasRequiredPrim) return prim_1;
	hasRequiredPrim = 1;
	var Graph = requireGraph();
	var PriorityQueue = requirePriorityQueue();

	prim_1 = prim;

	function prim(g, weightFunc) {
	  var result = new Graph();
	  var parents = {};
	  var pq = new PriorityQueue();
	  var v;

	  function updateNeighbors(edge) {
	    var w = edge.v === v ? edge.w : edge.v;
	    var pri = pq.priority(w);
	    if (pri !== undefined) {
	      var edgeWeight = weightFunc(edge);
	      if (edgeWeight < pri) {
	        parents[w] = v;
	        pq.decrease(w, edgeWeight);
	      }
	    }
	  }

	  if (g.nodeCount() === 0) {
	    return result;
	  }

	  g.nodes().forEach(function(v) {
	    pq.add(v, Number.POSITIVE_INFINITY);
	    result.setNode(v);
	  });

	  // Start from an arbitrary node
	  pq.decrease(g.nodes()[0], 0);

	  var init = false;
	  while (pq.size() > 0) {
	    v = pq.removeMin();
	    if (Object.hasOwn(parents, v)) {
	      result.setEdge(v, parents[v]);
	    } else if (init) {
	      throw new Error("Input graph is not connected: " + g);
	    } else {
	      init = true;
	    }

	    g.nodeEdges(v).forEach(updateNeighbors);
	  }

	  return result;
	}
	return prim_1;
}

var alg;
var hasRequiredAlg;

function requireAlg () {
	if (hasRequiredAlg) return alg;
	hasRequiredAlg = 1;
	alg = {
	  components: requireComponents(),
	  dijkstra: requireDijkstra(),
	  dijkstraAll: requireDijkstraAll(),
	  findCycles: requireFindCycles(),
	  floydWarshall: requireFloydWarshall(),
	  isAcyclic: requireIsAcyclic(),
	  postorder: requirePostorder(),
	  preorder: requirePreorder(),
	  prim: requirePrim(),
	  tarjan: requireTarjan(),
	  topsort: requireTopsort()
	};
	return alg;
}

/**
 * Copyright (c) 2014, Chris Pettitt
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 * list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 * this list of conditions and the following disclaimer in the documentation
 * and/or other materials provided with the distribution.
 *
 * 3. Neither the name of the copyright holder nor the names of its contributors
 * may be used to endorse or promote products derived from this software without
 * specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
 * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

var graphlib;
var hasRequiredGraphlib;

function requireGraphlib () {
	if (hasRequiredGraphlib) return graphlib;
	hasRequiredGraphlib = 1;
	var lib = requireLib();

	graphlib = {
	  Graph: lib.Graph,
	  json: requireJson(),
	  alg: requireAlg(),
	  version: lib.version
	};
	return graphlib;
}

/*
 * Simple doubly linked list implementation derived from Cormen, et al.,
 * "Introduction to Algorithms".
 */

var list;
var hasRequiredList;

function requireList () {
	if (hasRequiredList) return list;
	hasRequiredList = 1;
	class List {
	  constructor() {
	    let sentinel = {};
	    sentinel._next = sentinel._prev = sentinel;
	    this._sentinel = sentinel;
	  }

	  dequeue() {
	    let sentinel = this._sentinel;
	    let entry = sentinel._prev;
	    if (entry !== sentinel) {
	      unlink(entry);
	      return entry;
	    }
	  }

	  enqueue(entry) {
	    let sentinel = this._sentinel;
	    if (entry._prev && entry._next) {
	      unlink(entry);
	    }
	    entry._next = sentinel._next;
	    sentinel._next._prev = entry;
	    sentinel._next = entry;
	    entry._prev = sentinel;
	  }

	  toString() {
	    let strs = [];
	    let sentinel = this._sentinel;
	    let curr = sentinel._prev;
	    while (curr !== sentinel) {
	      strs.push(JSON.stringify(curr, filterOutLinks));
	      curr = curr._prev;
	    }
	    return "[" + strs.join(", ") + "]";
	  }
	}

	function unlink(entry) {
	  entry._prev._next = entry._next;
	  entry._next._prev = entry._prev;
	  delete entry._next;
	  delete entry._prev;
	}

	function filterOutLinks(k, v) {
	  if (k !== "_next" && k !== "_prev") {
	    return v;
	  }
	}

	list = List;
	return list;
}

var greedyFas;
var hasRequiredGreedyFas;

function requireGreedyFas () {
	if (hasRequiredGreedyFas) return greedyFas;
	hasRequiredGreedyFas = 1;
	let Graph = requireGraphlib().Graph;
	let List = requireList();

	/*
	 * A greedy heuristic for finding a feedback arc set for a graph. A feedback
	 * arc set is a set of edges that can be removed to make a graph acyclic.
	 * The algorithm comes from: P. Eades, X. Lin, and W. F. Smyth, "A fast and
	 * effective heuristic for the feedback arc set problem." This implementation
	 * adjusts that from the paper to allow for weighted edges.
	 */
	greedyFas = greedyFAS;

	let DEFAULT_WEIGHT_FN = () => 1;

	function greedyFAS(g, weightFn) {
	  if (g.nodeCount() <= 1) {
	    return [];
	  }
	  let state = buildState(g, weightFn || DEFAULT_WEIGHT_FN);
	  let results = doGreedyFAS(state.graph, state.buckets, state.zeroIdx);

	  // Expand multi-edges
	  return results.flatMap(e => g.outEdges(e.v, e.w));
	}

	function doGreedyFAS(g, buckets, zeroIdx) {
	  let results = [];
	  let sources = buckets[buckets.length - 1];
	  let sinks = buckets[0];

	  let entry;
	  while (g.nodeCount()) {
	    while ((entry = sinks.dequeue()))   { removeNode(g, buckets, zeroIdx, entry); }
	    while ((entry = sources.dequeue())) { removeNode(g, buckets, zeroIdx, entry); }
	    if (g.nodeCount()) {
	      for (let i = buckets.length - 2; i > 0; --i) {
	        entry = buckets[i].dequeue();
	        if (entry) {
	          results = results.concat(removeNode(g, buckets, zeroIdx, entry, true));
	          break;
	        }
	      }
	    }
	  }

	  return results;
	}

	function removeNode(g, buckets, zeroIdx, entry, collectPredecessors) {
	  let results = collectPredecessors ? [] : undefined;

	  g.inEdges(entry.v).forEach(edge => {
	    let weight = g.edge(edge);
	    let uEntry = g.node(edge.v);

	    if (collectPredecessors) {
	      results.push({ v: edge.v, w: edge.w });
	    }

	    uEntry.out -= weight;
	    assignBucket(buckets, zeroIdx, uEntry);
	  });

	  g.outEdges(entry.v).forEach(edge => {
	    let weight = g.edge(edge);
	    let w = edge.w;
	    let wEntry = g.node(w);
	    wEntry["in"] -= weight;
	    assignBucket(buckets, zeroIdx, wEntry);
	  });

	  g.removeNode(entry.v);

	  return results;
	}

	function buildState(g, weightFn) {
	  let fasGraph = new Graph();
	  let maxIn = 0;
	  let maxOut = 0;

	  g.nodes().forEach(v => {
	    fasGraph.setNode(v, { v: v, "in": 0, out: 0 });
	  });

	  // Aggregate weights on nodes, but also sum the weights across multi-edges
	  // into a single edge for the fasGraph.
	  g.edges().forEach(e => {
	    let prevWeight = fasGraph.edge(e.v, e.w) || 0;
	    let weight = weightFn(e);
	    let edgeWeight = prevWeight + weight;
	    fasGraph.setEdge(e.v, e.w, edgeWeight);
	    maxOut = Math.max(maxOut, fasGraph.node(e.v).out += weight);
	    maxIn  = Math.max(maxIn,  fasGraph.node(e.w)["in"]  += weight);
	  });

	  let buckets = range(maxOut + maxIn + 3).map(() => new List());
	  let zeroIdx = maxIn + 1;

	  fasGraph.nodes().forEach(v => {
	    assignBucket(buckets, zeroIdx, fasGraph.node(v));
	  });

	  return { graph: fasGraph, buckets: buckets, zeroIdx: zeroIdx };
	}

	function assignBucket(buckets, zeroIdx, entry) {
	  if (!entry.out) {
	    buckets[0].enqueue(entry);
	  } else if (!entry["in"]) {
	    buckets[buckets.length - 1].enqueue(entry);
	  } else {
	    buckets[entry.out - entry["in"] + zeroIdx].enqueue(entry);
	  }
	}

	function range(limit) {
	  const range = [];
	  for (let i = 0; i < limit; i++) {
	    range.push(i);
	  }

	  return range;
	}
	return greedyFas;
}

/* eslint "no-console": off */

var util$1;
var hasRequiredUtil$1;

function requireUtil$1 () {
	if (hasRequiredUtil$1) return util$1;
	hasRequiredUtil$1 = 1;

	let Graph = requireGraphlib().Graph;

	util$1 = {
	  addBorderNode,
	  addDummyNode,
	  applyWithChunking,
	  asNonCompoundGraph,
	  buildLayerMatrix,
	  intersectRect,
	  mapValues,
	  maxRank,
	  normalizeRanks,
	  notime,
	  partition,
	  pick,
	  predecessorWeights,
	  range,
	  removeEmptyRanks,
	  simplify,
	  successorWeights,
	  time,
	  uniqueId,
	  zipObject,
	};

	/*
	 * Adds a dummy node to the graph and return v.
	 */
	function addDummyNode(g, type, attrs, name) {
	  let v;
	  do {
	    v = uniqueId(name);
	  } while (g.hasNode(v));

	  attrs.dummy = type;
	  g.setNode(v, attrs);
	  return v;
	}

	/*
	 * Returns a new graph with only simple edges. Handles aggregation of data
	 * associated with multi-edges.
	 */
	function simplify(g) {
	  let simplified = new Graph().setGraph(g.graph());
	  g.nodes().forEach(v => simplified.setNode(v, g.node(v)));
	  g.edges().forEach(e => {
	    let simpleLabel = simplified.edge(e.v, e.w) || { weight: 0, minlen: 1 };
	    let label = g.edge(e);
	    simplified.setEdge(e.v, e.w, {
	      weight: simpleLabel.weight + label.weight,
	      minlen: Math.max(simpleLabel.minlen, label.minlen)
	    });
	  });
	  return simplified;
	}

	function asNonCompoundGraph(g) {
	  let simplified = new Graph({ multigraph: g.isMultigraph() }).setGraph(g.graph());
	  g.nodes().forEach(v => {
	    if (!g.children(v).length) {
	      simplified.setNode(v, g.node(v));
	    }
	  });
	  g.edges().forEach(e => {
	    simplified.setEdge(e, g.edge(e));
	  });
	  return simplified;
	}

	function successorWeights(g) {
	  let weightMap = g.nodes().map(v => {
	    let sucs = {};
	    g.outEdges(v).forEach(e => {
	      sucs[e.w] = (sucs[e.w] || 0) + g.edge(e).weight;
	    });
	    return sucs;
	  });
	  return zipObject(g.nodes(), weightMap);
	}

	function predecessorWeights(g) {
	  let weightMap = g.nodes().map(v => {
	    let preds = {};
	    g.inEdges(v).forEach(e => {
	      preds[e.v] = (preds[e.v] || 0) + g.edge(e).weight;
	    });
	    return preds;
	  });
	  return zipObject(g.nodes(), weightMap);
	}

	/*
	 * Finds where a line starting at point ({x, y}) would intersect a rectangle
	 * ({x, y, width, height}) if it were pointing at the rectangle's center.
	 */
	function intersectRect(rect, point) {
	  let x = rect.x;
	  let y = rect.y;

	  // Rectangle intersection algorithm from:
	  // http://math.stackexchange.com/questions/108113/find-edge-between-two-boxes
	  let dx = point.x - x;
	  let dy = point.y - y;
	  let w = rect.width / 2;
	  let h = rect.height / 2;

	  if (!dx && !dy) {
	    throw new Error("Not possible to find intersection inside of the rectangle");
	  }

	  let sx, sy;
	  if (Math.abs(dy) * w > Math.abs(dx) * h) {
	    // Intersection is top or bottom of rect.
	    if (dy < 0) {
	      h = -h;
	    }
	    sx = h * dx / dy;
	    sy = h;
	  } else {
	    // Intersection is left or right of rect.
	    if (dx < 0) {
	      w = -w;
	    }
	    sx = w;
	    sy = w * dy / dx;
	  }

	  return { x: x + sx, y: y + sy };
	}

	/*
	 * Given a DAG with each node assigned "rank" and "order" properties, this
	 * function will produce a matrix with the ids of each node.
	 */
	function buildLayerMatrix(g) {
	  let layering = range(maxRank(g) + 1).map(() => []);
	  g.nodes().forEach(v => {
	    let node = g.node(v);
	    let rank = node.rank;
	    if (rank !== undefined) {
	      layering[rank][node.order] = v;
	    }
	  });
	  return layering;
	}

	/*
	 * Adjusts the ranks for all nodes in the graph such that all nodes v have
	 * rank(v) >= 0 and at least one node w has rank(w) = 0.
	 */
	function normalizeRanks(g) {
	  let nodeRanks = g.nodes().map(v => {
	    let rank = g.node(v).rank;
	    if (rank === undefined) {
	      return Number.MAX_VALUE;
	    }

	    return rank;
	  });
	  let min = applyWithChunking(Math.min, nodeRanks);
	  g.nodes().forEach(v => {
	    let node = g.node(v);
	    if (Object.hasOwn(node, "rank")) {
	      node.rank -= min;
	    }
	  });
	}

	function removeEmptyRanks(g) {
	  // Ranks may not start at 0, so we need to offset them
	  let nodeRanks = g.nodes().map(v => g.node(v).rank);
	  let offset = applyWithChunking(Math.min, nodeRanks);

	  let layers = [];
	  g.nodes().forEach(v => {
	    let rank = g.node(v).rank - offset;
	    if (!layers[rank]) {
	      layers[rank] = [];
	    }
	    layers[rank].push(v);
	  });

	  let delta = 0;
	  let nodeRankFactor = g.graph().nodeRankFactor;
	  Array.from(layers).forEach((vs, i) => {
	    if (vs === undefined && i % nodeRankFactor !== 0) {
	      --delta;
	    } else if (vs !== undefined && delta) {
	      vs.forEach(v => g.node(v).rank += delta);
	    }
	  });
	}

	function addBorderNode(g, prefix, rank, order) {
	  let node = {
	    width: 0,
	    height: 0
	  };
	  if (arguments.length >= 4) {
	    node.rank = rank;
	    node.order = order;
	  }
	  return addDummyNode(g, "border", node, prefix);
	}

	function splitToChunks(array, chunkSize = CHUNKING_THRESHOLD) {
	  const chunks = [];
	  for (let i = 0; i < array.length; i += chunkSize) {
	    const chunk = array.slice(i, i + chunkSize);
	    chunks.push(chunk);
	  }
	  return chunks;
	}

	const CHUNKING_THRESHOLD = 65535;

	function applyWithChunking(fn, argsArray) {
	  if(argsArray.length > CHUNKING_THRESHOLD) {
	    const chunks = splitToChunks(argsArray);
	    return fn.apply(null, chunks.map(chunk => fn.apply(null, chunk)));
	  } else {
	    return fn.apply(null, argsArray);
	  }
	}

	function maxRank(g) {
	  const nodes = g.nodes();
	  const nodeRanks = nodes.map(v => {
	    let rank = g.node(v).rank;
	    if (rank === undefined) {
	      return Number.MIN_VALUE;
	    }
	    return rank;
	  });

	  return applyWithChunking(Math.max, nodeRanks);
	}

	/*
	 * Partition a collection into two groups: `lhs` and `rhs`. If the supplied
	 * function returns true for an entry it goes into `lhs`. Otherwise it goes
	 * into `rhs.
	 */
	function partition(collection, fn) {
	  let result = { lhs: [], rhs: [] };
	  collection.forEach(value => {
	    if (fn(value)) {
	      result.lhs.push(value);
	    } else {
	      result.rhs.push(value);
	    }
	  });
	  return result;
	}

	/*
	 * Returns a new function that wraps `fn` with a timer. The wrapper logs the
	 * time it takes to execute the function.
	 */
	function time(name, fn) {
	  let start = Date.now();
	  try {
	    return fn();
	  } finally {
	    console.log(name + " time: " + (Date.now() - start) + "ms");
	  }
	}

	function notime(name, fn) {
	  return fn();
	}

	let idCounter = 0;
	function uniqueId(prefix) {
	  var id = ++idCounter;
	  return toString(prefix) + id;
	}

	function range(start, limit, step = 1) {
	  if (limit == null) {
	    limit = start;
	    start = 0;
	  }

	  let endCon = (i) => i < limit;
	  if (step < 0) {
	    endCon = (i) => limit < i;
	  }

	  const range = [];
	  for (let i = start; endCon(i); i += step) {
	    range.push(i);
	  }

	  return range;
	}

	function pick(source, keys) {
	  const dest = {};
	  for (const key of keys) {
	    if (source[key] !== undefined) {
	      dest[key] = source[key];
	    }
	  }

	  return dest;
	}

	function mapValues(obj, funcOrProp) {
	  let func = funcOrProp;
	  if (typeof funcOrProp === 'string') {
	    func = (val) => val[funcOrProp];
	  }

	  return Object.entries(obj).reduce((acc, [k, v]) => {
	    acc[k] = func(v, k);
	    return acc;
	  }, {});
	}

	function zipObject(props, values) {
	  return props.reduce((acc, key, i) => {
	    acc[key] = values[i];
	    return acc;
	  }, {});
	}
	return util$1;
}

var acyclic;
var hasRequiredAcyclic;

function requireAcyclic () {
	if (hasRequiredAcyclic) return acyclic;
	hasRequiredAcyclic = 1;

	let greedyFAS = requireGreedyFas();
	let uniqueId = requireUtil$1().uniqueId;

	acyclic = {
	  run: run,
	  undo: undo
	};

	function run(g) {
	  let fas = (g.graph().acyclicer === "greedy"
	    ? greedyFAS(g, weightFn(g))
	    : dfsFAS(g));
	  fas.forEach(e => {
	    let label = g.edge(e);
	    g.removeEdge(e);
	    label.forwardName = e.name;
	    label.reversed = true;
	    g.setEdge(e.w, e.v, label, uniqueId("rev"));
	  });

	  function weightFn(g) {
	    return e => {
	      return g.edge(e).weight;
	    };
	  }
	}

	function dfsFAS(g) {
	  let fas = [];
	  let stack = {};
	  let visited = {};

	  function dfs(v) {
	    if (Object.hasOwn(visited, v)) {
	      return;
	    }
	    visited[v] = true;
	    stack[v] = true;
	    g.outEdges(v).forEach(e => {
	      if (Object.hasOwn(stack, e.w)) {
	        fas.push(e);
	      } else {
	        dfs(e.w);
	      }
	    });
	    delete stack[v];
	  }

	  g.nodes().forEach(dfs);
	  return fas;
	}

	function undo(g) {
	  g.edges().forEach(e => {
	    let label = g.edge(e);
	    if (label.reversed) {
	      g.removeEdge(e);

	      let forwardName = label.forwardName;
	      delete label.reversed;
	      delete label.forwardName;
	      g.setEdge(e.w, e.v, label, forwardName);
	    }
	  });
	}
	return acyclic;
}

var normalize;
var hasRequiredNormalize;

function requireNormalize () {
	if (hasRequiredNormalize) return normalize;
	hasRequiredNormalize = 1;

	let util = requireUtil$1();

	normalize = {
	  run: run,
	  undo: undo
	};

	/*
	 * Breaks any long edges in the graph into short segments that span 1 layer
	 * each. This operation is undoable with the denormalize function.
	 *
	 * Pre-conditions:
	 *
	 *    1. The input graph is a DAG.
	 *    2. Each node in the graph has a "rank" property.
	 *
	 * Post-condition:
	 *
	 *    1. All edges in the graph have a length of 1.
	 *    2. Dummy nodes are added where edges have been split into segments.
	 *    3. The graph is augmented with a "dummyChains" attribute which contains
	 *       the first dummy in each chain of dummy nodes produced.
	 */
	function run(g) {
	  g.graph().dummyChains = [];
	  g.edges().forEach(edge => normalizeEdge(g, edge));
	}

	function normalizeEdge(g, e) {
	  let v = e.v;
	  let vRank = g.node(v).rank;
	  let w = e.w;
	  let wRank = g.node(w).rank;
	  let name = e.name;
	  let edgeLabel = g.edge(e);
	  let labelRank = edgeLabel.labelRank;

	  if (wRank === vRank + 1) return;

	  g.removeEdge(e);

	  let dummy, attrs, i;
	  for (i = 0, ++vRank; vRank < wRank; ++i, ++vRank) {
	    edgeLabel.points = [];
	    attrs = {
	      width: 0, height: 0,
	      edgeLabel: edgeLabel, edgeObj: e,
	      rank: vRank
	    };
	    dummy = util.addDummyNode(g, "edge", attrs, "_d");
	    if (vRank === labelRank) {
	      attrs.width = edgeLabel.width;
	      attrs.height = edgeLabel.height;
	      attrs.dummy = "edge-label";
	      attrs.labelpos = edgeLabel.labelpos;
	    }
	    g.setEdge(v, dummy, { weight: edgeLabel.weight }, name);
	    if (i === 0) {
	      g.graph().dummyChains.push(dummy);
	    }
	    v = dummy;
	  }

	  g.setEdge(v, w, { weight: edgeLabel.weight }, name);
	}

	function undo(g) {
	  g.graph().dummyChains.forEach(v => {
	    let node = g.node(v);
	    let origLabel = node.edgeLabel;
	    let w;
	    g.setEdge(node.edgeObj, origLabel);
	    while (node.dummy) {
	      w = g.successors(v)[0];
	      g.removeNode(v);
	      origLabel.points.push({ x: node.x, y: node.y });
	      if (node.dummy === "edge-label") {
	        origLabel.x = node.x;
	        origLabel.y = node.y;
	        origLabel.width = node.width;
	        origLabel.height = node.height;
	      }
	      v = w;
	      node = g.node(v);
	    }
	  });
	}
	return normalize;
}

var util;
var hasRequiredUtil;

function requireUtil () {
	if (hasRequiredUtil) return util;
	hasRequiredUtil = 1;

	const { applyWithChunking } = requireUtil$1();

	util = {
	  longestPath: longestPath,
	  slack: slack
	};

	/*
	 * Initializes ranks for the input graph using the longest path algorithm. This
	 * algorithm scales well and is fast in practice, it yields rather poor
	 * solutions. Nodes are pushed to the lowest layer possible, leaving the bottom
	 * ranks wide and leaving edges longer than necessary. However, due to its
	 * speed, this algorithm is good for getting an initial ranking that can be fed
	 * into other algorithms.
	 *
	 * This algorithm does not normalize layers because it will be used by other
	 * algorithms in most cases. If using this algorithm directly, be sure to
	 * run normalize at the end.
	 *
	 * Pre-conditions:
	 *
	 *    1. Input graph is a DAG.
	 *    2. Input graph node labels can be assigned properties.
	 *
	 * Post-conditions:
	 *
	 *    1. Each node will be assign an (unnormalized) "rank" property.
	 */
	function longestPath(g) {
	  var visited = {};

	  function dfs(v) {
	    var label = g.node(v);
	    if (Object.hasOwn(visited, v)) {
	      return label.rank;
	    }
	    visited[v] = true;

	    let outEdgesMinLens = g.outEdges(v).map(e => {
	      if (e == null) {
	        return Number.POSITIVE_INFINITY;
	      }

	      return dfs(e.w) - g.edge(e).minlen;
	    });

	    var rank = applyWithChunking(Math.min, outEdgesMinLens);

	    if (rank === Number.POSITIVE_INFINITY) {
	      rank = 0;
	    }

	    return (label.rank = rank);
	  }

	  g.sources().forEach(dfs);
	}

	/*
	 * Returns the amount of slack for the given edge. The slack is defined as the
	 * difference between the length of the edge and its minimum length.
	 */
	function slack(g, e) {
	  return g.node(e.w).rank - g.node(e.v).rank - g.edge(e).minlen;
	}
	return util;
}

var feasibleTree_1;
var hasRequiredFeasibleTree;

function requireFeasibleTree () {
	if (hasRequiredFeasibleTree) return feasibleTree_1;
	hasRequiredFeasibleTree = 1;

	var Graph = requireGraphlib().Graph;
	var slack = requireUtil().slack;

	feasibleTree_1 = feasibleTree;

	/*
	 * Constructs a spanning tree with tight edges and adjusted the input node's
	 * ranks to achieve this. A tight edge is one that is has a length that matches
	 * its "minlen" attribute.
	 *
	 * The basic structure for this function is derived from Gansner, et al., "A
	 * Technique for Drawing Directed Graphs."
	 *
	 * Pre-conditions:
	 *
	 *    1. Graph must be a DAG.
	 *    2. Graph must be connected.
	 *    3. Graph must have at least one node.
	 *    5. Graph nodes must have been previously assigned a "rank" property that
	 *       respects the "minlen" property of incident edges.
	 *    6. Graph edges must have a "minlen" property.
	 *
	 * Post-conditions:
	 *
	 *    - Graph nodes will have their rank adjusted to ensure that all edges are
	 *      tight.
	 *
	 * Returns a tree (undirected graph) that is constructed using only "tight"
	 * edges.
	 */
	function feasibleTree(g) {
	  var t = new Graph({ directed: false });

	  // Choose arbitrary node from which to start our tree
	  var start = g.nodes()[0];
	  var size = g.nodeCount();
	  t.setNode(start, {});

	  var edge, delta;
	  while (tightTree(t, g) < size) {
	    edge = findMinSlackEdge(t, g);
	    delta = t.hasNode(edge.v) ? slack(g, edge) : -slack(g, edge);
	    shiftRanks(t, g, delta);
	  }

	  return t;
	}

	/*
	 * Finds a maximal tree of tight edges and returns the number of nodes in the
	 * tree.
	 */
	function tightTree(t, g) {
	  function dfs(v) {
	    g.nodeEdges(v).forEach(e => {
	      var edgeV = e.v,
	        w = (v === edgeV) ? e.w : edgeV;
	      if (!t.hasNode(w) && !slack(g, e)) {
	        t.setNode(w, {});
	        t.setEdge(v, w, {});
	        dfs(w);
	      }
	    });
	  }

	  t.nodes().forEach(dfs);
	  return t.nodeCount();
	}

	/*
	 * Finds the edge with the smallest slack that is incident on tree and returns
	 * it.
	 */
	function findMinSlackEdge(t, g) {
	  const edges = g.edges();

	  return edges.reduce((acc, edge) => {
	    let edgeSlack = Number.POSITIVE_INFINITY;
	    if (t.hasNode(edge.v) !== t.hasNode(edge.w)) {
	      edgeSlack = slack(g, edge);
	    }

	    if (edgeSlack < acc[0]) {
	      return [edgeSlack, edge];
	    }

	    return acc;
	  }, [Number.POSITIVE_INFINITY, null])[1];
	}

	function shiftRanks(t, g, delta) {
	  t.nodes().forEach(v => g.node(v).rank += delta);
	}
	return feasibleTree_1;
}

var networkSimplex_1;
var hasRequiredNetworkSimplex;

function requireNetworkSimplex () {
	if (hasRequiredNetworkSimplex) return networkSimplex_1;
	hasRequiredNetworkSimplex = 1;

	var feasibleTree = requireFeasibleTree();
	var slack = requireUtil().slack;
	var initRank = requireUtil().longestPath;
	var preorder = requireGraphlib().alg.preorder;
	var postorder = requireGraphlib().alg.postorder;
	var simplify = requireUtil$1().simplify;

	networkSimplex_1 = networkSimplex;

	// Expose some internals for testing purposes
	networkSimplex.initLowLimValues = initLowLimValues;
	networkSimplex.initCutValues = initCutValues;
	networkSimplex.calcCutValue = calcCutValue;
	networkSimplex.leaveEdge = leaveEdge;
	networkSimplex.enterEdge = enterEdge;
	networkSimplex.exchangeEdges = exchangeEdges;

	/*
	 * The network simplex algorithm assigns ranks to each node in the input graph
	 * and iteratively improves the ranking to reduce the length of edges.
	 *
	 * Preconditions:
	 *
	 *    1. The input graph must be a DAG.
	 *    2. All nodes in the graph must have an object value.
	 *    3. All edges in the graph must have "minlen" and "weight" attributes.
	 *
	 * Postconditions:
	 *
	 *    1. All nodes in the graph will have an assigned "rank" attribute that has
	 *       been optimized by the network simplex algorithm. Ranks start at 0.
	 *
	 *
	 * A rough sketch of the algorithm is as follows:
	 *
	 *    1. Assign initial ranks to each node. We use the longest path algorithm,
	 *       which assigns ranks to the lowest position possible. In general this
	 *       leads to very wide bottom ranks and unnecessarily long edges.
	 *    2. Construct a feasible tight tree. A tight tree is one such that all
	 *       edges in the tree have no slack (difference between length of edge
	 *       and minlen for the edge). This by itself greatly improves the assigned
	 *       rankings by shorting edges.
	 *    3. Iteratively find edges that have negative cut values. Generally a
	 *       negative cut value indicates that the edge could be removed and a new
	 *       tree edge could be added to produce a more compact graph.
	 *
	 * Much of the algorithms here are derived from Gansner, et al., "A Technique
	 * for Drawing Directed Graphs." The structure of the file roughly follows the
	 * structure of the overall algorithm.
	 */
	function networkSimplex(g) {
	  g = simplify(g);
	  initRank(g);
	  var t = feasibleTree(g);
	  initLowLimValues(t);
	  initCutValues(t, g);

	  var e, f;
	  while ((e = leaveEdge(t))) {
	    f = enterEdge(t, g, e);
	    exchangeEdges(t, g, e, f);
	  }
	}

	/*
	 * Initializes cut values for all edges in the tree.
	 */
	function initCutValues(t, g) {
	  var vs = postorder(t, t.nodes());
	  vs = vs.slice(0, vs.length - 1);
	  vs.forEach(v => assignCutValue(t, g, v));
	}

	function assignCutValue(t, g, child) {
	  var childLab = t.node(child);
	  var parent = childLab.parent;
	  t.edge(child, parent).cutvalue = calcCutValue(t, g, child);
	}

	/*
	 * Given the tight tree, its graph, and a child in the graph calculate and
	 * return the cut value for the edge between the child and its parent.
	 */
	function calcCutValue(t, g, child) {
	  var childLab = t.node(child);
	  var parent = childLab.parent;
	  // True if the child is on the tail end of the edge in the directed graph
	  var childIsTail = true;
	  // The graph's view of the tree edge we're inspecting
	  var graphEdge = g.edge(child, parent);
	  // The accumulated cut value for the edge between this node and its parent
	  var cutValue = 0;

	  if (!graphEdge) {
	    childIsTail = false;
	    graphEdge = g.edge(parent, child);
	  }

	  cutValue = graphEdge.weight;

	  g.nodeEdges(child).forEach(e => {
	    var isOutEdge = e.v === child,
	      other = isOutEdge ? e.w : e.v;

	    if (other !== parent) {
	      var pointsToHead = isOutEdge === childIsTail,
	        otherWeight = g.edge(e).weight;

	      cutValue += pointsToHead ? otherWeight : -otherWeight;
	      if (isTreeEdge(t, child, other)) {
	        var otherCutValue = t.edge(child, other).cutvalue;
	        cutValue += pointsToHead ? -otherCutValue : otherCutValue;
	      }
	    }
	  });

	  return cutValue;
	}

	function initLowLimValues(tree, root) {
	  if (arguments.length < 2) {
	    root = tree.nodes()[0];
	  }
	  dfsAssignLowLim(tree, {}, 1, root);
	}

	function dfsAssignLowLim(tree, visited, nextLim, v, parent) {
	  var low = nextLim;
	  var label = tree.node(v);

	  visited[v] = true;
	  tree.neighbors(v).forEach(w => {
	    if (!Object.hasOwn(visited, w)) {
	      nextLim = dfsAssignLowLim(tree, visited, nextLim, w, v);
	    }
	  });

	  label.low = low;
	  label.lim = nextLim++;
	  if (parent) {
	    label.parent = parent;
	  } else {
	    // TODO should be able to remove this when we incrementally update low lim
	    delete label.parent;
	  }

	  return nextLim;
	}

	function leaveEdge(tree) {
	  return tree.edges().find(e => tree.edge(e).cutvalue < 0);
	}

	function enterEdge(t, g, edge) {
	  var v = edge.v;
	  var w = edge.w;

	  // For the rest of this function we assume that v is the tail and w is the
	  // head, so if we don't have this edge in the graph we should flip it to
	  // match the correct orientation.
	  if (!g.hasEdge(v, w)) {
	    v = edge.w;
	    w = edge.v;
	  }

	  var vLabel = t.node(v);
	  var wLabel = t.node(w);
	  var tailLabel = vLabel;
	  var flip = false;

	  // If the root is in the tail of the edge then we need to flip the logic that
	  // checks for the head and tail nodes in the candidates function below.
	  if (vLabel.lim > wLabel.lim) {
	    tailLabel = wLabel;
	    flip = true;
	  }

	  var candidates = g.edges().filter(edge => {
	    return flip === isDescendant(t, t.node(edge.v), tailLabel) &&
	           flip !== isDescendant(t, t.node(edge.w), tailLabel);
	  });

	  return candidates.reduce((acc, edge) => {
	    if (slack(g, edge) < slack(g, acc)) {
	      return edge;
	    }

	    return acc;
	  });
	}

	function exchangeEdges(t, g, e, f) {
	  var v = e.v;
	  var w = e.w;
	  t.removeEdge(v, w);
	  t.setEdge(f.v, f.w, {});
	  initLowLimValues(t);
	  initCutValues(t, g);
	  updateRanks(t, g);
	}

	function updateRanks(t, g) {
	  var root = t.nodes().find(v => !g.node(v).parent);
	  var vs = preorder(t, root);
	  vs = vs.slice(1);
	  vs.forEach(v => {
	    var parent = t.node(v).parent,
	      edge = g.edge(v, parent),
	      flipped = false;

	    if (!edge) {
	      edge = g.edge(parent, v);
	      flipped = true;
	    }

	    g.node(v).rank = g.node(parent).rank + (flipped ? edge.minlen : -edge.minlen);
	  });
	}

	/*
	 * Returns true if the edge is in the tree.
	 */
	function isTreeEdge(tree, u, v) {
	  return tree.hasEdge(u, v);
	}

	/*
	 * Returns true if the specified node is descendant of the root node per the
	 * assigned low and lim attributes in the tree.
	 */
	function isDescendant(tree, vLabel, rootLabel) {
	  return rootLabel.low <= vLabel.lim && vLabel.lim <= rootLabel.lim;
	}
	return networkSimplex_1;
}

var rank_1;
var hasRequiredRank;

function requireRank () {
	if (hasRequiredRank) return rank_1;
	hasRequiredRank = 1;

	var rankUtil = requireUtil();
	var longestPath = rankUtil.longestPath;
	var feasibleTree = requireFeasibleTree();
	var networkSimplex = requireNetworkSimplex();

	rank_1 = rank;

	/*
	 * Assigns a rank to each node in the input graph that respects the "minlen"
	 * constraint specified on edges between nodes.
	 *
	 * This basic structure is derived from Gansner, et al., "A Technique for
	 * Drawing Directed Graphs."
	 *
	 * Pre-conditions:
	 *
	 *    1. Graph must be a connected DAG
	 *    2. Graph nodes must be objects
	 *    3. Graph edges must have "weight" and "minlen" attributes
	 *
	 * Post-conditions:
	 *
	 *    1. Graph nodes will have a "rank" attribute based on the results of the
	 *       algorithm. Ranks can start at any index (including negative), we'll
	 *       fix them up later.
	 */
	function rank(g) {
	  switch(g.graph().ranker) {
	  case "network-simplex": networkSimplexRanker(g); break;
	  case "tight-tree": tightTreeRanker(g); break;
	  case "longest-path": longestPathRanker(g); break;
	  default: networkSimplexRanker(g);
	  }
	}

	// A fast and simple ranker, but results are far from optimal.
	var longestPathRanker = longestPath;

	function tightTreeRanker(g) {
	  longestPath(g);
	  feasibleTree(g);
	}

	function networkSimplexRanker(g) {
	  networkSimplex(g);
	}
	return rank_1;
}

var parentDummyChains_1;
var hasRequiredParentDummyChains;

function requireParentDummyChains () {
	if (hasRequiredParentDummyChains) return parentDummyChains_1;
	hasRequiredParentDummyChains = 1;
	parentDummyChains_1 = parentDummyChains;

	function parentDummyChains(g) {
	  let postorderNums = postorder(g);

	  g.graph().dummyChains.forEach(v => {
	    let node = g.node(v);
	    let edgeObj = node.edgeObj;
	    let pathData = findPath(g, postorderNums, edgeObj.v, edgeObj.w);
	    let path = pathData.path;
	    let lca = pathData.lca;
	    let pathIdx = 0;
	    let pathV = path[pathIdx];
	    let ascending = true;

	    while (v !== edgeObj.w) {
	      node = g.node(v);

	      if (ascending) {
	        while ((pathV = path[pathIdx]) !== lca &&
	               g.node(pathV).maxRank < node.rank) {
	          pathIdx++;
	        }

	        if (pathV === lca) {
	          ascending = false;
	        }
	      }

	      if (!ascending) {
	        while (pathIdx < path.length - 1 &&
	               g.node(pathV = path[pathIdx + 1]).minRank <= node.rank) {
	          pathIdx++;
	        }
	        pathV = path[pathIdx];
	      }

	      g.setParent(v, pathV);
	      v = g.successors(v)[0];
	    }
	  });
	}

	// Find a path from v to w through the lowest common ancestor (LCA). Return the
	// full path and the LCA.
	function findPath(g, postorderNums, v, w) {
	  let vPath = [];
	  let wPath = [];
	  let low = Math.min(postorderNums[v].low, postorderNums[w].low);
	  let lim = Math.max(postorderNums[v].lim, postorderNums[w].lim);
	  let parent;
	  let lca;

	  // Traverse up from v to find the LCA
	  parent = v;
	  do {
	    parent = g.parent(parent);
	    vPath.push(parent);
	  } while (parent &&
	           (postorderNums[parent].low > low || lim > postorderNums[parent].lim));
	  lca = parent;

	  // Traverse from w to LCA
	  parent = w;
	  while ((parent = g.parent(parent)) !== lca) {
	    wPath.push(parent);
	  }

	  return { path: vPath.concat(wPath.reverse()), lca: lca };
	}

	function postorder(g) {
	  let result = {};
	  let lim = 0;

	  function dfs(v) {
	    let low = lim;
	    g.children(v).forEach(dfs);
	    result[v] = { low: low, lim: lim++ };
	  }
	  g.children().forEach(dfs);

	  return result;
	}
	return parentDummyChains_1;
}

var nestingGraph;
var hasRequiredNestingGraph;

function requireNestingGraph () {
	if (hasRequiredNestingGraph) return nestingGraph;
	hasRequiredNestingGraph = 1;
	let util = requireUtil$1();

	nestingGraph = {
	  run,
	  cleanup,
	};

	/*
	 * A nesting graph creates dummy nodes for the tops and bottoms of subgraphs,
	 * adds appropriate edges to ensure that all cluster nodes are placed between
	 * these boundaries, and ensures that the graph is connected.
	 *
	 * In addition we ensure, through the use of the minlen property, that nodes
	 * and subgraph border nodes to not end up on the same rank.
	 *
	 * Preconditions:
	 *
	 *    1. Input graph is a DAG
	 *    2. Nodes in the input graph has a minlen attribute
	 *
	 * Postconditions:
	 *
	 *    1. Input graph is connected.
	 *    2. Dummy nodes are added for the tops and bottoms of subgraphs.
	 *    3. The minlen attribute for nodes is adjusted to ensure nodes do not
	 *       get placed on the same rank as subgraph border nodes.
	 *
	 * The nesting graph idea comes from Sander, "Layout of Compound Directed
	 * Graphs."
	 */
	function run(g) {
	  let root = util.addDummyNode(g, "root", {}, "_root");
	  let depths = treeDepths(g);
	  let depthsArr = Object.values(depths);
	  let height = util.applyWithChunking(Math.max, depthsArr) - 1; // Note: depths is an Object not an array
	  let nodeSep = 2 * height + 1;

	  g.graph().nestingRoot = root;

	  // Multiply minlen by nodeSep to align nodes on non-border ranks.
	  g.edges().forEach(e => g.edge(e).minlen *= nodeSep);

	  // Calculate a weight that is sufficient to keep subgraphs vertically compact
	  let weight = sumWeights(g) + 1;

	  // Create border nodes and link them up
	  g.children().forEach(child => dfs(g, root, nodeSep, weight, height, depths, child));

	  // Save the multiplier for node layers for later removal of empty border
	  // layers.
	  g.graph().nodeRankFactor = nodeSep;
	}

	function dfs(g, root, nodeSep, weight, height, depths, v) {
	  let children = g.children(v);
	  if (!children.length) {
	    if (v !== root) {
	      g.setEdge(root, v, { weight: 0, minlen: nodeSep });
	    }
	    return;
	  }

	  let top = util.addBorderNode(g, "_bt");
	  let bottom = util.addBorderNode(g, "_bb");
	  let label = g.node(v);

	  g.setParent(top, v);
	  label.borderTop = top;
	  g.setParent(bottom, v);
	  label.borderBottom = bottom;

	  children.forEach(child => {
	    dfs(g, root, nodeSep, weight, height, depths, child);

	    let childNode = g.node(child);
	    let childTop = childNode.borderTop ? childNode.borderTop : child;
	    let childBottom = childNode.borderBottom ? childNode.borderBottom : child;
	    let thisWeight = childNode.borderTop ? weight : 2 * weight;
	    let minlen = childTop !== childBottom ? 1 : height - depths[v] + 1;

	    g.setEdge(top, childTop, {
	      weight: thisWeight,
	      minlen: minlen,
	      nestingEdge: true
	    });

	    g.setEdge(childBottom, bottom, {
	      weight: thisWeight,
	      minlen: minlen,
	      nestingEdge: true
	    });
	  });

	  if (!g.parent(v)) {
	    g.setEdge(root, top, { weight: 0, minlen: height + depths[v] });
	  }
	}

	function treeDepths(g) {
	  var depths = {};
	  function dfs(v, depth) {
	    var children = g.children(v);
	    if (children && children.length) {
	      children.forEach(child => dfs(child, depth + 1));
	    }
	    depths[v] = depth;
	  }
	  g.children().forEach(v => dfs(v, 1));
	  return depths;
	}

	function sumWeights(g) {
	  return g.edges().reduce((acc, e) => acc + g.edge(e).weight, 0);
	}

	function cleanup(g) {
	  var graphLabel = g.graph();
	  g.removeNode(graphLabel.nestingRoot);
	  delete graphLabel.nestingRoot;
	  g.edges().forEach(e => {
	    var edge = g.edge(e);
	    if (edge.nestingEdge) {
	      g.removeEdge(e);
	    }
	  });
	}
	return nestingGraph;
}

var addBorderSegments_1;
var hasRequiredAddBorderSegments;

function requireAddBorderSegments () {
	if (hasRequiredAddBorderSegments) return addBorderSegments_1;
	hasRequiredAddBorderSegments = 1;
	let util = requireUtil$1();

	addBorderSegments_1 = addBorderSegments;

	function addBorderSegments(g) {
	  function dfs(v) {
	    let children = g.children(v);
	    let node = g.node(v);
	    if (children.length) {
	      children.forEach(dfs);
	    }

	    if (Object.hasOwn(node, "minRank")) {
	      node.borderLeft = [];
	      node.borderRight = [];
	      for (let rank = node.minRank, maxRank = node.maxRank + 1;
	        rank < maxRank;
	        ++rank) {
	        addBorderNode(g, "borderLeft", "_bl", v, node, rank);
	        addBorderNode(g, "borderRight", "_br", v, node, rank);
	      }
	    }
	  }

	  g.children().forEach(dfs);
	}

	function addBorderNode(g, prop, prefix, sg, sgNode, rank) {
	  let label = { width: 0, height: 0, rank: rank, borderType: prop };
	  let prev = sgNode[prop][rank - 1];
	  let curr = util.addDummyNode(g, "border", label, prefix);
	  sgNode[prop][rank] = curr;
	  g.setParent(curr, sg);
	  if (prev) {
	    g.setEdge(prev, curr, { weight: 1 });
	  }
	}
	return addBorderSegments_1;
}

var coordinateSystem;
var hasRequiredCoordinateSystem;

function requireCoordinateSystem () {
	if (hasRequiredCoordinateSystem) return coordinateSystem;
	hasRequiredCoordinateSystem = 1;

	coordinateSystem = {
	  adjust: adjust,
	  undo: undo
	};

	function adjust(g) {
	  let rankDir = g.graph().rankdir.toLowerCase();
	  if (rankDir === "lr" || rankDir === "rl") {
	    swapWidthHeight(g);
	  }
	}

	function undo(g) {
	  let rankDir = g.graph().rankdir.toLowerCase();
	  if (rankDir === "bt" || rankDir === "rl") {
	    reverseY(g);
	  }

	  if (rankDir === "lr" || rankDir === "rl") {
	    swapXY(g);
	    swapWidthHeight(g);
	  }
	}

	function swapWidthHeight(g) {
	  g.nodes().forEach(v => swapWidthHeightOne(g.node(v)));
	  g.edges().forEach(e => swapWidthHeightOne(g.edge(e)));
	}

	function swapWidthHeightOne(attrs) {
	  let w = attrs.width;
	  attrs.width = attrs.height;
	  attrs.height = w;
	}

	function reverseY(g) {
	  g.nodes().forEach(v => reverseYOne(g.node(v)));

	  g.edges().forEach(e => {
	    let edge = g.edge(e);
	    edge.points.forEach(reverseYOne);
	    if (Object.hasOwn(edge, "y")) {
	      reverseYOne(edge);
	    }
	  });
	}

	function reverseYOne(attrs) {
	  attrs.y = -attrs.y;
	}

	function swapXY(g) {
	  g.nodes().forEach(v => swapXYOne(g.node(v)));

	  g.edges().forEach(e => {
	    let edge = g.edge(e);
	    edge.points.forEach(swapXYOne);
	    if (Object.hasOwn(edge, "x")) {
	      swapXYOne(edge);
	    }
	  });
	}

	function swapXYOne(attrs) {
	  let x = attrs.x;
	  attrs.x = attrs.y;
	  attrs.y = x;
	}
	return coordinateSystem;
}

var initOrder_1;
var hasRequiredInitOrder;

function requireInitOrder () {
	if (hasRequiredInitOrder) return initOrder_1;
	hasRequiredInitOrder = 1;

	let util = requireUtil$1();

	initOrder_1 = initOrder;

	/*
	 * Assigns an initial order value for each node by performing a DFS search
	 * starting from nodes in the first rank. Nodes are assigned an order in their
	 * rank as they are first visited.
	 *
	 * This approach comes from Gansner, et al., "A Technique for Drawing Directed
	 * Graphs."
	 *
	 * Returns a layering matrix with an array per layer and each layer sorted by
	 * the order of its nodes.
	 */
	function initOrder(g) {
	  let visited = {};
	  let simpleNodes = g.nodes().filter(v => !g.children(v).length);
	  let simpleNodesRanks = simpleNodes.map(v => g.node(v).rank);
	  let maxRank = util.applyWithChunking(Math.max, simpleNodesRanks);
	  let layers = util.range(maxRank + 1).map(() => []);

	  function dfs(v) {
	    if (visited[v]) return;
	    visited[v] = true;
	    let node = g.node(v);
	    layers[node.rank].push(v);
	    g.successors(v).forEach(dfs);
	  }

	  let orderedVs = simpleNodes.sort((a, b) => g.node(a).rank - g.node(b).rank);
	  orderedVs.forEach(dfs);

	  return layers;
	}
	return initOrder_1;
}

var crossCount_1;
var hasRequiredCrossCount;

function requireCrossCount () {
	if (hasRequiredCrossCount) return crossCount_1;
	hasRequiredCrossCount = 1;

	let zipObject = requireUtil$1().zipObject;

	crossCount_1 = crossCount;

	/*
	 * A function that takes a layering (an array of layers, each with an array of
	 * ordererd nodes) and a graph and returns a weighted crossing count.
	 *
	 * Pre-conditions:
	 *
	 *    1. Input graph must be simple (not a multigraph), directed, and include
	 *       only simple edges.
	 *    2. Edges in the input graph must have assigned weights.
	 *
	 * Post-conditions:
	 *
	 *    1. The graph and layering matrix are left unchanged.
	 *
	 * This algorithm is derived from Barth, et al., "Bilayer Cross Counting."
	 */
	function crossCount(g, layering) {
	  let cc = 0;
	  for (let i = 1; i < layering.length; ++i) {
	    cc += twoLayerCrossCount(g, layering[i-1], layering[i]);
	  }
	  return cc;
	}

	function twoLayerCrossCount(g, northLayer, southLayer) {
	  // Sort all of the edges between the north and south layers by their position
	  // in the north layer and then the south. Map these edges to the position of
	  // their head in the south layer.
	  let southPos = zipObject(southLayer, southLayer.map((v, i) => i));
	  let southEntries = northLayer.flatMap(v => {
	    return g.outEdges(v).map(e => {
	      return { pos: southPos[e.w], weight: g.edge(e).weight };
	    }).sort((a, b) => a.pos - b.pos);
	  });

	  // Build the accumulator tree
	  let firstIndex = 1;
	  while (firstIndex < southLayer.length) firstIndex <<= 1;
	  let treeSize = 2 * firstIndex - 1;
	  firstIndex -= 1;
	  let tree = new Array(treeSize).fill(0);

	  // Calculate the weighted crossings
	  let cc = 0;
	  southEntries.forEach(entry => {
	    let index = entry.pos + firstIndex;
	    tree[index] += entry.weight;
	    let weightSum = 0;
	    while (index > 0) {
	      if (index % 2) {
	        weightSum += tree[index + 1];
	      }
	      index = (index - 1) >> 1;
	      tree[index] += entry.weight;
	    }
	    cc += entry.weight * weightSum;
	  });

	  return cc;
	}
	return crossCount_1;
}

var barycenter_1;
var hasRequiredBarycenter;

function requireBarycenter () {
	if (hasRequiredBarycenter) return barycenter_1;
	hasRequiredBarycenter = 1;
	barycenter_1 = barycenter;

	function barycenter(g, movable = []) {
	  return movable.map(v => {
	    let inV = g.inEdges(v);
	    if (!inV.length) {
	      return { v: v };
	    } else {
	      let result = inV.reduce((acc, e) => {
	        let edge = g.edge(e),
	          nodeU = g.node(e.v);
	        return {
	          sum: acc.sum + (edge.weight * nodeU.order),
	          weight: acc.weight + edge.weight
	        };
	      }, { sum: 0, weight: 0 });

	      return {
	        v: v,
	        barycenter: result.sum / result.weight,
	        weight: result.weight
	      };
	    }
	  });
	}
	return barycenter_1;
}

var resolveConflicts_1;
var hasRequiredResolveConflicts;

function requireResolveConflicts () {
	if (hasRequiredResolveConflicts) return resolveConflicts_1;
	hasRequiredResolveConflicts = 1;

	let util = requireUtil$1();

	resolveConflicts_1 = resolveConflicts;

	/*
	 * Given a list of entries of the form {v, barycenter, weight} and a
	 * constraint graph this function will resolve any conflicts between the
	 * constraint graph and the barycenters for the entries. If the barycenters for
	 * an entry would violate a constraint in the constraint graph then we coalesce
	 * the nodes in the conflict into a new node that respects the contraint and
	 * aggregates barycenter and weight information.
	 *
	 * This implementation is based on the description in Forster, "A Fast and
	 * Simple Hueristic for Constrained Two-Level Crossing Reduction," thought it
	 * differs in some specific details.
	 *
	 * Pre-conditions:
	 *
	 *    1. Each entry has the form {v, barycenter, weight}, or if the node has
	 *       no barycenter, then {v}.
	 *
	 * Returns:
	 *
	 *    A new list of entries of the form {vs, i, barycenter, weight}. The list
	 *    `vs` may either be a singleton or it may be an aggregation of nodes
	 *    ordered such that they do not violate constraints from the constraint
	 *    graph. The property `i` is the lowest original index of any of the
	 *    elements in `vs`.
	 */
	function resolveConflicts(entries, cg) {
	  let mappedEntries = {};
	  entries.forEach((entry, i) => {
	    let tmp = mappedEntries[entry.v] = {
	      indegree: 0,
	      "in": [],
	      out: [],
	      vs: [entry.v],
	      i: i
	    };
	    if (entry.barycenter !== undefined) {
	      tmp.barycenter = entry.barycenter;
	      tmp.weight = entry.weight;
	    }
	  });

	  cg.edges().forEach(e => {
	    let entryV = mappedEntries[e.v];
	    let entryW = mappedEntries[e.w];
	    if (entryV !== undefined && entryW !== undefined) {
	      entryW.indegree++;
	      entryV.out.push(mappedEntries[e.w]);
	    }
	  });

	  let sourceSet = Object.values(mappedEntries).filter(entry => !entry.indegree);

	  return doResolveConflicts(sourceSet);
	}

	function doResolveConflicts(sourceSet) {
	  let entries = [];

	  function handleIn(vEntry) {
	    return uEntry => {
	      if (uEntry.merged) {
	        return;
	      }
	      if (uEntry.barycenter === undefined ||
	          vEntry.barycenter === undefined ||
	          uEntry.barycenter >= vEntry.barycenter) {
	        mergeEntries(vEntry, uEntry);
	      }
	    };
	  }

	  function handleOut(vEntry) {
	    return wEntry => {
	      wEntry["in"].push(vEntry);
	      if (--wEntry.indegree === 0) {
	        sourceSet.push(wEntry);
	      }
	    };
	  }

	  while (sourceSet.length) {
	    let entry = sourceSet.pop();
	    entries.push(entry);
	    entry["in"].reverse().forEach(handleIn(entry));
	    entry.out.forEach(handleOut(entry));
	  }

	  return entries.filter(entry => !entry.merged).map(entry => {
	    return util.pick(entry, ["vs", "i", "barycenter", "weight"]);
	  });
	}

	function mergeEntries(target, source) {
	  let sum = 0;
	  let weight = 0;

	  if (target.weight) {
	    sum += target.barycenter * target.weight;
	    weight += target.weight;
	  }

	  if (source.weight) {
	    sum += source.barycenter * source.weight;
	    weight += source.weight;
	  }

	  target.vs = source.vs.concat(target.vs);
	  target.barycenter = sum / weight;
	  target.weight = weight;
	  target.i = Math.min(source.i, target.i);
	  source.merged = true;
	}
	return resolveConflicts_1;
}

var sort_1;
var hasRequiredSort;

function requireSort () {
	if (hasRequiredSort) return sort_1;
	hasRequiredSort = 1;
	let util = requireUtil$1();

	sort_1 = sort;

	function sort(entries, biasRight) {
	  let parts = util.partition(entries, entry => {
	    return Object.hasOwn(entry, "barycenter");
	  });
	  let sortable = parts.lhs,
	    unsortable = parts.rhs.sort((a, b) => b.i - a.i),
	    vs = [],
	    sum = 0,
	    weight = 0,
	    vsIndex = 0;

	  sortable.sort(compareWithBias(!!biasRight));

	  vsIndex = consumeUnsortable(vs, unsortable, vsIndex);

	  sortable.forEach(entry => {
	    vsIndex += entry.vs.length;
	    vs.push(entry.vs);
	    sum += entry.barycenter * entry.weight;
	    weight += entry.weight;
	    vsIndex = consumeUnsortable(vs, unsortable, vsIndex);
	  });

	  let result = { vs: vs.flat(true) };
	  if (weight) {
	    result.barycenter = sum / weight;
	    result.weight = weight;
	  }
	  return result;
	}

	function consumeUnsortable(vs, unsortable, index) {
	  let last;
	  while (unsortable.length && (last = unsortable[unsortable.length - 1]).i <= index) {
	    unsortable.pop();
	    vs.push(last.vs);
	    index++;
	  }
	  return index;
	}

	function compareWithBias(bias) {
	  return (entryV, entryW) => {
	    if (entryV.barycenter < entryW.barycenter) {
	      return -1;
	    } else if (entryV.barycenter > entryW.barycenter) {
	      return 1;
	    }

	    return !bias ? entryV.i - entryW.i : entryW.i - entryV.i;
	  };
	}
	return sort_1;
}

var sortSubgraph_1;
var hasRequiredSortSubgraph;

function requireSortSubgraph () {
	if (hasRequiredSortSubgraph) return sortSubgraph_1;
	hasRequiredSortSubgraph = 1;
	let barycenter = requireBarycenter();
	let resolveConflicts = requireResolveConflicts();
	let sort = requireSort();

	sortSubgraph_1 = sortSubgraph;

	function sortSubgraph(g, v, cg, biasRight) {
	  let movable = g.children(v);
	  let node = g.node(v);
	  let bl = node ? node.borderLeft : undefined;
	  let br = node ? node.borderRight: undefined;
	  let subgraphs = {};

	  if (bl) {
	    movable = movable.filter(w => w !== bl && w !== br);
	  }

	  let barycenters = barycenter(g, movable);
	  barycenters.forEach(entry => {
	    if (g.children(entry.v).length) {
	      let subgraphResult = sortSubgraph(g, entry.v, cg, biasRight);
	      subgraphs[entry.v] = subgraphResult;
	      if (Object.hasOwn(subgraphResult, "barycenter")) {
	        mergeBarycenters(entry, subgraphResult);
	      }
	    }
	  });

	  let entries = resolveConflicts(barycenters, cg);
	  expandSubgraphs(entries, subgraphs);

	  let result = sort(entries, biasRight);

	  if (bl) {
	    result.vs = [bl, result.vs, br].flat(true);
	    if (g.predecessors(bl).length) {
	      let blPred = g.node(g.predecessors(bl)[0]),
	        brPred = g.node(g.predecessors(br)[0]);
	      if (!Object.hasOwn(result, "barycenter")) {
	        result.barycenter = 0;
	        result.weight = 0;
	      }
	      result.barycenter = (result.barycenter * result.weight +
	                           blPred.order + brPred.order) / (result.weight + 2);
	      result.weight += 2;
	    }
	  }

	  return result;
	}

	function expandSubgraphs(entries, subgraphs) {
	  entries.forEach(entry => {
	    entry.vs = entry.vs.flatMap(v => {
	      if (subgraphs[v]) {
	        return subgraphs[v].vs;
	      }
	      return v;
	    });
	  });
	}

	function mergeBarycenters(target, other) {
	  if (target.barycenter !== undefined) {
	    target.barycenter = (target.barycenter * target.weight +
	                         other.barycenter * other.weight) /
	                        (target.weight + other.weight);
	    target.weight += other.weight;
	  } else {
	    target.barycenter = other.barycenter;
	    target.weight = other.weight;
	  }
	}
	return sortSubgraph_1;
}

var buildLayerGraph_1;
var hasRequiredBuildLayerGraph;

function requireBuildLayerGraph () {
	if (hasRequiredBuildLayerGraph) return buildLayerGraph_1;
	hasRequiredBuildLayerGraph = 1;
	let Graph = requireGraphlib().Graph;
	let util = requireUtil$1();

	buildLayerGraph_1 = buildLayerGraph;

	/*
	 * Constructs a graph that can be used to sort a layer of nodes. The graph will
	 * contain all base and subgraph nodes from the request layer in their original
	 * hierarchy and any edges that are incident on these nodes and are of the type
	 * requested by the "relationship" parameter.
	 *
	 * Nodes from the requested rank that do not have parents are assigned a root
	 * node in the output graph, which is set in the root graph attribute. This
	 * makes it easy to walk the hierarchy of movable nodes during ordering.
	 *
	 * Pre-conditions:
	 *
	 *    1. Input graph is a DAG
	 *    2. Base nodes in the input graph have a rank attribute
	 *    3. Subgraph nodes in the input graph has minRank and maxRank attributes
	 *    4. Edges have an assigned weight
	 *
	 * Post-conditions:
	 *
	 *    1. Output graph has all nodes in the movable rank with preserved
	 *       hierarchy.
	 *    2. Root nodes in the movable layer are made children of the node
	 *       indicated by the root attribute of the graph.
	 *    3. Non-movable nodes incident on movable nodes, selected by the
	 *       relationship parameter, are included in the graph (without hierarchy).
	 *    4. Edges incident on movable nodes, selected by the relationship
	 *       parameter, are added to the output graph.
	 *    5. The weights for copied edges are aggregated as need, since the output
	 *       graph is not a multi-graph.
	 */
	function buildLayerGraph(g, rank, relationship) {
	  let root = createRootNode(g),
	    result = new Graph({ compound: true }).setGraph({ root: root })
	      .setDefaultNodeLabel(v => g.node(v));

	  g.nodes().forEach(v => {
	    let node = g.node(v),
	      parent = g.parent(v);

	    if (node.rank === rank || node.minRank <= rank && rank <= node.maxRank) {
	      result.setNode(v);
	      result.setParent(v, parent || root);

	      // This assumes we have only short edges!
	      g[relationship](v).forEach(e => {
	        let u = e.v === v ? e.w : e.v,
	          edge = result.edge(u, v),
	          weight = edge !== undefined ? edge.weight : 0;
	        result.setEdge(u, v, { weight: g.edge(e).weight + weight });
	      });

	      if (Object.hasOwn(node, "minRank")) {
	        result.setNode(v, {
	          borderLeft: node.borderLeft[rank],
	          borderRight: node.borderRight[rank]
	        });
	      }
	    }
	  });

	  return result;
	}

	function createRootNode(g) {
	  var v;
	  while (g.hasNode((v = util.uniqueId("_root"))));
	  return v;
	}
	return buildLayerGraph_1;
}

var addSubgraphConstraints_1;
var hasRequiredAddSubgraphConstraints;

function requireAddSubgraphConstraints () {
	if (hasRequiredAddSubgraphConstraints) return addSubgraphConstraints_1;
	hasRequiredAddSubgraphConstraints = 1;
	addSubgraphConstraints_1 = addSubgraphConstraints;

	function addSubgraphConstraints(g, cg, vs) {
	  let prev = {},
	    rootPrev;

	  vs.forEach(v => {
	    let child = g.parent(v),
	      parent,
	      prevChild;
	    while (child) {
	      parent = g.parent(child);
	      if (parent) {
	        prevChild = prev[parent];
	        prev[parent] = child;
	      } else {
	        prevChild = rootPrev;
	        rootPrev = child;
	      }
	      if (prevChild && prevChild !== child) {
	        cg.setEdge(prevChild, child);
	        return;
	      }
	      child = parent;
	    }
	  });

	  /*
	  function dfs(v) {
	    var children = v ? g.children(v) : g.children();
	    if (children.length) {
	      var min = Number.POSITIVE_INFINITY,
	          subgraphs = [];
	      children.forEach(function(child) {
	        var childMin = dfs(child);
	        if (g.children(child).length) {
	          subgraphs.push({ v: child, order: childMin });
	        }
	        min = Math.min(min, childMin);
	      });
	      _.sortBy(subgraphs, "order").reduce(function(prev, curr) {
	        cg.setEdge(prev.v, curr.v);
	        return curr;
	      });
	      return min;
	    }
	    return g.node(v).order;
	  }
	  dfs(undefined);
	  */
	}
	return addSubgraphConstraints_1;
}

var order_1;
var hasRequiredOrder;

function requireOrder () {
	if (hasRequiredOrder) return order_1;
	hasRequiredOrder = 1;

	let initOrder = requireInitOrder();
	let crossCount = requireCrossCount();
	let sortSubgraph = requireSortSubgraph();
	let buildLayerGraph = requireBuildLayerGraph();
	let addSubgraphConstraints = requireAddSubgraphConstraints();
	let Graph = requireGraphlib().Graph;
	let util = requireUtil$1();

	order_1 = order;

	/*
	 * Applies heuristics to minimize edge crossings in the graph and sets the best
	 * order solution as an order attribute on each node.
	 *
	 * Pre-conditions:
	 *
	 *    1. Graph must be DAG
	 *    2. Graph nodes must be objects with a "rank" attribute
	 *    3. Graph edges must have the "weight" attribute
	 *
	 * Post-conditions:
	 *
	 *    1. Graph nodes will have an "order" attribute based on the results of the
	 *       algorithm.
	 */
	function order(g, opts) {
	  if (opts && typeof opts.customOrder === 'function') {
	    opts.customOrder(g, order);
	    return;
	  }

	  let maxRank = util.maxRank(g),
	    downLayerGraphs = buildLayerGraphs(g, util.range(1, maxRank + 1), "inEdges"),
	    upLayerGraphs = buildLayerGraphs(g, util.range(maxRank - 1, -1, -1), "outEdges");

	  let layering = initOrder(g);
	  assignOrder(g, layering);

	  if (opts && opts.disableOptimalOrderHeuristic) {
	    return;
	  }

	  let bestCC = Number.POSITIVE_INFINITY,
	    best;

	  for (let i = 0, lastBest = 0; lastBest < 4; ++i, ++lastBest) {
	    sweepLayerGraphs(i % 2 ? downLayerGraphs : upLayerGraphs, i % 4 >= 2);

	    layering = util.buildLayerMatrix(g);
	    let cc = crossCount(g, layering);
	    if (cc < bestCC) {
	      lastBest = 0;
	      best = Object.assign({}, layering);
	      bestCC = cc;
	    }
	  }

	  assignOrder(g, best);
	}

	function buildLayerGraphs(g, ranks, relationship) {
	  return ranks.map(function(rank) {
	    return buildLayerGraph(g, rank, relationship);
	  });
	}

	function sweepLayerGraphs(layerGraphs, biasRight) {
	  let cg = new Graph();
	  layerGraphs.forEach(function(lg) {
	    let root = lg.graph().root;
	    let sorted = sortSubgraph(lg, root, cg, biasRight);
	    sorted.vs.forEach((v, i) => lg.node(v).order = i);
	    addSubgraphConstraints(lg, cg, sorted.vs);
	  });
	}

	function assignOrder(g, layering) {
	  Object.values(layering).forEach(layer => layer.forEach((v, i) => g.node(v).order = i));
	}
	return order_1;
}

var bk;
var hasRequiredBk;

function requireBk () {
	if (hasRequiredBk) return bk;
	hasRequiredBk = 1;

	let Graph = requireGraphlib().Graph;
	let util = requireUtil$1();

	/*
	 * This module provides coordinate assignment based on Brandes and Köpf, "Fast
	 * and Simple Horizontal Coordinate Assignment."
	 */

	bk = {
	  positionX: positionX,
	  findType1Conflicts: findType1Conflicts,
	  findType2Conflicts: findType2Conflicts,
	  addConflict: addConflict,
	  hasConflict: hasConflict,
	  verticalAlignment: verticalAlignment,
	  horizontalCompaction: horizontalCompaction,
	  alignCoordinates: alignCoordinates,
	  findSmallestWidthAlignment: findSmallestWidthAlignment,
	  balance: balance
	};

	/*
	 * Marks all edges in the graph with a type-1 conflict with the "type1Conflict"
	 * property. A type-1 conflict is one where a non-inner segment crosses an
	 * inner segment. An inner segment is an edge with both incident nodes marked
	 * with the "dummy" property.
	 *
	 * This algorithm scans layer by layer, starting with the second, for type-1
	 * conflicts between the current layer and the previous layer. For each layer
	 * it scans the nodes from left to right until it reaches one that is incident
	 * on an inner segment. It then scans predecessors to determine if they have
	 * edges that cross that inner segment. At the end a final scan is done for all
	 * nodes on the current rank to see if they cross the last visited inner
	 * segment.
	 *
	 * This algorithm (safely) assumes that a dummy node will only be incident on a
	 * single node in the layers being scanned.
	 */
	function findType1Conflicts(g, layering) {
	  let conflicts = {};

	  function visitLayer(prevLayer, layer) {
	    let
	      // last visited node in the previous layer that is incident on an inner
	      // segment.
	      k0 = 0,
	      // Tracks the last node in this layer scanned for crossings with a type-1
	      // segment.
	      scanPos = 0,
	      prevLayerLength = prevLayer.length,
	      lastNode = layer[layer.length - 1];

	    layer.forEach((v, i) => {
	      let w = findOtherInnerSegmentNode(g, v),
	        k1 = w ? g.node(w).order : prevLayerLength;

	      if (w || v === lastNode) {
	        layer.slice(scanPos, i+1).forEach(scanNode => {
	          g.predecessors(scanNode).forEach(u => {
	            let uLabel = g.node(u),
	              uPos = uLabel.order;
	            if ((uPos < k0 || k1 < uPos) &&
	                !(uLabel.dummy && g.node(scanNode).dummy)) {
	              addConflict(conflicts, u, scanNode);
	            }
	          });
	        });
	        scanPos = i + 1;
	        k0 = k1;
	      }
	    });

	    return layer;
	  }

	  layering.length && layering.reduce(visitLayer);

	  return conflicts;
	}

	function findType2Conflicts(g, layering) {
	  let conflicts = {};

	  function scan(south, southPos, southEnd, prevNorthBorder, nextNorthBorder) {
	    let v;
	    util.range(southPos, southEnd).forEach(i => {
	      v = south[i];
	      if (g.node(v).dummy) {
	        g.predecessors(v).forEach(u => {
	          let uNode = g.node(u);
	          if (uNode.dummy &&
	              (uNode.order < prevNorthBorder || uNode.order > nextNorthBorder)) {
	            addConflict(conflicts, u, v);
	          }
	        });
	      }
	    });
	  }


	  function visitLayer(north, south) {
	    let prevNorthPos = -1,
	      nextNorthPos,
	      southPos = 0;

	    south.forEach((v, southLookahead) => {
	      if (g.node(v).dummy === "border") {
	        let predecessors = g.predecessors(v);
	        if (predecessors.length) {
	          nextNorthPos = g.node(predecessors[0]).order;
	          scan(south, southPos, southLookahead, prevNorthPos, nextNorthPos);
	          southPos = southLookahead;
	          prevNorthPos = nextNorthPos;
	        }
	      }
	      scan(south, southPos, south.length, nextNorthPos, north.length);
	    });

	    return south;
	  }

	  layering.length && layering.reduce(visitLayer);

	  return conflicts;
	}

	function findOtherInnerSegmentNode(g, v) {
	  if (g.node(v).dummy) {
	    return g.predecessors(v).find(u => g.node(u).dummy);
	  }
	}

	function addConflict(conflicts, v, w) {
	  if (v > w) {
	    let tmp = v;
	    v = w;
	    w = tmp;
	  }

	  let conflictsV = conflicts[v];
	  if (!conflictsV) {
	    conflicts[v] = conflictsV = {};
	  }
	  conflictsV[w] = true;
	}

	function hasConflict(conflicts, v, w) {
	  if (v > w) {
	    let tmp = v;
	    v = w;
	    w = tmp;
	  }
	  return !!conflicts[v] && Object.hasOwn(conflicts[v], w);
	}

	/*
	 * Try to align nodes into vertical "blocks" where possible. This algorithm
	 * attempts to align a node with one of its median neighbors. If the edge
	 * connecting a neighbor is a type-1 conflict then we ignore that possibility.
	 * If a previous node has already formed a block with a node after the node
	 * we're trying to form a block with, we also ignore that possibility - our
	 * blocks would be split in that scenario.
	 */
	function verticalAlignment(g, layering, conflicts, neighborFn) {
	  let root = {},
	    align = {},
	    pos = {};

	  // We cache the position here based on the layering because the graph and
	  // layering may be out of sync. The layering matrix is manipulated to
	  // generate different extreme alignments.
	  layering.forEach(layer => {
	    layer.forEach((v, order) => {
	      root[v] = v;
	      align[v] = v;
	      pos[v] = order;
	    });
	  });

	  layering.forEach(layer => {
	    let prevIdx = -1;
	    layer.forEach(v => {
	      let ws = neighborFn(v);
	      if (ws.length) {
	        ws = ws.sort((a, b) => pos[a] - pos[b]);
	        let mp = (ws.length - 1) / 2;
	        for (let i = Math.floor(mp), il = Math.ceil(mp); i <= il; ++i) {
	          let w = ws[i];
	          if (align[v] === v &&
	              prevIdx < pos[w] &&
	              !hasConflict(conflicts, v, w)) {
	            align[w] = v;
	            align[v] = root[v] = root[w];
	            prevIdx = pos[w];
	          }
	        }
	      }
	    });
	  });

	  return { root: root, align: align };
	}

	function horizontalCompaction(g, layering, root, align, reverseSep) {
	  // This portion of the algorithm differs from BK due to a number of problems.
	  // Instead of their algorithm we construct a new block graph and do two
	  // sweeps. The first sweep places blocks with the smallest possible
	  // coordinates. The second sweep removes unused space by moving blocks to the
	  // greatest coordinates without violating separation.
	  let xs = {},
	    blockG = buildBlockGraph(g, layering, root, reverseSep),
	    borderType = reverseSep ? "borderLeft" : "borderRight";

	  function iterate(setXsFunc, nextNodesFunc) {
	    let stack = blockG.nodes();
	    let elem = stack.pop();
	    let visited = {};
	    while (elem) {
	      if (visited[elem]) {
	        setXsFunc(elem);
	      } else {
	        visited[elem] = true;
	        stack.push(elem);
	        stack = stack.concat(nextNodesFunc(elem));
	      }

	      elem = stack.pop();
	    }
	  }

	  // First pass, assign smallest coordinates
	  function pass1(elem) {
	    xs[elem] = blockG.inEdges(elem).reduce((acc, e) => {
	      return Math.max(acc, xs[e.v] + blockG.edge(e));
	    }, 0);
	  }

	  // Second pass, assign greatest coordinates
	  function pass2(elem) {
	    let min = blockG.outEdges(elem).reduce((acc, e) => {
	      return Math.min(acc, xs[e.w] - blockG.edge(e));
	    }, Number.POSITIVE_INFINITY);

	    let node = g.node(elem);
	    if (min !== Number.POSITIVE_INFINITY && node.borderType !== borderType) {
	      xs[elem] = Math.max(xs[elem], min);
	    }
	  }

	  iterate(pass1, blockG.predecessors.bind(blockG));
	  iterate(pass2, blockG.successors.bind(blockG));

	  // Assign x coordinates to all nodes
	  Object.keys(align).forEach(v => xs[v] = xs[root[v]]);

	  return xs;
	}


	function buildBlockGraph(g, layering, root, reverseSep) {
	  let blockGraph = new Graph(),
	    graphLabel = g.graph(),
	    sepFn = sep(graphLabel.nodesep, graphLabel.edgesep, reverseSep);

	  layering.forEach(layer => {
	    let u;
	    layer.forEach(v => {
	      let vRoot = root[v];
	      blockGraph.setNode(vRoot);
	      if (u) {
	        var uRoot = root[u],
	          prevMax = blockGraph.edge(uRoot, vRoot);
	        blockGraph.setEdge(uRoot, vRoot, Math.max(sepFn(g, v, u), prevMax || 0));
	      }
	      u = v;
	    });
	  });

	  return blockGraph;
	}

	/*
	 * Returns the alignment that has the smallest width of the given alignments.
	 */
	function findSmallestWidthAlignment(g, xss) {
	  return Object.values(xss).reduce((currentMinAndXs, xs) => {
	    let max = Number.NEGATIVE_INFINITY;
	    let min = Number.POSITIVE_INFINITY;

	    Object.entries(xs).forEach(([v, x]) => {
	      let halfWidth = width(g, v) / 2;

	      max = Math.max(x + halfWidth, max);
	      min = Math.min(x - halfWidth, min);
	    });

	    const newMin = max - min;
	    if (newMin < currentMinAndXs[0]) {
	      currentMinAndXs = [newMin, xs];
	    }
	    return currentMinAndXs;
	  }, [Number.POSITIVE_INFINITY, null])[1];
	}

	/*
	 * Align the coordinates of each of the layout alignments such that
	 * left-biased alignments have their minimum coordinate at the same point as
	 * the minimum coordinate of the smallest width alignment and right-biased
	 * alignments have their maximum coordinate at the same point as the maximum
	 * coordinate of the smallest width alignment.
	 */
	function alignCoordinates(xss, alignTo) {
	  let alignToVals = Object.values(alignTo),
	    alignToMin = util.applyWithChunking(Math.min, alignToVals),
	    alignToMax = util.applyWithChunking(Math.max, alignToVals);

	  ["u", "d"].forEach(vert => {
	    ["l", "r"].forEach(horiz => {
	      let alignment = vert + horiz,
	        xs = xss[alignment];

	      if (xs === alignTo) return;

	      let xsVals = Object.values(xs);
	      let delta = alignToMin - util.applyWithChunking(Math.min, xsVals);
	      if (horiz !== "l") {
	        delta = alignToMax - util.applyWithChunking(Math.max,xsVals);
	      }

	      if (delta) {
	        xss[alignment] = util.mapValues(xs, x => x + delta);
	      }
	    });
	  });
	}

	function balance(xss, align) {
	  return util.mapValues(xss.ul, (num, v) => {
	    if (align) {
	      return xss[align.toLowerCase()][v];
	    } else {
	      let xs = Object.values(xss).map(xs => xs[v]).sort((a, b) => a - b);
	      return (xs[1] + xs[2]) / 2;
	    }
	  });
	}

	function positionX(g) {
	  let layering = util.buildLayerMatrix(g);
	  let conflicts = Object.assign(
	    findType1Conflicts(g, layering),
	    findType2Conflicts(g, layering));

	  let xss = {};
	  let adjustedLayering;
	  ["u", "d"].forEach(vert => {
	    adjustedLayering = vert === "u" ? layering : Object.values(layering).reverse();
	    ["l", "r"].forEach(horiz => {
	      if (horiz === "r") {
	        adjustedLayering = adjustedLayering.map(inner => {
	          return Object.values(inner).reverse();
	        });
	      }

	      let neighborFn = (vert === "u" ? g.predecessors : g.successors).bind(g);
	      let align = verticalAlignment(g, adjustedLayering, conflicts, neighborFn);
	      let xs = horizontalCompaction(g, adjustedLayering,
	        align.root, align.align, horiz === "r");
	      if (horiz === "r") {
	        xs = util.mapValues(xs, x => -x);
	      }
	      xss[vert + horiz] = xs;
	    });
	  });


	  let smallestWidth = findSmallestWidthAlignment(g, xss);
	  alignCoordinates(xss, smallestWidth);
	  return balance(xss, g.graph().align);
	}

	function sep(nodeSep, edgeSep, reverseSep) {
	  return (g, v, w) => {
	    let vLabel = g.node(v);
	    let wLabel = g.node(w);
	    let sum = 0;
	    let delta;

	    sum += vLabel.width / 2;
	    if (Object.hasOwn(vLabel, "labelpos")) {
	      switch (vLabel.labelpos.toLowerCase()) {
	      case "l": delta = -vLabel.width / 2; break;
	      case "r": delta = vLabel.width / 2; break;
	      }
	    }
	    if (delta) {
	      sum += reverseSep ? delta : -delta;
	    }
	    delta = 0;

	    sum += (vLabel.dummy ? edgeSep : nodeSep) / 2;
	    sum += (wLabel.dummy ? edgeSep : nodeSep) / 2;

	    sum += wLabel.width / 2;
	    if (Object.hasOwn(wLabel, "labelpos")) {
	      switch (wLabel.labelpos.toLowerCase()) {
	      case "l": delta = wLabel.width / 2; break;
	      case "r": delta = -wLabel.width / 2; break;
	      }
	    }
	    if (delta) {
	      sum += reverseSep ? delta : -delta;
	    }
	    delta = 0;

	    return sum;
	  };
	}

	function width(g, v) {
	  return g.node(v).width;
	}
	return bk;
}

var position_1;
var hasRequiredPosition;

function requirePosition () {
	if (hasRequiredPosition) return position_1;
	hasRequiredPosition = 1;

	let util = requireUtil$1();
	let positionX = requireBk().positionX;

	position_1 = position;

	function position(g) {
	  g = util.asNonCompoundGraph(g);

	  positionY(g);
	  Object.entries(positionX(g)).forEach(([v, x]) => g.node(v).x = x);
	}

	function positionY(g) {
	  let layering = util.buildLayerMatrix(g);
	  let rankSep = g.graph().ranksep;
	  let prevY = 0;
	  layering.forEach(layer => {
	    const maxHeight = layer.reduce((acc, v) => {
	      const height = g.node(v).height;
	      if (acc > height) {
	        return acc;
	      } else {
	        return height;
	      }
	    }, 0);
	    layer.forEach(v => g.node(v).y = prevY + maxHeight / 2);
	    prevY += maxHeight + rankSep;
	  });
	}
	return position_1;
}

var layout_1;
var hasRequiredLayout;

function requireLayout () {
	if (hasRequiredLayout) return layout_1;
	hasRequiredLayout = 1;

	let acyclic = requireAcyclic();
	let normalize = requireNormalize();
	let rank = requireRank();
	let normalizeRanks = requireUtil$1().normalizeRanks;
	let parentDummyChains = requireParentDummyChains();
	let removeEmptyRanks = requireUtil$1().removeEmptyRanks;
	let nestingGraph = requireNestingGraph();
	let addBorderSegments = requireAddBorderSegments();
	let coordinateSystem = requireCoordinateSystem();
	let order = requireOrder();
	let position = requirePosition();
	let util = requireUtil$1();
	let Graph = requireGraphlib().Graph;

	layout_1 = layout;

	function layout(g, opts) {
	  let time = opts && opts.debugTiming ? util.time : util.notime;
	  time("layout", () => {
	    let layoutGraph =
	      time("  buildLayoutGraph", () => buildLayoutGraph(g));
	    time("  runLayout",        () => runLayout(layoutGraph, time, opts));
	    time("  updateInputGraph", () => updateInputGraph(g, layoutGraph));
	  });
	}

	function runLayout(g, time, opts) {
	  time("    makeSpaceForEdgeLabels", () => makeSpaceForEdgeLabels(g));
	  time("    removeSelfEdges",        () => removeSelfEdges(g));
	  time("    acyclic",                () => acyclic.run(g));
	  time("    nestingGraph.run",       () => nestingGraph.run(g));
	  time("    rank",                   () => rank(util.asNonCompoundGraph(g)));
	  time("    injectEdgeLabelProxies", () => injectEdgeLabelProxies(g));
	  time("    removeEmptyRanks",       () => removeEmptyRanks(g));
	  time("    nestingGraph.cleanup",   () => nestingGraph.cleanup(g));
	  time("    normalizeRanks",         () => normalizeRanks(g));
	  time("    assignRankMinMax",       () => assignRankMinMax(g));
	  time("    removeEdgeLabelProxies", () => removeEdgeLabelProxies(g));
	  time("    normalize.run",          () => normalize.run(g));
	  time("    parentDummyChains",      () => parentDummyChains(g));
	  time("    addBorderSegments",      () => addBorderSegments(g));
	  time("    order",                  () => order(g, opts));
	  time("    insertSelfEdges",        () => insertSelfEdges(g));
	  time("    adjustCoordinateSystem", () => coordinateSystem.adjust(g));
	  time("    position",               () => position(g));
	  time("    positionSelfEdges",      () => positionSelfEdges(g));
	  time("    removeBorderNodes",      () => removeBorderNodes(g));
	  time("    normalize.undo",         () => normalize.undo(g));
	  time("    fixupEdgeLabelCoords",   () => fixupEdgeLabelCoords(g));
	  time("    undoCoordinateSystem",   () => coordinateSystem.undo(g));
	  time("    translateGraph",         () => translateGraph(g));
	  time("    assignNodeIntersects",   () => assignNodeIntersects(g));
	  time("    reversePoints",          () => reversePointsForReversedEdges(g));
	  time("    acyclic.undo",           () => acyclic.undo(g));
	}

	/*
	 * Copies final layout information from the layout graph back to the input
	 * graph. This process only copies whitelisted attributes from the layout graph
	 * to the input graph, so it serves as a good place to determine what
	 * attributes can influence layout.
	 */
	function updateInputGraph(inputGraph, layoutGraph) {
	  inputGraph.nodes().forEach(v => {
	    let inputLabel = inputGraph.node(v);
	    let layoutLabel = layoutGraph.node(v);

	    if (inputLabel) {
	      inputLabel.x = layoutLabel.x;
	      inputLabel.y = layoutLabel.y;
	      inputLabel.rank = layoutLabel.rank;

	      if (layoutGraph.children(v).length) {
	        inputLabel.width = layoutLabel.width;
	        inputLabel.height = layoutLabel.height;
	      }
	    }
	  });

	  inputGraph.edges().forEach(e => {
	    let inputLabel = inputGraph.edge(e);
	    let layoutLabel = layoutGraph.edge(e);

	    inputLabel.points = layoutLabel.points;
	    if (Object.hasOwn(layoutLabel, "x")) {
	      inputLabel.x = layoutLabel.x;
	      inputLabel.y = layoutLabel.y;
	    }
	  });

	  inputGraph.graph().width = layoutGraph.graph().width;
	  inputGraph.graph().height = layoutGraph.graph().height;
	}

	let graphNumAttrs = ["nodesep", "edgesep", "ranksep", "marginx", "marginy"];
	let graphDefaults = { ranksep: 50, edgesep: 20, nodesep: 50, rankdir: "tb" };
	let graphAttrs = ["acyclicer", "ranker", "rankdir", "align"];
	let nodeNumAttrs = ["width", "height"];
	let nodeDefaults = { width: 0, height: 0 };
	let edgeNumAttrs = ["minlen", "weight", "width", "height", "labeloffset"];
	let edgeDefaults = {
	  minlen: 1, weight: 1, width: 0, height: 0,
	  labeloffset: 10, labelpos: "r"
	};
	let edgeAttrs = ["labelpos"];

	/*
	 * Constructs a new graph from the input graph, which can be used for layout.
	 * This process copies only whitelisted attributes from the input graph to the
	 * layout graph. Thus this function serves as a good place to determine what
	 * attributes can influence layout.
	 */
	function buildLayoutGraph(inputGraph) {
	  let g = new Graph({ multigraph: true, compound: true });
	  let graph = canonicalize(inputGraph.graph());

	  g.setGraph(Object.assign({},
	    graphDefaults,
	    selectNumberAttrs(graph, graphNumAttrs),
	    util.pick(graph, graphAttrs)));

	  inputGraph.nodes().forEach(v => {
	    let node = canonicalize(inputGraph.node(v));
	    const newNode = selectNumberAttrs(node, nodeNumAttrs);
	    Object.keys(nodeDefaults).forEach(k => {
	      if (newNode[k] === undefined) {
	        newNode[k] = nodeDefaults[k];
	      }
	    });

	    g.setNode(v, newNode);
	    g.setParent(v, inputGraph.parent(v));
	  });

	  inputGraph.edges().forEach(e => {
	    let edge = canonicalize(inputGraph.edge(e));
	    g.setEdge(e, Object.assign({},
	      edgeDefaults,
	      selectNumberAttrs(edge, edgeNumAttrs),
	      util.pick(edge, edgeAttrs)));
	  });

	  return g;
	}

	/*
	 * This idea comes from the Gansner paper: to account for edge labels in our
	 * layout we split each rank in half by doubling minlen and halving ranksep.
	 * Then we can place labels at these mid-points between nodes.
	 *
	 * We also add some minimal padding to the width to push the label for the edge
	 * away from the edge itself a bit.
	 */
	function makeSpaceForEdgeLabels(g) {
	  let graph = g.graph();
	  graph.ranksep /= 2;
	  g.edges().forEach(e => {
	    let edge = g.edge(e);
	    edge.minlen *= 2;
	    if (edge.labelpos.toLowerCase() !== "c") {
	      if (graph.rankdir === "TB" || graph.rankdir === "BT") {
	        edge.width += edge.labeloffset;
	      } else {
	        edge.height += edge.labeloffset;
	      }
	    }
	  });
	}

	/*
	 * Creates temporary dummy nodes that capture the rank in which each edge's
	 * label is going to, if it has one of non-zero width and height. We do this
	 * so that we can safely remove empty ranks while preserving balance for the
	 * label's position.
	 */
	function injectEdgeLabelProxies(g) {
	  g.edges().forEach(e => {
	    let edge = g.edge(e);
	    if (edge.width && edge.height) {
	      let v = g.node(e.v);
	      let w = g.node(e.w);
	      let label = { rank: (w.rank - v.rank) / 2 + v.rank, e: e };
	      util.addDummyNode(g, "edge-proxy", label, "_ep");
	    }
	  });
	}

	function assignRankMinMax(g) {
	  let maxRank = 0;
	  g.nodes().forEach(v => {
	    let node = g.node(v);
	    if (node.borderTop) {
	      node.minRank = g.node(node.borderTop).rank;
	      node.maxRank = g.node(node.borderBottom).rank;
	      maxRank = Math.max(maxRank, node.maxRank);
	    }
	  });
	  g.graph().maxRank = maxRank;
	}

	function removeEdgeLabelProxies(g) {
	  g.nodes().forEach(v => {
	    let node = g.node(v);
	    if (node.dummy === "edge-proxy") {
	      g.edge(node.e).labelRank = node.rank;
	      g.removeNode(v);
	    }
	  });
	}

	function translateGraph(g) {
	  let minX = Number.POSITIVE_INFINITY;
	  let maxX = 0;
	  let minY = Number.POSITIVE_INFINITY;
	  let maxY = 0;
	  let graphLabel = g.graph();
	  let marginX = graphLabel.marginx || 0;
	  let marginY = graphLabel.marginy || 0;

	  function getExtremes(attrs) {
	    let x = attrs.x;
	    let y = attrs.y;
	    let w = attrs.width;
	    let h = attrs.height;
	    minX = Math.min(minX, x - w / 2);
	    maxX = Math.max(maxX, x + w / 2);
	    minY = Math.min(minY, y - h / 2);
	    maxY = Math.max(maxY, y + h / 2);
	  }

	  g.nodes().forEach(v => getExtremes(g.node(v)));
	  g.edges().forEach(e => {
	    let edge = g.edge(e);
	    if (Object.hasOwn(edge, "x")) {
	      getExtremes(edge);
	    }
	  });

	  minX -= marginX;
	  minY -= marginY;

	  g.nodes().forEach(v => {
	    let node = g.node(v);
	    node.x -= minX;
	    node.y -= minY;
	  });

	  g.edges().forEach(e => {
	    let edge = g.edge(e);
	    edge.points.forEach(p => {
	      p.x -= minX;
	      p.y -= minY;
	    });
	    if (Object.hasOwn(edge, "x")) { edge.x -= minX; }
	    if (Object.hasOwn(edge, "y")) { edge.y -= minY; }
	  });

	  graphLabel.width = maxX - minX + marginX;
	  graphLabel.height = maxY - minY + marginY;
	}

	function assignNodeIntersects(g) {
	  g.edges().forEach(e => {
	    let edge = g.edge(e);
	    let nodeV = g.node(e.v);
	    let nodeW = g.node(e.w);
	    let p1, p2;
	    if (!edge.points) {
	      edge.points = [];
	      p1 = nodeW;
	      p2 = nodeV;
	    } else {
	      p1 = edge.points[0];
	      p2 = edge.points[edge.points.length - 1];
	    }
	    edge.points.unshift(util.intersectRect(nodeV, p1));
	    edge.points.push(util.intersectRect(nodeW, p2));
	  });
	}

	function fixupEdgeLabelCoords(g) {
	  g.edges().forEach(e => {
	    let edge = g.edge(e);
	    if (Object.hasOwn(edge, "x")) {
	      if (edge.labelpos === "l" || edge.labelpos === "r") {
	        edge.width -= edge.labeloffset;
	      }
	      switch (edge.labelpos) {
	      case "l": edge.x -= edge.width / 2 + edge.labeloffset; break;
	      case "r": edge.x += edge.width / 2 + edge.labeloffset; break;
	      }
	    }
	  });
	}

	function reversePointsForReversedEdges(g) {
	  g.edges().forEach(e => {
	    let edge = g.edge(e);
	    if (edge.reversed) {
	      edge.points.reverse();
	    }
	  });
	}

	function removeBorderNodes(g) {
	  g.nodes().forEach(v => {
	    if (g.children(v).length) {
	      let node = g.node(v);
	      let t = g.node(node.borderTop);
	      let b = g.node(node.borderBottom);
	      let l = g.node(node.borderLeft[node.borderLeft.length - 1]);
	      let r = g.node(node.borderRight[node.borderRight.length - 1]);

	      node.width = Math.abs(r.x - l.x);
	      node.height = Math.abs(b.y - t.y);
	      node.x = l.x + node.width / 2;
	      node.y = t.y + node.height / 2;
	    }
	  });

	  g.nodes().forEach(v => {
	    if (g.node(v).dummy === "border") {
	      g.removeNode(v);
	    }
	  });
	}

	function removeSelfEdges(g) {
	  g.edges().forEach(e => {
	    if (e.v === e.w) {
	      var node = g.node(e.v);
	      if (!node.selfEdges) {
	        node.selfEdges = [];
	      }
	      node.selfEdges.push({ e: e, label: g.edge(e) });
	      g.removeEdge(e);
	    }
	  });
	}

	function insertSelfEdges(g) {
	  var layers = util.buildLayerMatrix(g);
	  layers.forEach(layer => {
	    var orderShift = 0;
	    layer.forEach((v, i) => {
	      var node = g.node(v);
	      node.order = i + orderShift;
	      (node.selfEdges || []).forEach(selfEdge => {
	        util.addDummyNode(g, "selfedge", {
	          width: selfEdge.label.width,
	          height: selfEdge.label.height,
	          rank: node.rank,
	          order: i + (++orderShift),
	          e: selfEdge.e,
	          label: selfEdge.label
	        }, "_se");
	      });
	      delete node.selfEdges;
	    });
	  });
	}

	function positionSelfEdges(g) {
	  g.nodes().forEach(v => {
	    var node = g.node(v);
	    if (node.dummy === "selfedge") {
	      var selfNode = g.node(node.e.v);
	      var x = selfNode.x + selfNode.width / 2;
	      var y = selfNode.y;
	      var dx = node.x - x;
	      var dy = selfNode.height / 2;
	      g.setEdge(node.e, node.label);
	      g.removeNode(v);
	      node.label.points = [
	        { x: x + 2 * dx / 3, y: y - dy },
	        { x: x + 5 * dx / 6, y: y - dy },
	        { x: x +     dx    , y: y },
	        { x: x + 5 * dx / 6, y: y + dy },
	        { x: x + 2 * dx / 3, y: y + dy }
	      ];
	      node.label.x = node.x;
	      node.label.y = node.y;
	    }
	  });
	}

	function selectNumberAttrs(obj, attrs) {
	  return util.mapValues(util.pick(obj, attrs), Number);
	}

	function canonicalize(attrs) {
	  var newAttrs = {};
	  if (attrs) {
	    Object.entries(attrs).forEach(([k, v]) => {
	      if (typeof k === "string") {
	        k = k.toLowerCase();
	      }

	      newAttrs[k] = v;
	    });
	  }
	  return newAttrs;
	}
	return layout_1;
}

var debug;
var hasRequiredDebug;

function requireDebug () {
	if (hasRequiredDebug) return debug;
	hasRequiredDebug = 1;
	let util = requireUtil$1();
	let Graph = requireGraphlib().Graph;

	debug = {
	  debugOrdering: debugOrdering
	};

	/* istanbul ignore next */
	function debugOrdering(g) {
	  let layerMatrix = util.buildLayerMatrix(g);

	  let h = new Graph({ compound: true, multigraph: true }).setGraph({});

	  g.nodes().forEach(v => {
	    h.setNode(v, { label: v });
	    h.setParent(v, "layer" + g.node(v).rank);
	  });

	  g.edges().forEach(e => h.setEdge(e.v, e.w, {}, e.name));

	  layerMatrix.forEach((layer, i) => {
	    let layerV = "layer" + i;
	    h.setNode(layerV, { rank: "same" });
	    layer.reduce((u, v) => {
	      h.setEdge(u, v, { style: "invis" });
	      return v;
	    });
	  });

	  return h;
	}
	return debug;
}

var version;
var hasRequiredVersion;

function requireVersion () {
	if (hasRequiredVersion) return version;
	hasRequiredVersion = 1;
	version = "1.1.4";
	return version;
}

/*
Copyright (c) 2012-2014 Chris Pettitt

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
*/

var dagre;
var hasRequiredDagre;

function requireDagre () {
	if (hasRequiredDagre) return dagre;
	hasRequiredDagre = 1;
	dagre = {
	  graphlib: requireGraphlib(),

	  layout: requireLayout(),
	  debug: requireDebug(),
	  util: {
	    time: requireUtil$1().time,
	    notime: requireUtil$1().notime
	  },
	  version: requireVersion()
	};
	return dagre;
}

requireDagre();

export { DateToken as D, MapCache as M, PeriodType as P, Symbol$1 as S, isArray as a, baseGetTag as b, cls as c, defaultLocale as d, Duration as e, interpolatePath as f, isObjectLike as i, periodTypeMappings as p, range as r };
//# sourceMappingURL=index-DwwMqnhu.js.map
