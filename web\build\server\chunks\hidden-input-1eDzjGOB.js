import { p as push, Z as spread_attributes, Q as bind_props, q as pop } from './index3-CqUPEnZw.js';
import { m as mergeProps, h as srOnlyStylesString } from './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';

function Hidden_input($$payload, $$props) {
  push();
  let {
    value = void 0,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const mergedProps = mergeProps(restProps, {
    "aria-hidden": "true",
    tabindex: -1,
    style: srOnlyStylesString
  });
  if (mergedProps.type === "checkbox") {
    $$payload.out += "<!--[-->";
    $$payload.out += `<input${spread_attributes({ ...mergedProps, value }, null)}/>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<input${spread_attributes({ value, ...mergedProps }, null)}/>`;
  }
  $$payload.out += `<!--]-->`;
  bind_props($$props, { value });
  pop();
}

export { Hidden_input as H };
//# sourceMappingURL=hidden-input-1eDzjGOB.js.map
