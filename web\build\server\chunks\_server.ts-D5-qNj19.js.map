{"version": 3, "file": "_server.ts-D5-qNj19.js", "sources": ["../../../node_modules/uuid/dist/esm-node/rng.js", "../../../node_modules/uuid/dist/esm-node/stringify.js", "../../../node_modules/uuid/dist/esm-node/native.js", "../../../node_modules/uuid/dist/esm-node/v4.js", "../../../.svelte-kit/adapter-node/entries/endpoints/api/resume/upload/_server.ts.js"], "sourcesContent": ["import crypto from 'crypto';\nconst rnds8Pool = new Uint8Array(256); // # of random values to pre-allocate\n\nlet poolPtr = rnds8Pool.length;\nexport default function rng() {\n  if (poolPtr > rnds8Pool.length - 16) {\n    crypto.randomFillSync(rnds8Pool);\n    poolPtr = 0;\n  }\n\n  return rnds8Pool.slice(poolPtr, poolPtr += 16);\n}", "import validate from './validate.js';\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nconst byteToHex = [];\n\nfor (let i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).slice(1));\n}\n\nexport function unsafeStringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  return byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]];\n}\n\nfunction stringify(arr, offset = 0) {\n  const uuid = unsafeStringify(arr, offset); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!validate(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n\n  return uuid;\n}\n\nexport default stringify;", "import crypto from 'crypto';\nexport default {\n  randomUUID: crypto.randomUUID\n};", "import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\n\nfunction v4(options, buf, offset) {\n  if (native.randomUUID && !buf && !options) {\n    return native.randomUUID();\n  }\n\n  options = options || {};\n  const rnds = options.random || (options.rng || rng)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n\n    return buf;\n  }\n\n  return unsafeStringify(rnds);\n}\n\nexport default v4;", "import { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { R as RedisConnection } from \"../../../../../chunks/redis.js\";\nimport { v4 } from \"uuid\";\nimport { uploadDocumentToR2 } from \"../../../../../chunks/r2DocumentUpload.js\";\nimport { e as ensureUniqueDocumentName } from \"../../../../../chunks/documentNameUniqueness.js\";\nimport { d as determineDocumentSource } from \"../../../../../chunks/documentSource.js\";\nimport { c as canCreateResume, a as trackResumeCreation } from \"../../../../../chunks/resume-usage.js\";\nconst JOB_STREAM = \"resume-parsing::stream\";\nconst JOB_GROUP = \"resume-parsing::group\";\nfunction getMessage(skipParsing, parseIntoProfile) {\n  if (skipParsing) {\n    return \"Resume uploaded. It was already parsed previously.\";\n  }\n  if (parseIntoProfile) {\n    return \"Resume uploaded and parsing started. Profile will be updated with resume data.\";\n  }\n  return \"Resume uploaded successfully.\";\n}\nconst POST = async ({ request, locals }) => {\n  const user = locals.user;\n  console.log(\"User in resume upload:\", user);\n  if (!user) return new Response(\"Unauthorized\", { status: 401 });\n  if (!user.id) {\n    console.error(\"User missing ID:\", user);\n    return new Response(\"User ID missing\", { status: 400 });\n  }\n  const formData = await request.formData();\n  const file = formData.get(\"file\");\n  const profileId = formData.get(\"profileId\");\n  let label = formData.get(\"label\") || file.name;\n  const documentType = formData.get(\"type\") || \"resume\";\n  const parseIntoProfileStr = formData.get(\"parseIntoProfile\");\n  const parseIntoProfile = parseIntoProfileStr === \"true\";\n  if (!file) {\n    return new Response(\"Missing file\", { status: 400 });\n  }\n  if (profileId) {\n    const profile = await prisma.profile.findUnique({ where: { id: profileId } });\n    if (!profile) {\n      return new Response(\"Invalid profileId\", { status: 404 });\n    }\n  }\n  label = await ensureUniqueDocumentName(label, user.id, documentType);\n  try {\n    const isDev = process.env.NODE_ENV === \"development\" || process.env.VITE_DISABLE_FEATURE_LIMITS === \"true\";\n    if (!isDev) {\n      const canCreate = await canCreateResume(user.id);\n      if (!canCreate) {\n        return new Response(\n          JSON.stringify({\n            error: \"Document limit reached\",\n            limitReached: true,\n            message: \"You have reached your document upload limit. Please upgrade your plan to upload more documents.\"\n          }),\n          {\n            status: 403,\n            headers: { \"Content-Type\": \"application/json\" }\n          }\n        );\n      }\n    } else {\n      console.log(\"Development mode: Bypassing document limit check in resume upload API\");\n    }\n    console.log(\"Uploading resume:\", {\n      fileName: file.name,\n      fileType: file.type,\n      fileSize: file.size,\n      documentType\n    });\n    const identifier = profileId || user.id;\n    const uploadResult = await uploadDocumentToR2(file, \"resume\", identifier);\n    console.log(\"R2 upload result:\", uploadResult);\n    if (!uploadResult.success) {\n      return new Response(\n        JSON.stringify({\n          error: \"File upload failed\",\n          details: uploadResult.error\n        }),\n        { status: 500, headers: { \"Content-Type\": \"application/json\" } }\n      );\n    }\n    console.log(\"Creating document with data:\", {\n      label,\n      fileUrl: uploadResult.publicPath,\n      userId: user.id,\n      profileId: profileId || null,\n      type: documentType\n    });\n    console.log(\"User ID:\", user.id);\n    const document = await prisma.document.create({\n      data: {\n        label,\n        fileUrl: uploadResult.publicPath,\n        filePath: uploadResult.filePath,\n        fileName: uploadResult.originalFileName,\n        type: documentType,\n        contentType: uploadResult.contentType,\n        fileSize: uploadResult.fileSize,\n        storageType: \"r2\",\n        storageLocation: \"resumes\",\n        // Note: 'source' field is not in the Prisma schema, so we can't set it here\n        userId: user.id,\n        ...profileId ? { profileId } : {}\n      }\n    });\n    console.log(\"Document created:\", document);\n    const resume = await prisma.resume.create({\n      data: {\n        documentId: document.id\n      },\n      include: {\n        document: true\n      }\n    });\n    console.log(\"Resume created:\", resume);\n    try {\n      const { updateResumeFileKey } = await import(\"../../../../../chunks/r2DocumentUpload.js\");\n      const updateResult = await updateResumeFileKey(uploadResult.filePath, resume.id);\n      if (updateResult.success) {\n        await prisma.document.update({\n          where: { id: document.id },\n          data: {\n            filePath: updateResult.newFileKey,\n            fileUrl: updateResult.newPublicUrl\n          }\n        });\n        console.log(`Updated resume file key to: ${updateResult.newFileKey}`);\n      } else {\n        console.warn(`Failed to update resume file key: ${updateResult.error}`);\n      }\n    } catch (error) {\n      console.warn(\"Error updating resume file key:\", error);\n    }\n    await trackResumeCreation(user.id);\n    let skipParsing = false;\n    if (parseIntoProfile) {\n      const existingResume = await prisma.resume.findUnique({\n        where: { id: resume.id },\n        select: { isParsed: true, parsedAt: true, parsedData: true }\n      });\n      console.log(\"Existing resume parse status:\", existingResume);\n      if (existingResume?.isParsed && existingResume?.parsedData) {\n        console.log(\"Resume is already parsed, skipping parsing process\");\n        skipParsing = true;\n      }\n      if (!skipParsing) {\n        const jobId = v4();\n        try {\n          const jobData = {\n            jobId,\n            resumeId: resume.id,\n            filePath: uploadResult.filePath,\n            // Use the filePath directly from the upload result\n            fileUrl: uploadResult.publicPath,\n            userId: user.id,\n            profileId: profileId || \"\",\n            timestamp: (/* @__PURE__ */ new Date()).toISOString()\n          };\n          try {\n            await RedisConnection.xgroup(\"CREATE\", JOB_STREAM, JOB_GROUP, \"$\", \"MKSTREAM\");\n            console.log(`Created ${JOB_STREAM} stream group`);\n          } catch (err) {\n            if (!err.message.includes(\"BUSYGROUP\")) {\n              console.error(\"Error creating stream group:\", err);\n            }\n          }\n          await RedisConnection.xadd(\n            JOB_STREAM,\n            \"*\",\n            // Use '*' for auto-generated ID\n            \"job\",\n            JSON.stringify(jobData)\n          );\n          console.log(`Parsing job added to stream '${JOB_STREAM}':`, jobId);\n          await prisma.resume.update({\n            where: { id: resume.id },\n            data: {\n              isParsed: false,\n              parsedAt: null\n            }\n          });\n          console.log(\"Resume updated to reset parse status\");\n        } catch (redisError) {\n          console.error(\"Error adding job to Redis queue:\", redisError);\n        }\n      }\n    } else {\n      console.log(\"Skipping parsing as parseIntoProfile is false\");\n    }\n    const source = determineDocumentSource(document);\n    const responseData = {\n      resume,\n      document: {\n        ...document,\n        source\n        // Add the determined source information for the frontend\n      },\n      parseIntoProfile,\n      // Include the parseIntoProfile flag\n      profileId: profileId || null,\n      // Include the profileId\n      alreadyParsed: skipParsing,\n      // Include whether the resume was already parsed\n      message: getMessage(skipParsing, parseIntoProfile)\n    };\n    return new Response(JSON.stringify(responseData), {\n      headers: { \"Content-Type\": \"application/json\" }\n    });\n  } catch (error) {\n    console.error(\"Error creating resume:\", error);\n    if (error?.message?.includes(\"File type\")) {\n      return new Response(\n        JSON.stringify({\n          error: \"Invalid file type\",\n          details: error.message\n        }),\n        {\n          status: 400,\n          headers: { \"Content-Type\": \"application/json\" }\n        }\n      );\n    }\n    return new Response(\n      JSON.stringify({\n        error: \"Failed to create resume\",\n        details: error.message\n      }),\n      {\n        status: 500,\n        headers: { \"Content-Type\": \"application/json\" }\n      }\n    );\n  }\n};\nexport {\n  POST\n};\n"], "names": ["crypto"], "mappings": ";;;;;;;;;;;;;;;;AACA,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;;AAEtC,IAAI,OAAO,GAAG,SAAS,CAAC,MAAM;AACf,SAAS,GAAG,GAAG;AAC9B,EAAE,IAAI,OAAO,GAAG,SAAS,CAAC,MAAM,GAAG,EAAE,EAAE;AACvC,IAAIA,eAAM,CAAC,cAAc,CAAC,SAAS,CAAC;AACpC,IAAI,OAAO,GAAG,CAAC;AACf;;AAEA,EAAE,OAAO,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,IAAI,EAAE,CAAC;AAChD;;ACVA;AACA;AACA;AACA;;AAEA,MAAM,SAAS,GAAG,EAAE;;AAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE;AAC9B,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACnD;;AAEO,SAAS,eAAe,CAAC,GAAG,EAAE,MAAM,GAAG,CAAC,EAAE;AACjD;AACA;AACA,EAAE,OAAO,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;AACpf;;ACfA,aAAe;AACf,EAAE,UAAU,EAAEA,eAAM,CAAC;AACrB,CAAC;;ACCD,SAAS,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE;AAClC,EAAE,IAAI,MAAM,CAAC,UAAU,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;AAC7C,IAAI,OAAO,MAAM,CAAC,UAAU,EAAE;AAC9B;;AAEA,EAAE,OAAO,GAAG,OAAO,IAAI,EAAE;AACzB,EAAE,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;;AAExD,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI;AACjC,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;;AAYlC,EAAE,OAAO,eAAe,CAAC,IAAI,CAAC;AAC9B;;ACnBA,MAAM,UAAU,GAAG,wBAAwB;AAC3C,MAAM,SAAS,GAAG,uBAAuB;AACzC,SAAS,UAAU,CAAC,WAAW,EAAE,gBAAgB,EAAE;AACnD,EAAE,IAAI,WAAW,EAAE;AACnB,IAAI,OAAO,oDAAoD;AAC/D;AACA,EAAE,IAAI,gBAAgB,EAAE;AACxB,IAAI,OAAO,gFAAgF;AAC3F;AACA,EAAE,OAAO,+BAA+B;AACxC;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC;AAC7C,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;AAChB,IAAI,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,IAAI,CAAC;AAC3C,IAAI,OAAO,IAAI,QAAQ,CAAC,iBAAiB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,QAAQ,EAAE;AAC3C,EAAE,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;AACnC,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC;AAC7C,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,IAAI;AAChD,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,QAAQ;AACvD,EAAE,MAAM,mBAAmB,GAAG,QAAQ,CAAC,GAAG,CAAC,kBAAkB,CAAC;AAC9D,EAAE,MAAM,gBAAgB,GAAG,mBAAmB,KAAK,MAAM;AACzD,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,CAAC;AACjF,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,OAAO,IAAI,QAAQ,CAAC,mBAAmB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/D;AACA;AACA,EAAE,KAAK,GAAG,MAAM,wBAAwB,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,YAAY,CAAC;AACtE,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,2BAA2B,KAAK,MAAM;AAC9G,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;AACtD,MAAM,IAAI,CAAC,SAAS,EAAE;AACtB,QAAQ,OAAO,IAAI,QAAQ;AAC3B,UAAU,IAAI,CAAC,SAAS,CAAC;AACzB,YAAY,KAAK,EAAE,wBAAwB;AAC3C,YAAY,YAAY,EAAE,IAAI;AAC9B,YAAY,OAAO,EAAE;AACrB,WAAW,CAAC;AACZ,UAAU;AACV,YAAY,MAAM,EAAE,GAAG;AACvB,YAAY,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB;AACzD;AACA,SAAS;AACT;AACA,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,GAAG,CAAC,uEAAuE,CAAC;AAC1F;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE;AACrC,MAAM,QAAQ,EAAE,IAAI,CAAC,IAAI;AACzB,MAAM,QAAQ,EAAE,IAAI,CAAC,IAAI;AACzB,MAAM,QAAQ,EAAE,IAAI,CAAC,IAAI;AACzB,MAAM;AACN,KAAK,CAAC;AACN,IAAI,MAAM,UAAU,GAAG,SAAS,IAAI,IAAI,CAAC,EAAE;AAC3C,IAAI,MAAM,YAAY,GAAG,MAAM,kBAAkB,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,CAAC;AAC7E,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,YAAY,CAAC;AAClD,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;AAC/B,MAAM,OAAO,IAAI,QAAQ;AACzB,QAAQ,IAAI,CAAC,SAAS,CAAC;AACvB,UAAU,KAAK,EAAE,oBAAoB;AACrC,UAAU,OAAO,EAAE,YAAY,CAAC;AAChC,SAAS,CAAC;AACV,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;AACtE,OAAO;AACP;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE;AAChD,MAAM,KAAK;AACX,MAAM,OAAO,EAAE,YAAY,CAAC,UAAU;AACtC,MAAM,MAAM,EAAE,IAAI,CAAC,EAAE;AACrB,MAAM,SAAS,EAAE,SAAS,IAAI,IAAI;AAClC,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC;AACpC,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AAClD,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK;AACb,QAAQ,OAAO,EAAE,YAAY,CAAC,UAAU;AACxC,QAAQ,QAAQ,EAAE,YAAY,CAAC,QAAQ;AACvC,QAAQ,QAAQ,EAAE,YAAY,CAAC,gBAAgB;AAC/C,QAAQ,IAAI,EAAE,YAAY;AAC1B,QAAQ,WAAW,EAAE,YAAY,CAAC,WAAW;AAC7C,QAAQ,QAAQ,EAAE,YAAY,CAAC,QAAQ;AACvC,QAAQ,WAAW,EAAE,IAAI;AACzB,QAAQ,eAAe,EAAE,SAAS;AAClC;AACA,QAAQ,MAAM,EAAE,IAAI,CAAC,EAAE;AACvB,QAAQ,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,GAAG;AACvC;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,QAAQ,CAAC;AAC9C,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AAC9C,MAAM,IAAI,EAAE;AACZ,QAAQ,UAAU,EAAE,QAAQ,CAAC;AAC7B,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC;AAC1C,IAAI,IAAI;AACR,MAAM,MAAM,EAAE,mBAAmB,EAAE,GAAG,MAAM,OAAO,gCAA2C,CAAC;AAC/F,MAAM,MAAM,YAAY,GAAG,MAAM,mBAAmB,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,CAAC;AACtF,MAAM,IAAI,YAAY,CAAC,OAAO,EAAE;AAChC,QAAQ,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AACrC,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AACpC,UAAU,IAAI,EAAE;AAChB,YAAY,QAAQ,EAAE,YAAY,CAAC,UAAU;AAC7C,YAAY,OAAO,EAAE,YAAY,CAAC;AAClC;AACA,SAAS,CAAC;AACV,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;AAC7E,OAAO,MAAM;AACb,QAAQ,OAAO,CAAC,IAAI,CAAC,CAAC,kCAAkC,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/E;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,IAAI,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC5D;AACA,IAAI,MAAM,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;AACtC,IAAI,IAAI,WAAW,GAAG,KAAK;AAC3B,IAAI,IAAI,gBAAgB,EAAE;AAC1B,MAAM,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;AAC5D,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE;AAChC,QAAQ,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI;AAClE,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,cAAc,CAAC;AAClE,MAAM,IAAI,cAAc,EAAE,QAAQ,IAAI,cAAc,EAAE,UAAU,EAAE;AAClE,QAAQ,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC;AACzE,QAAQ,WAAW,GAAG,IAAI;AAC1B;AACA,MAAM,IAAI,CAAC,WAAW,EAAE;AACxB,QAAQ,MAAM,KAAK,GAAG,EAAE,EAAE;AAC1B,QAAQ,IAAI;AACZ,UAAU,MAAM,OAAO,GAAG;AAC1B,YAAY,KAAK;AACjB,YAAY,QAAQ,EAAE,MAAM,CAAC,EAAE;AAC/B,YAAY,QAAQ,EAAE,YAAY,CAAC,QAAQ;AAC3C;AACA,YAAY,OAAO,EAAE,YAAY,CAAC,UAAU;AAC5C,YAAY,MAAM,EAAE,IAAI,CAAC,EAAE;AAC3B,YAAY,SAAS,EAAE,SAAS,IAAI,EAAE;AACtC,YAAY,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC/D,WAAW;AACX,UAAU,IAAI;AACd,YAAY,MAAM,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,EAAE,UAAU,CAAC;AAC1F,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;AAC7D,WAAW,CAAC,OAAO,GAAG,EAAE;AACxB,YAAY,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;AACpD,cAAc,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,GAAG,CAAC;AAChE;AACA;AACA,UAAU,MAAM,eAAe,CAAC,IAAI;AACpC,YAAY,UAAU;AACtB,YAAY,GAAG;AACf;AACA,YAAY,KAAK;AACjB,YAAY,IAAI,CAAC,SAAS,CAAC,OAAO;AAClC,WAAW;AACX,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,UAAU,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;AAC5E,UAAU,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AACrC,YAAY,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE;AACpC,YAAY,IAAI,EAAE;AAClB,cAAc,QAAQ,EAAE,KAAK;AAC7B,cAAc,QAAQ,EAAE;AACxB;AACA,WAAW,CAAC;AACZ,UAAU,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC;AAC7D,SAAS,CAAC,OAAO,UAAU,EAAE;AAC7B,UAAU,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,UAAU,CAAC;AACvE;AACA;AACA,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC;AAClE;AACA,IAAI,MAAM,MAAM,GAAG,uBAAuB,CAAC,QAAQ,CAAC;AACpD,IAAI,MAAM,YAAY,GAAG;AACzB,MAAM,MAAM;AACZ,MAAM,QAAQ,EAAE;AAChB,QAAQ,GAAG,QAAQ;AACnB,QAAQ;AACR;AACA,OAAO;AACP,MAAM,gBAAgB;AACtB;AACA,MAAM,SAAS,EAAE,SAAS,IAAI,IAAI;AAClC;AACA,MAAM,aAAa,EAAE,WAAW;AAChC;AACA,MAAM,OAAO,EAAE,UAAU,CAAC,WAAW,EAAE,gBAAgB;AACvD,KAAK;AACL,IAAI,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE;AACtD,MAAM,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB;AACnD,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC;AAClD,IAAI,IAAI,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC,WAAW,CAAC,EAAE;AAC/C,MAAM,OAAO,IAAI,QAAQ;AACzB,QAAQ,IAAI,CAAC,SAAS,CAAC;AACvB,UAAU,KAAK,EAAE,mBAAmB;AACpC,UAAU,OAAO,EAAE,KAAK,CAAC;AACzB,SAAS,CAAC;AACV,QAAQ;AACR,UAAU,MAAM,EAAE,GAAG;AACrB,UAAU,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB;AACvD;AACA,OAAO;AACP;AACA,IAAI,OAAO,IAAI,QAAQ;AACvB,MAAM,IAAI,CAAC,SAAS,CAAC;AACrB,QAAQ,KAAK,EAAE,yBAAyB;AACxC,QAAQ,OAAO,EAAE,KAAK,CAAC;AACvB,OAAO,CAAC;AACR,MAAM;AACN,QAAQ,MAAM,EAAE,GAAG;AACnB,QAAQ,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB;AACrD;AACA,KAAK;AACL;AACA;;;;", "x_google_ignoreList": [0, 1, 2, 3]}