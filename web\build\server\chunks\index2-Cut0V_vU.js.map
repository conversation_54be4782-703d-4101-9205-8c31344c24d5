{"version": 3, "file": "index2-Cut0V_vU.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/index2.js"], "sourcesContent": ["import { z as noop, A as subscribe_to_store, B as safe_not_equal, C as run_all } from \"./index3.js\";\nconst subscriber_queue = [];\nfunction readable(value, start) {\n  return {\n    subscribe: writable(value, start).subscribe\n  };\n}\nfunction writable(value, start = noop) {\n  let stop = null;\n  const subscribers = /* @__PURE__ */ new Set();\n  function set(new_value) {\n    if (safe_not_equal(value, new_value)) {\n      value = new_value;\n      if (stop) {\n        const run_queue = !subscriber_queue.length;\n        for (const subscriber of subscribers) {\n          subscriber[1]();\n          subscriber_queue.push(subscriber, value);\n        }\n        if (run_queue) {\n          for (let i = 0; i < subscriber_queue.length; i += 2) {\n            subscriber_queue[i][0](subscriber_queue[i + 1]);\n          }\n          subscriber_queue.length = 0;\n        }\n      }\n    }\n  }\n  function update(fn) {\n    set(fn(\n      /** @type {T} */\n      value\n    ));\n  }\n  function subscribe(run, invalidate = noop) {\n    const subscriber = [run, invalidate];\n    subscribers.add(subscriber);\n    if (subscribers.size === 1) {\n      stop = start(set, update) || noop;\n    }\n    run(\n      /** @type {T} */\n      value\n    );\n    return () => {\n      subscribers.delete(subscriber);\n      if (subscribers.size === 0 && stop) {\n        stop();\n        stop = null;\n      }\n    };\n  }\n  return { set, update, subscribe };\n}\nfunction derived(stores, fn, initial_value) {\n  const single = !Array.isArray(stores);\n  const stores_array = single ? [stores] : stores;\n  if (!stores_array.every(Boolean)) {\n    throw new Error(\"derived() expects stores as input, got a falsy value\");\n  }\n  const auto = fn.length < 2;\n  return readable(initial_value, (set, update) => {\n    let started = false;\n    const values = [];\n    let pending = 0;\n    let cleanup = noop;\n    const sync = () => {\n      if (pending) {\n        return;\n      }\n      cleanup();\n      const result = fn(single ? values[0] : values, set, update);\n      if (auto) {\n        set(result);\n      } else {\n        cleanup = typeof result === \"function\" ? result : noop;\n      }\n    };\n    const unsubscribers = stores_array.map(\n      (store, i) => subscribe_to_store(\n        store,\n        (value) => {\n          values[i] = value;\n          pending &= ~(1 << i);\n          if (started) {\n            sync();\n          }\n        },\n        () => {\n          pending |= 1 << i;\n        }\n      )\n    );\n    started = true;\n    sync();\n    return function stop() {\n      run_all(unsubscribers);\n      cleanup();\n      started = false;\n    };\n  });\n}\nfunction readonly(store) {\n  return {\n    // @ts-expect-error TODO i suspect the bind is unnecessary\n    subscribe: store.subscribe.bind(store)\n  };\n}\nfunction get(store) {\n  let value;\n  subscribe_to_store(store, (_) => value = _)();\n  return value;\n}\nexport {\n  readonly as a,\n  derived as d,\n  get as g,\n  readable as r,\n  writable as w\n};\n"], "names": [], "mappings": ";;AACA,MAAM,gBAAgB,GAAG,EAAE;AAC3B,SAAS,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE;AAChC,EAAE,OAAO;AACT,IAAI,SAAS,EAAE,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACtC,GAAG;AACH;AACA,SAAS,QAAQ,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI,EAAE;AACvC,EAAE,IAAI,IAAI,GAAG,IAAI;AACjB,EAAE,MAAM,WAAW,mBAAmB,IAAI,GAAG,EAAE;AAC/C,EAAE,SAAS,GAAG,CAAC,SAAS,EAAE;AAC1B,IAAI,IAAI,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE;AAC1C,MAAM,KAAK,GAAG,SAAS;AACvB,MAAM,IAAI,IAAI,EAAE;AAChB,QAAQ,MAAM,SAAS,GAAG,CAAC,gBAAgB,CAAC,MAAM;AAClD,QAAQ,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;AAC9C,UAAU,UAAU,CAAC,CAAC,CAAC,EAAE;AACzB,UAAU,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC;AAClD;AACA,QAAQ,IAAI,SAAS,EAAE;AACvB,UAAU,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AAC/D,YAAY,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3D;AACA,UAAU,gBAAgB,CAAC,MAAM,GAAG,CAAC;AACrC;AACA;AACA;AACA;AACA,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE;AACtB,IAAI,GAAG,CAAC,EAAE;AACV;AACA,MAAM;AACN,KAAK,CAAC;AACN;AACA,EAAE,SAAS,SAAS,CAAC,GAAG,EAAE,UAAU,GAAG,IAAI,EAAE;AAC7C,IAAI,MAAM,UAAU,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC;AACxC,IAAI,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC;AAC/B,IAAI,IAAI,WAAW,CAAC,IAAI,KAAK,CAAC,EAAE;AAChC,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,IAAI;AACvC;AACA,IAAI,GAAG;AACP;AACA,MAAM;AACN,KAAK;AACL,IAAI,OAAO,MAAM;AACjB,MAAM,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC;AACpC,MAAM,IAAI,WAAW,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,EAAE;AAC1C,QAAQ,IAAI,EAAE;AACd,QAAQ,IAAI,GAAG,IAAI;AACnB;AACA,KAAK;AACL;AACA,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE;AACnC;AACA,SAAS,OAAO,CAAC,MAAM,EAAE,EAAE,EAAE,aAAa,EAAE;AAC5C,EAAE,MAAM,MAAM,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;AACvC,EAAE,MAAM,YAAY,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC,GAAG,MAAM;AACjD,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;AACpC,IAAI,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC;AAC3E;AACA,EAAE,MAAM,IAAI,GAAG,EAAE,CAAC,MAAM,GAAG,CAAC;AAC5B,EAAE,OAAO,QAAQ,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK;AAClD,IAAI,IAAI,OAAO,GAAG,KAAK;AACvB,IAAI,MAAM,MAAM,GAAG,EAAE;AACrB,IAAI,IAAI,OAAO,GAAG,CAAC;AACnB,IAAI,IAAI,OAAO,GAAG,IAAI;AACtB,IAAI,MAAM,IAAI,GAAG,MAAM;AACvB,MAAM,IAAI,OAAO,EAAE;AACnB,QAAQ;AACR;AACA,MAAM,OAAO,EAAE;AACf,MAAM,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,MAAM,CAAC;AACjE,MAAM,IAAI,IAAI,EAAE;AAChB,QAAQ,GAAG,CAAC,MAAM,CAAC;AACnB,OAAO,MAAM;AACb,QAAQ,OAAO,GAAG,OAAO,MAAM,KAAK,UAAU,GAAG,MAAM,GAAG,IAAI;AAC9D;AACA,KAAK;AACL,IAAI,MAAM,aAAa,GAAG,YAAY,CAAC,GAAG;AAC1C,MAAM,CAAC,KAAK,EAAE,CAAC,KAAK,kBAAkB;AACtC,QAAQ,KAAK;AACb,QAAQ,CAAC,KAAK,KAAK;AACnB,UAAU,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK;AAC3B,UAAU,OAAO,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;AAC9B,UAAU,IAAI,OAAO,EAAE;AACvB,YAAY,IAAI,EAAE;AAClB;AACA,SAAS;AACT,QAAQ,MAAM;AACd,UAAU,OAAO,IAAI,CAAC,IAAI,CAAC;AAC3B;AACA;AACA,KAAK;AACL,IAAI,OAAO,GAAG,IAAI;AAClB,IAAI,IAAI,EAAE;AACV,IAAI,OAAO,SAAS,IAAI,GAAG;AAC3B,MAAM,OAAO,CAAC,aAAa,CAAC;AAC5B,MAAM,OAAO,EAAE;AACf,MAAM,OAAO,GAAG,KAAK;AACrB,KAAK;AACL,GAAG,CAAC;AACJ;AACA,SAAS,QAAQ,CAAC,KAAK,EAAE;AACzB,EAAE,OAAO;AACT;AACA,IAAI,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK;AACzC,GAAG;AACH;AACA,SAAS,GAAG,CAAC,KAAK,EAAE;AACpB,EAAE,IAAI,KAAK;AACX,EAAE,kBAAkB,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC,EAAE;AAC/C,EAAE,OAAO,KAAK;AACd;;;;"}