import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

function scanRoutes(routesDir, basePath = "", ignorePatterns = []) {
  const routes = [];
  if (!fs.existsSync(routesDir)) {
    return routes;
  }
  const entries = fs.readdirSync(routesDir, { withFileTypes: true });
  for (const entry of entries) {
    const entryPath = path.join(routesDir, entry.name);
    if (entry.name.startsWith(".") || entry.name.startsWith("_")) {
      continue;
    }
    if (entry.isDirectory()) {
      if (entry.name === "api" || entry.name === "dashboard" || entry.name === "sitemap.xml" || entry.name === "robots.txt") {
        continue;
      }
      const routeName = entry.name.replace(/\[\.\.\.(.*)\]/, "").replace(/\[(.*)\]/, "");
      const hasPage = fs.existsSync(path.join(entryPath, "+page.svelte")) || fs.existsSync(path.join(entryPath, "+page.js")) || fs.existsSync(path.join(entryPath, "+page.ts"));
      if (hasPage) {
        if (!entry.name.includes("[")) {
          const routePath = path.join(basePath, routeName).replace(/\\/g, "/");
          const shouldIgnore = ignorePatterns.some((pattern) => {
            const normalizedPath = routePath.replace(/\\/g, "/");
            const regexPattern = pattern.replace(/\./g, "\\.").replace(/\*/g, ".*");
            const regex = new RegExp(`^${regexPattern}$|^${regexPattern}/`);
            return regex.test(normalizedPath);
          });
          if (!shouldIgnore) {
            routes.push(routePath);
          }
        }
      }
      const subRoutes = scanRoutes(entryPath, path.join(basePath, routeName), ignorePatterns);
      routes.push(...subRoutes);
    } else if (entry.name === "+page.svelte" || entry.name === "+page.js" || entry.name === "+page.ts") {
      if (basePath && !routes.includes(basePath)) {
        routes.push(basePath);
      }
    }
  }
  return routes;
}
function generateSitemap(domain, routes, options = {}, ignoreRoutes = []) {
  const {
    changefreq = "weekly",
    priority = 0.7,
    includeAgents = false,
    includeImages = true,
    includeNews = false,
    includeVideo = false
  } = options;
  const today = (/* @__PURE__ */ new Date()).toISOString();
  if (!routes.includes("")) {
    routes.unshift("");
  }
  const filteredRoutes = routes.filter((route) => {
    const normalizedRoute = route.replace(/\\/g, "/");
    if (ignoreRoutes.includes(normalizedRoute)) {
      return false;
    }
    for (const ignoreRoute of ignoreRoutes) {
      if (normalizedRoute === ignoreRoute || normalizedRoute.startsWith(`${ignoreRoute}/`)) {
        return false;
      }
    }
    return true;
  });
  let namespaces = 'xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"';
  if (includeAgents) {
    namespaces += ' xmlns:xhtml="http://www.w3.org/1999/xhtml"';
  }
  if (includeImages) {
    namespaces += ' xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"';
  }
  if (includeNews) {
    namespaces += ' xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"';
  }
  if (includeVideo) {
    namespaces += ' xmlns:video="http://www.google.com/schemas/sitemap-video/1.1"';
  }
  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset ${namespaces}>
`;
  const routeSettings = {
    "": { changefreq: "daily", priority: "1.0" },
    blog: { changefreq: "daily", priority: "0.9" },
    press: { changefreq: "weekly", priority: "0.8" },
    "press/releases": { changefreq: "weekly", priority: "0.8" },
    jobs: { changefreq: "daily", priority: "0.9" },
    about: { changefreq: "monthly", priority: "0.7" },
    contact: { changefreq: "monthly", priority: "0.7" },
    pricing: { changefreq: "monthly", priority: "0.8" },
    terms: { changefreq: "yearly", priority: "0.5" },
    privacy: { changefreq: "yearly", priority: "0.5" },
    "system-status": { changefreq: "hourly", priority: "0.6" }
  };
  sitemap += filteredRoutes.map((route) => {
    const settings = routeSettings[route] || {
      changefreq,
      priority
    };
    let entry = `  <url>
    <loc>${domain}/${route}</loc>
    <lastmod>${today}</lastmod>
    <changefreq>${settings.changefreq}</changefreq>
    <priority>${settings.priority}</priority>`;
    if (includeAgents) {
      entry += `
    <xhtml:link rel="alternate" href="${domain}/${route}" hreflang="en" />
    <xhtml:link rel="alternate" media="only screen and (max-width: 640px)" href="${domain}/${route}" />`;
    }
    if (includeImages && (route === "" || route === "about" || route.startsWith("press"))) {
      entry += `
    <image:image>
      <image:loc>${domain}/images/logo.png</image:loc>
      <image:title>Hirli Logo</image:title>
      <image:caption>Hirli - AI-powered job application platform</image:caption>
    </image:image>`;
    }
    if (includeNews && route === "press/releases") {
      entry += `
    <news:news>
      <news:publication>
        <news:name>Hirli Press Releases</news:name>
        <news:language>en</news:language>
      </news:publication>
      <news:publication_date>${today}</news:publication_date>
      <news:title>Latest Hirli Press Releases</news:title>
    </news:news>`;
    }
    entry += `
  </url>`;
    return entry;
  }).join("\n");
  sitemap += `
</urlset>`;
  return sitemap;
}
const GET = async ({ url }) => {
  const host = process.env.NODE_ENV === "production" ? "hirli.com" : url.host;
  const protocol = process.env.NODE_ENV === "production" ? "https" : url.protocol.slice(0, -1);
  const domain = `${protocol}://${host}`;
  const __filename = fileURLToPath(import.meta.url);
  const __dirname = path.dirname(__filename);
  const routesDir = path.resolve(__dirname, "..");
  const ignorePatterns = [
    "auth/*",
    "dashboard/*",
    "api/*",
    "admin/*",
    "legal/*/edit",
    "profile/*",
    "settings/*",
    "account/*",
    "user/*",
    "notifications/*",
    "test/*",
    "demo/*",
    "beta/*",
    "dev/*",
    "staging/*"
  ];
  const ignoreRoutes = [
    "auth",
    "login",
    "signup",
    "reset-password",
    "verify-email",
    "forgot-password",
    "logout",
    "callback"
  ];
  const routes = scanRoutes(routesDir, "", ignorePatterns);
  const additionalRoutes = [
    "blog",
    "jobs",
    "press",
    "press/releases",
    "press/images",
    "press/logo-guidelines",
    "press/brand-guidelines",
    "press/contact",
    "system-status"
  ];
  const allRoutes = [.../* @__PURE__ */ new Set([...routes, ...additionalRoutes])];
  const sitemap = generateSitemap(
    domain,
    allRoutes,
    {
      changefreq: "weekly",
      priority: 0.8,
      includeAgents: true,
      includeImages: true,
      includeNews: process.env.NODE_ENV === "production",
      // Only include news in production
      includeVideo: false
    },
    ignoreRoutes
  );
  return new Response(sitemap, {
    headers: {
      "Content-Type": "application/xml",
      "Cache-Control": "max-age=0, s-maxage=3600"
    }
  });
};

export { GET };
//# sourceMappingURL=_server.ts-B3hQowIH.js.map
