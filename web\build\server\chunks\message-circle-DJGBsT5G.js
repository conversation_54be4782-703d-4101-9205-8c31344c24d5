import { X as sanitize_props, R as spread_props, a0 as slot } from './index3-CqUPEnZw.js';
import { I as Icon } from './Icon-A4vzmk-O.js';

function Message_circle($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    [
      "path",
      { "d": "M7.9 20A9 9 0 1 0 4 16.1L2 22Z" }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "message-circle" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}

export { Message_circle as M };
//# sourceMappingURL=message-circle-DJGBsT5G.js.map
