import { L as LimitType, a as FeatureAccessLevel } from './features-SWeUHekJ.js';
import { F as FEATURES, g as getFeatureLimitById, b as getFeatureById } from './dynamic-registry-Cmy1Wm2Q.js';

class FeatureAccess {
  userData;
  constructor(userData) {
    this.userData = userData;
  }
  /**
   * Check if a feature is available to the user
   * @param featureId Feature ID
   * @returns True if the feature is available
   */
  hasAccess(featureId) {
    if (!this.userData.plan) return false;
    const planFeature = this.getPlanFeature(featureId);
    if (!planFeature) return false;
    return planFeature.accessLevel !== FeatureAccessLevel.NotIncluded;
  }
  /**
   * Get the access level for a feature
   * @param featureId Feature ID
   * @returns Access level or NotIncluded if not available
   */
  getAccessLevel(featureId) {
    if (!this.userData.plan) return FeatureAccessLevel.NotIncluded;
    const planFeature = this.getPlanFeature(featureId);
    return planFeature?.accessLevel || FeatureAccessLevel.NotIncluded;
  }
  /**
   * Check if a feature has a specific limit
   * @param featureId Feature ID
   * @param limitId Limit ID
   * @returns True if the feature has the limit
   */
  hasLimit(featureId, limitId) {
    if (!this.userData.plan) return false;
    const planFeature = this.getPlanFeature(featureId);
    if (!planFeature) return false;
    if (planFeature.accessLevel === FeatureAccessLevel.Unlimited) return false;
    return !!this.getLimitValue(featureId, limitId);
  }
  /**
   * Get the limit value for a feature
   * @param featureId Feature ID
   * @param limitId Limit ID
   * @returns Limit value or undefined if not found
   */
  getLimitValue(featureId, limitId) {
    if (!this.userData.plan) return void 0;
    const planFeature = this.getPlanFeature(featureId);
    if (!planFeature) return void 0;
    if (planFeature.accessLevel === FeatureAccessLevel.Unlimited) return "unlimited";
    if (planFeature.accessLevel !== FeatureAccessLevel.Limited) return void 0;
    const limitValue = planFeature.limits?.find((limit) => limit.limitId === limitId);
    return limitValue?.value;
  }
  /**
   * Get the numeric limit value for a feature
   * @param featureId Feature ID
   * @param limitId Limit ID
   * @param defaultValue Default value if not found
   * @returns Numeric limit value
   */
  getNumericLimitValue(featureId, limitId, defaultValue) {
    const limitValue = this.getLimitValue(featureId, limitId);
    if (limitValue === void 0) return defaultValue;
    if (limitValue === "unlimited") return Infinity;
    return limitValue;
  }
  /**
   * Get the current usage for a feature limit
   * @param featureId Feature ID
   * @param limitId Limit ID
   * @returns Current usage or 0 if not found
   */
  getCurrentUsage(featureId, limitId) {
    const limit = getFeatureLimitById(limitId);
    if (!limit) return 0;
    if (limit.type === LimitType.Monthly) {
      const now = /* @__PURE__ */ new Date();
      const period = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, "0")}`;
      const usage2 = this.userData.usage.find(
        (u) => u.featureId === featureId && u.limitId === limitId && u.period === period
      );
      return usage2?.used || 0;
    }
    const usage = this.userData.usage.find(
      (u) => u.featureId === featureId && u.limitId === limitId
    );
    return usage?.used || 0;
  }
  /**
   * Check if a user has reached their limit for a feature
   * @param featureId Feature ID
   * @param limitId Limit ID
   * @returns True if the user has reached their limit
   */
  hasReachedLimit(featureId, limitId) {
    if (!this.userData.plan) return true;
    const planFeature = this.getPlanFeature(featureId);
    if (!planFeature) return true;
    if (planFeature.accessLevel === FeatureAccessLevel.Unlimited) return false;
    if (planFeature.accessLevel !== FeatureAccessLevel.Limited) return false;
    const limitValue = this.getLimitValue(featureId, limitId);
    if (limitValue === void 0) return false;
    if (limitValue === "unlimited") return false;
    const currentUsage = this.getCurrentUsage(featureId, limitId);
    return currentUsage >= limitValue;
  }
  /**
   * Get the remaining usage for a feature limit
   * @param featureId Feature ID
   * @param limitId Limit ID
   * @returns Remaining usage or Infinity if unlimited
   */
  getRemainingUsage(featureId, limitId) {
    if (!this.userData.plan) return 0;
    const planFeature = this.getPlanFeature(featureId);
    if (!planFeature) return 0;
    if (planFeature.accessLevel === FeatureAccessLevel.Unlimited) return Infinity;
    if (planFeature.accessLevel !== FeatureAccessLevel.Limited) return Infinity;
    const limitValue = this.getLimitValue(featureId, limitId);
    if (limitValue === void 0) return Infinity;
    if (limitValue === "unlimited") return Infinity;
    const currentUsage = this.getCurrentUsage(featureId, limitId);
    return Math.max(0, limitValue - currentUsage);
  }
  /**
   * Check if a user can perform an action that requires a feature
   * @param featureId Feature ID
   * @param limitId Optional limit ID to check
   * @returns True if the user can perform the action
   */
  canPerformAction(featureId, limitId) {
    if (!this.hasAccess(featureId)) return false;
    if (!limitId) return true;
    return !this.hasReachedLimit(featureId, limitId);
  }
  /**
   * Get the reason why a user can't perform an action
   * @param featureId Feature ID
   * @param limitId Optional limit ID to check
   * @returns Reason or null if the user can perform the action
   */
  getBlockReason(featureId, limitId) {
    const feature = getFeatureById(featureId);
    if (!feature) return "Feature not found";
    if (!this.hasAccess(featureId)) {
      return `Your current plan does not include the ${feature.name} feature.`;
    }
    if (!limitId) return null;
    if (this.hasReachedLimit(featureId, limitId)) {
      const limit = getFeatureLimitById(limitId);
      if (!limit) return "Limit not found";
      const limitValue = this.getLimitValue(featureId, limitId);
      if (limitValue === void 0) return null;
      if (limitValue === "unlimited") return null;
      return `You've reached your limit of ${limitValue} ${limit.unit || ""} for ${feature.name}.`;
    }
    return null;
  }
  /**
   * Get all features available to the user
   * @returns Array of available features
   */
  getAvailableFeatures() {
    return FEATURES.filter((feature) => this.hasAccess(feature.id));
  }
  /**
   * Get all features available to the user by category
   * @returns Record of features by category
   */
  getAvailableFeaturesByCategory() {
    const features = this.getAvailableFeatures();
    const result = {};
    for (const feature of features) {
      if (!result[feature.category]) {
        result[feature.category] = [];
      }
      result[feature.category].push(feature);
    }
    return result;
  }
  /**
   * Get the plan feature configuration
   * @param featureId Feature ID
   * @returns Plan feature configuration or undefined if not found
   */
  getPlanFeature(featureId) {
    if (!this.userData.plan) return void 0;
    return this.userData.plan.features.find((feature) => feature.featureId === featureId);
  }
  /**
   * Create a feature access instance from user data
   * @param userData User data
   * @returns Feature access instance
   */
  static fromUserData(userData) {
    return new FeatureAccess(userData);
  }
}
function createFeatureAccess(userData) {
  const planId = userData.subscription?.planId || userData.role || "free";
  const plan = userData.plan;
  let subscription;
  if (userData.subscription) {
    subscription = {
      planId: userData.subscription.planId || planId,
      startDate: userData.subscription.startDate || /* @__PURE__ */ new Date(),
      endDate: userData.subscription.endDate,
      cancelAtPeriodEnd: userData.subscription.cancelAtPeriodEnd || false,
      status: userData.subscription.status || "active",
      trialEndDate: userData.subscription.trialEndDate
    };
  }
  const usage = [];
  if (userData.usage) {
    for (const feature of FEATURES) {
      if (!feature.limits) continue;
      for (const limit of feature.limits) {
        const usageKey = `${feature.id}_${limit.id}`;
        if (usageKey in userData.usage) {
          const now = /* @__PURE__ */ new Date();
          const period = limit.type === LimitType.Monthly ? `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, "0")}` : void 0;
          usage.push({
            featureId: feature.id,
            limitId: limit.id,
            used: userData.usage[usageKey],
            period,
            lastUpdated: /* @__PURE__ */ new Date()
          });
        }
      }
    }
  }
  const featureUserData = {
    id: userData.id,
    subscription,
    plan,
    usage
  };
  return FeatureAccess.fromUserData(featureUserData);
}

export { createFeatureAccess as c };
//# sourceMappingURL=index13-DOBlGKWb.js.map
