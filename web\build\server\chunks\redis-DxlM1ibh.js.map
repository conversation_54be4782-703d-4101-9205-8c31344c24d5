{"version": 3, "file": "redis-DxlM1ibh.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/redis.js"], "sourcesContent": ["import { Redis } from \"ioredis\";\nconst NODE_ENV = process.env.NODE_ENV || \"development\";\nconst REDIS_HOST = process.env.REDIS_HOST || \"localhost\";\nconst REDIS_PORT = parseInt(process.env.REDIS_PORT || \"6379\", 10);\nconst REDIS_PASSWORD = process.env.REDIS_PASSWORD || null;\nconst REDIS_URL = process.env.REDIS_URL;\nconst REDIS_URL_INTERNAL = process.env.REDIS_URL_INTERNAL;\nconst REDIS_URL_EXTERNAL = process.env.REDIS_URL_EXTERNAL;\nconst LOCAL_TESTING = process.env.LOCAL_TESTING === \"true\";\nfunction getRedisUrl() {\n  console.log(`[redis] NODE_ENV: ${NODE_ENV}`);\n  console.log(`[redis] Available Redis URLs:`);\n  console.log(`[redis] REDIS_URL: ${REDIS_URL ? \"defined\" : \"undefined\"}`);\n  console.log(`[redis] REDIS_URL_INTERNAL: ${REDIS_URL_INTERNAL ? \"defined\" : \"undefined\"}`);\n  console.log(`[redis] REDIS_URL_EXTERNAL: ${REDIS_URL_EXTERNAL ? \"defined\" : \"undefined\"}`);\n  if (LOCAL_TESTING) {\n    const localUrl = `redis://${REDIS_HOST}:${REDIS_PORT}`;\n    console.log(`[redis] LOCAL_TESTING enabled, using ${localUrl}`);\n    return localUrl;\n  }\n  if (NODE_ENV === \"production\") {\n    if (REDIS_URL_INTERNAL) {\n      console.log(`[redis] Using REDIS_URL_INTERNAL in production`);\n      return REDIS_URL_INTERNAL;\n    }\n    if (REDIS_URL) {\n      console.log(`[redis] Using REDIS_URL in production`);\n      return REDIS_URL;\n    }\n  } else {\n    if (REDIS_URL_EXTERNAL) {\n      console.log(`[redis] Using REDIS_URL_EXTERNAL in development`);\n      return REDIS_URL_EXTERNAL;\n    }\n    if (REDIS_URL) {\n      console.log(`[redis] Using REDIS_URL in development`);\n      return REDIS_URL;\n    }\n  }\n  const defaultUrl = `redis://${REDIS_HOST}:${REDIS_PORT}`;\n  console.log(`[redis] No Redis URL provided, using default: ${defaultUrl}`);\n  return defaultUrl;\n}\nfunction getRedisOptions(url) {\n  const isInternalUrl = url.includes(\"red-\") && !url.includes(\"@\");\n  const isExternalUrl = url.includes(\"rediss://\") || url.includes(\"@\");\n  if (isInternalUrl) {\n    console.log(`[redis] Using internal Redis URL format`);\n    if (NODE_ENV !== \"production\") {\n      console.warn(\n        `[redis] ⚠️ Warning: Using internal URL format outside of production environment may cause connection issues`\n      );\n    }\n  } else if (isExternalUrl) {\n    const sanitizedUrl = url.replace(/(:.*@)/, \":****@\");\n    console.log(`[redis] Using external Redis URL format: ${sanitizedUrl}`);\n    if (NODE_ENV === \"production\") {\n      console.warn(\n        `[redis] ⚠️ Warning: Using external URL format in production environment may cause connection issues`\n      );\n    }\n  } else {\n    console.log(`[redis] Using standard Redis URL format`);\n  }\n  const baseOptions = {\n    retryStrategy: (times) => {\n      const delay = Math.min(times * 100, 3e3);\n      console.log(`[redis] Connection attempt ${times} failed. Retrying in ${delay}ms...`);\n      return delay;\n    },\n    maxRetriesPerRequest: 3,\n    enableOfflineQueue: true,\n    connectTimeout: 5e3\n    // 5 second timeout\n  };\n  if (NODE_ENV === \"production\") {\n    return {\n      ...baseOptions,\n      retryStrategy: (times) => {\n        return Math.min(times * 100, 1e4);\n      },\n      // Enable TLS for secure connections (rediss://)\n      tls: url.startsWith(\"rediss://\") ? { rejectUnauthorized: false } : void 0,\n      // For internal Render.com networking, we need to set the password explicitly\n      password: isInternalUrl ? REDIS_PASSWORD : void 0,\n      // Increase connection timeout for cloud environments\n      connectTimeout: 2e4,\n      // Increase retry attempts\n      maxRetriesPerRequest: 5\n    };\n  }\n  return {\n    ...baseOptions,\n    // Add TLS for secure connections (rediss://)\n    tls: url.startsWith(\"rediss://\") ? { rejectUnauthorized: false } : void 0,\n    // Keep reconnecting in development mode to avoid connection issues\n    reconnectOnError: () => true,\n    maxRetriesPerRequest: 5,\n    enableOfflineQueue: true\n  };\n}\nfunction createRedisClient() {\n  const url = getRedisUrl();\n  const options = getRedisOptions(url);\n  console.log(`[redis] Connecting to Redis...`);\n  const client = new Redis(url, options);\n  client.on(\"connect\", () => console.log(\"✅ Redis connected successfully\"));\n  client.on(\"error\", (error) => console.error(`❌ Redis error: ${error}`));\n  client.on(\"reconnecting\", () => console.log(\"⏳ Redis reconnecting...\"));\n  return client;\n}\nlet RedisConnection = null;\ntry {\n  console.log(\"[redis] Creating Redis client...\");\n  RedisConnection = createRedisClient();\n  console.log(`[redis] Redis client created with status: ${RedisConnection.status}`);\n  RedisConnection.on(\"connect\", () => {\n    console.log(\"[redis] ✅ Redis client connected\");\n  });\n  RedisConnection.on(\"ready\", () => {\n    console.log(\"[redis] ✅ Redis client ready\");\n  });\n  RedisConnection.on(\"error\", (error) => {\n    console.error(`[redis] ❌ Redis error: ${error.message}`);\n  });\n  RedisConnection.on(\"close\", () => {\n    console.log(\"[redis] ⚠️ Redis connection closed\");\n  });\n  RedisConnection.on(\"reconnecting\", () => {\n    console.log(\"[redis] ⏳ Redis reconnecting...\");\n  });\n  RedisConnection.on(\"end\", () => {\n    console.log(\"[redis] ⚠️ Redis connection ended\");\n  });\n  RedisConnection.ping().then((result) => {\n    console.log(`[redis] Ping result: ${result}`);\n  }).catch((error) => {\n    console.error(`[redis] Ping failed: ${error.message}`);\n  });\n} catch (error) {\n  console.error(`[redis] ❌ Failed to initialize Redis: ${error}`);\n  console.log(\"[redis] ⚠️ Redis connection failed\");\n}\nasync function getRedisClient() {\n  try {\n    if (RedisConnection) {\n      await RedisConnection.ping();\n      return RedisConnection;\n    }\n    return null;\n  } catch (error) {\n    console.error(`❌ Redis connection error: ${error}`);\n    console.log(\"⚠️ Redis connection failed\");\n    return null;\n  }\n}\nexport {\n  RedisConnection as R,\n  getRedisClient as g\n};\n"], "names": [], "mappings": ";;AACA,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa;AACtD,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,WAAW;AACxD,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,MAAM,EAAE,EAAE,CAAC;AACjE,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,IAAI;AACzD,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,SAAS;AACvC,MAAM,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB;AACzD,MAAM,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB;AACzD,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,KAAK,MAAM;AAC1D,SAAS,WAAW,GAAG;AACvB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC9C,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,6BAA6B,CAAC,CAAC;AAC9C,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,SAAS,GAAG,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC;AAC1E,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,kBAAkB,GAAG,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC;AAC5F,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,kBAAkB,GAAG,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC;AAC5F,EAAE,IAAI,aAAa,EAAE;AACrB,IAAI,MAAM,QAAQ,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AAC1D,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,qCAAqC,EAAE,QAAQ,CAAC,CAAC,CAAC;AACnE,IAAI,OAAO,QAAQ;AACnB;AACA,EAAE,IAAI,QAAQ,KAAK,YAAY,EAAE;AACjC,IAAI,IAAI,kBAAkB,EAAE;AAC5B,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,8CAA8C,CAAC,CAAC;AACnE,MAAM,OAAO,kBAAkB;AAC/B;AACA,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,qCAAqC,CAAC,CAAC;AAC1D,MAAM,OAAO,SAAS;AACtB;AACA,GAAG,MAAM;AACT,IAAI,IAAI,kBAAkB,EAAE;AAC5B,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,+CAA+C,CAAC,CAAC;AACpE,MAAM,OAAO,kBAAkB;AAC/B;AACA,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,sCAAsC,CAAC,CAAC;AAC3D,MAAM,OAAO,SAAS;AACtB;AACA;AACA,EAAE,MAAM,UAAU,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AAC1D,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,8CAA8C,EAAE,UAAU,CAAC,CAAC,CAAC;AAC5E,EAAE,OAAO,UAAU;AACnB;AACA,SAAS,eAAe,CAAC,GAAG,EAAE;AAC9B,EAAE,MAAM,aAAa,GAAG,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC;AAClE,EAAE,MAAM,aAAa,GAAG,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC;AACtE,EAAE,IAAI,aAAa,EAAE;AACrB,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,uCAAuC,CAAC,CAAC;AAC1D,IAAI,IAAI,QAAQ,KAAK,YAAY,EAAE;AACnC,MAAM,OAAO,CAAC,IAAI;AAClB,QAAQ,CAAC,2GAA2G;AACpH,OAAO;AACP;AACA,GAAG,MAAM,IAAI,aAAa,EAAE;AAC5B,IAAI,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC;AACxD,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,yCAAyC,EAAE,YAAY,CAAC,CAAC,CAAC;AAC3E,IAAI,IAAI,QAAQ,KAAK,YAAY,EAAE;AACnC,MAAM,OAAO,CAAC,IAAI;AAClB,QAAQ,CAAC,mGAAmG;AAC5G,OAAO;AACP;AACA,GAAG,MAAM;AACT,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,uCAAuC,CAAC,CAAC;AAC1D;AACA,EAAE,MAAM,WAAW,GAAG;AACtB,IAAI,aAAa,EAAE,CAAC,KAAK,KAAK;AAC9B,MAAM,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,EAAE,GAAG,CAAC;AAC9C,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AAC1F,MAAM,OAAO,KAAK;AAClB,KAAK;AACL,IAAI,oBAAoB,EAAE,CAAC;AAC3B,IAAI,kBAAkB,EAAE,IAAI;AAC5B,IAAI,cAAc,EAAE;AACpB;AACA,GAAG;AACH,EAAE,IAAI,QAAQ,KAAK,YAAY,EAAE;AACjC,IAAI,OAAO;AACX,MAAM,GAAG,WAAW;AACpB,MAAM,aAAa,EAAE,CAAC,KAAK,KAAK;AAChC,QAAQ,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,EAAE,GAAG,CAAC;AACzC,OAAO;AACP;AACA,MAAM,GAAG,EAAE,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,GAAG,EAAE,kBAAkB,EAAE,KAAK,EAAE,GAAG,MAAM;AAC/E;AACA,MAAM,QAAQ,EAAE,aAAa,GAAG,cAAc,GAAG,MAAM;AACvD;AACA,MAAM,cAAc,EAAE,GAAG;AACzB;AACA,MAAM,oBAAoB,EAAE;AAC5B,KAAK;AACL;AACA,EAAE,OAAO;AACT,IAAI,GAAG,WAAW;AAClB;AACA,IAAI,GAAG,EAAE,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,GAAG,EAAE,kBAAkB,EAAE,KAAK,EAAE,GAAG,MAAM;AAC7E;AACA,IAAI,gBAAgB,EAAE,MAAM,IAAI;AAChC,IAAI,oBAAoB,EAAE,CAAC;AAC3B,IAAI,kBAAkB,EAAE;AACxB,GAAG;AACH;AACA,SAAS,iBAAiB,GAAG;AAC7B,EAAE,MAAM,GAAG,GAAG,WAAW,EAAE;AAC3B,EAAE,MAAM,OAAO,GAAG,eAAe,CAAC,GAAG,CAAC;AACtC,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,8BAA8B,CAAC,CAAC;AAC/C,EAAE,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC;AACxC,EAAE,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;AAC3E,EAAE,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AACzE,EAAE,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,MAAM,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;AACzE,EAAE,OAAO,MAAM;AACf;AACG,IAAC,eAAe,GAAG;AACtB,IAAI;AACJ,EAAE,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC;AACjD,EAAE,eAAe,GAAG,iBAAiB,EAAE;AACvC,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,0CAA0C,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;AACpF,EAAE,eAAe,CAAC,EAAE,CAAC,SAAS,EAAE,MAAM;AACtC,IAAI,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC;AACnD,GAAG,CAAC;AACJ,EAAE,eAAe,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM;AACpC,IAAI,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC;AAC/C,GAAG,CAAC;AACJ,EAAE,eAAe,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,KAAK;AACzC,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AAC5D,GAAG,CAAC;AACJ,EAAE,eAAe,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM;AACpC,IAAI,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC;AACrD,GAAG,CAAC;AACJ,EAAE,eAAe,CAAC,EAAE,CAAC,cAAc,EAAE,MAAM;AAC3C,IAAI,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC;AAClD,GAAG,CAAC;AACJ,EAAE,eAAe,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM;AAClC,IAAI,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC;AACpD,GAAG,CAAC;AACJ,EAAE,eAAe,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK;AAC1C,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC,CAAC;AACjD,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK;AACtB,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,qBAAqB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AAC1D,GAAG,CAAC;AACJ,CAAC,CAAC,OAAO,KAAK,EAAE;AAChB,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjE,EAAE,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC;AACnD;AACA,eAAe,cAAc,GAAG;AAChC,EAAE,IAAI;AACN,IAAI,IAAI,eAAe,EAAE;AACzB,MAAM,MAAM,eAAe,CAAC,IAAI,EAAE;AAClC,MAAM,OAAO,eAAe;AAC5B;AACA,IAAI,OAAO,IAAI;AACf,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC,CAAC;AACvD,IAAI,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC;AAC7C,IAAI,OAAO,IAAI;AACf;AACA;;;;"}