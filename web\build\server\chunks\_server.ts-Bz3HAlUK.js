async function GET({ url }) {
  const host = process.env.NODE_ENV === "production" ? "hirli.com" : url.host;
  const protocol = process.env.NODE_ENV === "production" ? "https" : url.protocol.slice(0, -1);
  const domain = `${protocol}://${host}`;
  if (process.env.NODE_ENV !== "production") {
    return new Response(
      `# Non-production environment - disallow all
User-agent: *
Disallow: /
`,
      {
        headers: {
          "Content-Type": "text/plain",
          "Cache-Control": "max-age=0, s-maxage=3600"
        }
      }
    );
  }
  const robotsTxt = `# https://www.robotstxt.org/robotstxt.html
User-agent: *
Allow: /

# Disallow admin and API routes
Disallow: /admin/
Disallow: /api/
Disallow: /dashboard/
Disallow: /graphql/
Disallow: /graphiql/

# Disallow authentication routes
Disallow: /auth/
Disallow: /login/
Disallow: /signup/
Disallow: /reset-password/
Disallow: /verify-email/
Disallow: /forgot-password/

# Disallow user-specific routes
Disallow: /profile/
Disallow: /settings/
Disallow: /account/
Disallow: /user/
Disallow: /notifications/

# Disallow temporary and test routes
Disallow: /test/
Disallow: /demo/
Disallow: /beta/
Disallow: /dev/
Disallow: /staging/

# Disallow duplicate content
Disallow: /*?*
Disallow: /*&*
Disallow: /*.pdf$
Disallow: /*.doc$
Disallow: /*.docx$
Disallow: /*.xls$
Disallow: /*.xlsx$
Disallow: /*.ppt$
Disallow: /*.pptx$

# Block AI and crawler bots
User-agent: AI2Bot
User-agent: Ai2Bot-Dolma
User-agent: aiHitBot
User-agent: Amazonbot
User-agent: anthropic-ai
User-agent: Anthropic-AI
User-agent: Applebot
User-agent: Applebot-Extended
User-agent: Bard
User-agent: BardBot
User-agent: Baiduspider
User-agent: Bingbot
User-agent: Brightbot 1.0
User-agent: Bytespider
User-agent: CCBot
User-agent: ChatGPT-User
User-agent: Claude-Web
User-agent: ClaudeBot
User-agent: ClaudeBot-Preview
User-agent: cohere-ai
User-agent: cohere-training-data-crawler
User-agent: Cotoyogi
User-agent: Crawlspace
User-agent: DataForSeoBot
User-agent: Diffbot
User-agent: DuckAssistBot
User-agent: FacebookBot
User-agent: Factset_spyderbot
User-agent: FirecrawlAgent
User-agent: FriendlyCrawler
User-agent: Google-Extended
User-agent: GoogleOther
User-agent: GoogleOther-Image
User-agent: GoogleOther-Video
User-agent: GPTBot
User-agent: iaskspider/2.0
User-agent: ICC-Crawler
User-agent: ImagesiftBot
User-agent: img2dataset
User-agent: imgproxy
User-agent: ISSCyberRiskCrawler
User-agent: Kangaroo Bot
User-agent: llama-cpp
User-agent: llama-2
User-agent: llama-3
User-agent: LLaMABot
User-agent: magpie-crawler
User-agent: Mistral-AI
User-agent: MistralBot
User-agent: meta-externalagent
User-agent: Meta-ExternalAgent
User-agent: meta-externalfetcher
User-agent: Meta-ExternalFetcher
User-agent: NovaAct
User-agent: OAI-SearchBot
User-agent: omgili
User-agent: omgilibot
User-agent: Operator
User-agent: PanguBot
User-agent: Perplexity-User
User-agent: PerplexityBot
User-agent: PetalBot
User-agent: Poe
User-agent: PoeBot
User-agent: Qwant
User-agent: Scrapy
User-agent: SemrushBot-OCOB
User-agent: SemrushBot-SWA
User-agent: Sidetrade indexer bot
User-agent: TikTokSpider
User-agent: Timpibot
User-agent: VelenPublicWebCrawler
User-agent: Webzio-Extended
User-agent: YouBot
User-agent: YandexBot
User-agent: ZoominfoBot
Disallow: /

# Sitemap
Sitemap: ${domain}/sitemap.xml
`;
  return new Response(robotsTxt, {
    headers: {
      "Content-Type": "text/plain",
      "Cache-Control": "max-age=0, s-maxage=3600"
    }
  });
}

export { GET };
//# sourceMappingURL=_server.ts-Bz3HAlUK.js.map
