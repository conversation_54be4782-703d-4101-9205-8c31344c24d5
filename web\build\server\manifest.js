const manifest = (() => {
function __memo(fn) {
	let value;
	return () => value ??= (value = fn());
}

return {
	appDir: "_app",
	appPath: "_app",
	assets: new Set(["assets/favicon/favicon-128x128.png","assets/favicon/favicon-16x16.png","assets/favicon/favicon-192x192.png","assets/favicon/favicon-256x256.png","assets/favicon/favicon-32x32.png","assets/favicon/favicon-48x48.png","assets/favicon/favicon-512x512.png","assets/favicon/favicon-64x64.png","assets/favicon/favicon.ico","assets/favicon/manifest.json","assets/fonts/Inter-Italic-VariableFont_opsz,wght.ttf","assets/fonts/Inter-VariableFont_opsz,wght.ttf","assets/fonts/Roboto-Italic-VariableFont_wdth,wght.ttf","assets/fonts/Roboto-VariableFont_wdth,wght.ttf","assets/svg/google.svg","assets/svg/linkedin.svg","assets/webp/accent.webp","assets/webp/classic.webp","assets/webp/minimalist.webp","assets/webp/modern.webp","debug-tools.html","site.webmanifest","studio/favicon.ico","studio/index.html","studio/static/.gitkeep","studio/static/apple-touch-icon.png","studio/static/browser-brmmQ1t9.js","studio/static/favicon-192.png","studio/static/favicon-512.png","studio/static/favicon-96.png","studio/static/favicon.ico","studio/static/favicon.svg","studio/static/index-D8KJVLq7.js","studio/static/index2-BMjMOPK3.js","studio/static/index3-BNY2sVNS.js","studio/static/manifest.webmanifest","studio/static/refractor-fojG8wt3.js","studio/static/resources-Ck3zQgZu.js","studio/static/resources-DSajARg_.js","studio/static/resources2-D6hvydvy.js","studio/static/resources3-CZHseQDq.js","studio/static/resources4-1DGMzDYL.js","studio/static/resources5-DsYbJdPK.js","studio/static/resources6-D4aEngfB.js","studio/static/sanity-CyfLjDop.js","studio/static/sanity-DV0NwVOn.js","studio/static/sanity-X1s7OzRS.js","studio/static/SanityVision-B_653MrW.js","studio/static/stegaEncodeSourceMap-B9cQufyk.js","studio/static/ViteDevServerStopped-B_NO0zRS.js","studio/vendor/react/index-ClGR8UKT.mjs","studio/vendor/react/jsx-dev-runtime-CmvbC6Mp.mjs","studio/vendor/react/jsx-runtime-MPXMuslR.mjs","studio/vendor/react/package.json-_hTv_IDL.mjs","studio/vendor/react-dom/client-BjvhQLWp.mjs","studio/vendor/react-dom/index-BtnwqIhl.mjs","studio/vendor/react-dom/package.json-BEz7F3rk.mjs","studio/vendor/react-dom/server-HH7ZgBma.mjs","studio/vendor/react-dom/server.browser-gWQ0XzMy.mjs","studio/vendor/styled-components/index-BP3W3NaG.mjs","studio/vendor/styled-components/package.json-D1iMoum4.mjs","uploads/cover-letters/9cac1c6a-bfdd-46b0-bfbc-5eeaa21799eb.docx","uploads/references/3ab3e0e5-b0f2-4ec2-8a9c-3dfea5a70349.doc","uploads/resumes/f25f4797-2777-4dbc-8810-870271104d97.pdf","uploads/resumes/Resume.docx","uploads/sample-resumes/deedy-resume-sample.png","uploads/sample-resumes/sample-resume-1.docx","uploads/sample-resumes/sample-resume-2.docx","uploads/sample-resumes/sample-resume-3.docx","uploads/sample-resumes/software-engineer-github-2.pdf","service-worker.js"]),
	mimeTypes: {".png":"image/png",".json":"application/json",".ttf":"font/ttf",".svg":"image/svg+xml",".webp":"image/webp",".html":"text/html",".webmanifest":"application/manifest+json",".js":"text/javascript",".mjs":"text/javascript",".doc":"application/msword",".pdf":"application/pdf"},
	_: {
		client: {start:"_app/immutable/entry/start.D8Rm5Y74.js",app:"_app/immutable/entry/app.DQzOGG6V.js",imports:["_app/immutable/entry/start.D8Rm5Y74.js","_app/immutable/chunks/DQC1iFs0.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/entry/app.DQzOGG6V.js","_app/immutable/chunks/C1FmrZbK.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/Btcx8l8F.js"],stylesheets:[],fonts:[],uses_env_dynamic_public:false},
		nodes: [
			__memo(() => import('./chunks/0-kGWgWifT.js')),
			__memo(() => import('./chunks/1-DsY9wNIS.js')),
			__memo(() => import('./chunks/2-4GPEINAh.js')),
			__memo(() => import('./chunks/3-CBSsoGyr.js')),
			__memo(() => import('./chunks/4-ZdhgD1st.js')),
			__memo(() => import('./chunks/5-C1Gd1L9v.js')),
			__memo(() => import('./chunks/6-Cis8DPNl.js')),
			__memo(() => import('./chunks/7-CCYC6ma3.js')),
			__memo(() => import('./chunks/8-8OpSxZTs.js')),
			__memo(() => import('./chunks/9-CF6W8WOg.js')),
			__memo(() => import('./chunks/10-CZKZ4EZ4.js')),
			__memo(() => import('./chunks/11-7lfkZ-M3.js')),
			__memo(() => import('./chunks/12-6hE8cZ7L.js')),
			__memo(() => import('./chunks/13-TAXqqpK1.js')),
			__memo(() => import('./chunks/14-k2dB-o0K.js')),
			__memo(() => import('./chunks/15-C99sUmZ5.js')),
			__memo(() => import('./chunks/16-DVGbb10Y.js')),
			__memo(() => import('./chunks/17-OZLKWQbV.js')),
			__memo(() => import('./chunks/18-xVqZHa_c.js')),
			__memo(() => import('./chunks/19-CUur70Rq.js')),
			__memo(() => import('./chunks/20-c2BXoUAn.js')),
			__memo(() => import('./chunks/21-Bs7wvwZX.js')),
			__memo(() => import('./chunks/22-8YesUk8X.js')),
			__memo(() => import('./chunks/23-mB6RStkm.js')),
			__memo(() => import('./chunks/24-D9tQ0Nk4.js')),
			__memo(() => import('./chunks/25-bWFoxyTD.js')),
			__memo(() => import('./chunks/26-BgtZ1y-4.js')),
			__memo(() => import('./chunks/27-M51qyC9u.js').then(function (n) { return n._; })),
			__memo(() => import('./chunks/28-5NGcJCRa.js')),
			__memo(() => import('./chunks/29-r2xdlT6V.js')),
			__memo(() => import('./chunks/30-BZDhI-LU.js')),
			__memo(() => import('./chunks/31-BRL0K-8c.js')),
			__memo(() => import('./chunks/32-DY6TLC5k.js')),
			__memo(() => import('./chunks/33-C4iqlcu4.js')),
			__memo(() => import('./chunks/34-DTHclPiM.js')),
			__memo(() => import('./chunks/35-VHps65IS.js')),
			__memo(() => import('./chunks/36-2lEqPyIm.js')),
			__memo(() => import('./chunks/37-DxatDyLu.js')),
			__memo(() => import('./chunks/38-DDWoN24j.js')),
			__memo(() => import('./chunks/39-qfjmztjH.js')),
			__memo(() => import('./chunks/40-BgBZtU33.js')),
			__memo(() => import('./chunks/41-DM-kuIt8.js')),
			__memo(() => import('./chunks/42-Djz0DhXY.js')),
			__memo(() => import('./chunks/43-S66f6XRw.js')),
			__memo(() => import('./chunks/44-4ol2ZD7K.js')),
			__memo(() => import('./chunks/45-DuRIDJ0L.js')),
			__memo(() => import('./chunks/46-CgInGauL.js')),
			__memo(() => import('./chunks/47-C6Z7TBC7.js')),
			__memo(() => import('./chunks/48-By2bQjEn.js')),
			__memo(() => import('./chunks/49-Dz5fL03t.js')),
			__memo(() => import('./chunks/50-DOQ3gAF_.js')),
			__memo(() => import('./chunks/51-C8QNFg8_.js')),
			__memo(() => import('./chunks/52-BiZ3Sk6P.js')),
			__memo(() => import('./chunks/53-Bvc2t0D7.js')),
			__memo(() => import('./chunks/54-Cjz9HFD3.js')),
			__memo(() => import('./chunks/55-D50QIlk2.js')),
			__memo(() => import('./chunks/56-Bb3avwuh.js')),
			__memo(() => import('./chunks/57-B9yCf2GU.js')),
			__memo(() => import('./chunks/58-CSizWw_Q.js')),
			__memo(() => import('./chunks/59-ZqMXCpd1.js')),
			__memo(() => import('./chunks/60-D47FI1ss.js')),
			__memo(() => import('./chunks/61-5VRg0PVz.js')),
			__memo(() => import('./chunks/62-BxPq2pK1.js')),
			__memo(() => import('./chunks/63-DLIzY7VZ.js')),
			__memo(() => import('./chunks/64-BfkmbsfV.js')),
			__memo(() => import('./chunks/65-T0kmXUAt.js')),
			__memo(() => import('./chunks/66-nY4DUU1W.js')),
			__memo(() => import('./chunks/67-CLFVCJnx.js')),
			__memo(() => import('./chunks/68-EwZ6lv-W.js')),
			__memo(() => import('./chunks/69-Ccx0OHdP.js')),
			__memo(() => import('./chunks/70-B5TsXxRh.js')),
			__memo(() => import('./chunks/71-DYoWkMU8.js').then(function (n) { return n._; })),
			__memo(() => import('./chunks/72-DoxrVdiH.js')),
			__memo(() => import('./chunks/73-bAoxs1co.js')),
			__memo(() => import('./chunks/74-BP16hw6O.js')),
			__memo(() => import('./chunks/75-8_HzH9Rn.js')),
			__memo(() => import('./chunks/76-B5R18jaq.js')),
			__memo(() => import('./chunks/77-Bx7qxt7B.js')),
			__memo(() => import('./chunks/78-Dguui1K1.js')),
			__memo(() => import('./chunks/79-CNvrcw_u.js')),
			__memo(() => import('./chunks/80-BCJBXhfp.js')),
			__memo(() => import('./chunks/81-CXpBN9MF.js')),
			__memo(() => import('./chunks/82-JRCbJCb6.js')),
			__memo(() => import('./chunks/83-CdQWDpxk.js')),
			__memo(() => import('./chunks/84-CKKY5LGp.js')),
			__memo(() => import('./chunks/85-BBWk_oP9.js')),
			__memo(() => import('./chunks/86-BTH3Ndhx.js')),
			__memo(() => import('./chunks/87-DBywf-S0.js')),
			__memo(() => import('./chunks/88-kbNRpYBr.js')),
			__memo(() => import('./chunks/89-dXaIt_oF.js')),
			__memo(() => import('./chunks/90-DpQ3VB9h.js')),
			__memo(() => import('./chunks/91-FarJXFni.js')),
			__memo(() => import('./chunks/92-BUWjUDkn.js')),
			__memo(() => import('./chunks/93-2Sio7TBd.js')),
			__memo(() => import('./chunks/94-B9wRvK5J.js')),
			__memo(() => import('./chunks/95-BV23XqMY.js')),
			__memo(() => import('./chunks/96-BDxH5hO9.js'))
		],
		routes: [
			{
				id: "/",
				pattern: /^\/$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 9 },
				endpoint: __memo(() => import('./chunks/_server.ts-DL927elr.js'))
			},
			{
				id: "/about",
				pattern: /^\/about\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 10 },
				endpoint: null
			},
			{
				id: "/admin/features",
				pattern: /^\/admin\/features\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 11 },
				endpoint: null
			},
			{
				id: "/api",
				pattern: /^\/api\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DVbsOGgv.js'))
			},
			{
				id: "/api/admin/check-admin",
				pattern: /^\/api\/admin\/check-admin\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Bjb0M6Bd.js'))
			},
			{
				id: "/api/admin/feature-usage",
				pattern: /^\/api\/admin\/feature-usage\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BrbwCxnW.js'))
			},
			{
				id: "/api/admin/feature-usage/check",
				pattern: /^\/api\/admin\/feature-usage\/check\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DXxfw3F3.js'))
			},
			{
				id: "/api/admin/feature-usage/export",
				pattern: /^\/api\/admin\/feature-usage\/export\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DhPWz-HK.js'))
			},
			{
				id: "/api/admin/feature-usage/summary",
				pattern: /^\/api\/admin\/feature-usage\/summary\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-MvL4lRnF.js'))
			},
			{
				id: "/api/admin/features",
				pattern: /^\/api\/admin\/features\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-C5jVFWaC.js'))
			},
			{
				id: "/api/admin/features/seed-all",
				pattern: /^\/api\/admin\/features\/seed-all\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-r3--uykD.js'))
			},
			{
				id: "/api/admin/features/seed-analysis",
				pattern: /^\/api\/admin\/features\/seed-analysis\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-D5jxFWsQ.js'))
			},
			{
				id: "/api/admin/features/seed-service",
				pattern: /^\/api\/admin\/features\/seed-service\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-B3v5rG5H.js'))
			},
			{
				id: "/api/admin/features/sync-all",
				pattern: /^\/api\/admin\/features\/sync-all\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-C_818oOk.js'))
			},
			{
				id: "/api/admin/features/sync",
				pattern: /^\/api\/admin\/features\/sync\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BBuPhaDq.js'))
			},
			{
				id: "/api/admin/initialize-features",
				pattern: /^\/api\/admin\/initialize-features\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-5B3iB2Qj.js'))
			},
			{
				id: "/api/admin/make-admin",
				pattern: /^\/api\/admin\/make-admin\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-J5Q1LjYg.js'))
			},
			{
				id: "/api/admin/mock-users",
				pattern: /^\/api\/admin\/mock-users\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-i-G1fZEZ.js'))
			},
			{
				id: "/api/admin/plans",
				pattern: /^\/api\/admin\/plans\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CIGcmnOm.js'))
			},
			{
				id: "/api/admin/plans/initialize",
				pattern: /^\/api\/admin\/plans\/initialize\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DFs0Bw2h.js'))
			},
			{
				id: "/api/admin/plans/load-from-stripe",
				pattern: /^\/api\/admin\/plans\/load-from-stripe\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-0MXR2gQQ.js'))
			},
			{
				id: "/api/admin/plans/sync-stripe",
				pattern: /^\/api\/admin\/plans\/sync-stripe\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BKK0TI8o.js'))
			},
			{
				id: "/api/admin/seed-features",
				pattern: /^\/api\/admin\/seed-features\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CKTtqTKF.js'))
			},
			{
				id: "/api/admin/users",
				pattern: /^\/api\/admin\/users\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-rOOwIfUK.js'))
			},
			{
				id: "/api/admin/users/[userId]",
				pattern: /^\/api\/admin\/users\/([^/]+?)\/?$/,
				params: [{"name":"userId","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Co8NMMfI.js'))
			},
			{
				id: "/api/admin/users/[userId]/plan",
				pattern: /^\/api\/admin\/users\/([^/]+?)\/plan\/?$/,
				params: [{"name":"userId","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BFBGDKLn.js'))
			},
			{
				id: "/api/ai/ats",
				pattern: /^\/api\/ai\/ats\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DuMI1cN3.js'))
			},
			{
				id: "/api/ai/ats/analyze/[resumeId]",
				pattern: /^\/api\/ai\/ats\/analyze\/([^/]+?)\/?$/,
				params: [{"name":"resumeId","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DhnzsR3Y.js'))
			},
			{
				id: "/api/ai/ats/job-match",
				pattern: /^\/api\/ai\/ats\/job-match\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Dwlv5rUF.js'))
			},
			{
				id: "/api/ai/ats/job",
				pattern: /^\/api\/ai\/ats\/job\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BE_-2IeC.js'))
			},
			{
				id: "/api/ai/ats/text",
				pattern: /^\/api\/ai\/ats\/text\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DlS9-NLk.js'))
			},
			{
				id: "/api/ai/interview",
				pattern: /^\/api\/ai\/interview\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Qhf-_Fxx.js'))
			},
			{
				id: "/api/ai/interview/sessions",
				pattern: /^\/api\/ai\/interview\/sessions\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CV1WqGQD.js'))
			},
			{
				id: "/api/ai/interview/[id]",
				pattern: /^\/api\/ai\/interview\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BWnhlxjN.js'))
			},
			{
				id: "/api/ai/job-match",
				pattern: /^\/api\/ai\/job-match\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-D_geXbqr.js'))
			},
			{
				id: "/api/ai/jobs/match-details",
				pattern: /^\/api\/ai\/jobs\/match-details\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Bg6qVWPD.js'))
			},
			{
				id: "/api/ai/resume/suggestions",
				pattern: /^\/api\/ai\/resume\/suggestions\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CAy-I_xk.js'))
			},
			{
				id: "/api/ai/resume/suggestions/[id]/apply",
				pattern: /^\/api\/ai\/resume\/suggestions\/([^/]+?)\/apply\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-B7vgtZAI.js'))
			},
			{
				id: "/api/applications",
				pattern: /^\/api\/applications\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-C9VrYwuF.js'))
			},
			{
				id: "/api/applications/add",
				pattern: /^\/api\/applications\/add\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Bd1DUmVF.js'))
			},
			{
				id: "/api/applications/check",
				pattern: /^\/api\/applications\/check\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DptnCUXv.js'))
			},
			{
				id: "/api/applications/status",
				pattern: /^\/api\/applications\/status\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-kF3215va.js'))
			},
			{
				id: "/api/applications/[applicationId]",
				pattern: /^\/api\/applications\/([^/]+?)\/?$/,
				params: [{"name":"applicationId","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CrkGR-zO.js'))
			},
			{
				id: "/api/applications/[applicationId]/interviews",
				pattern: /^\/api\/applications\/([^/]+?)\/interviews\/?$/,
				params: [{"name":"applicationId","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DxB4lFa3.js'))
			},
			{
				id: "/api/applications/[applicationId]/interviews/[interviewId]",
				pattern: /^\/api\/applications\/([^/]+?)\/interviews\/([^/]+?)\/?$/,
				params: [{"name":"applicationId","optional":false,"rest":false,"chained":false},{"name":"interviewId","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CH5TsnLp.js'))
			},
			{
				id: "/api/applications/[applicationId]/interviews/[interviewId]/questions",
				pattern: /^\/api\/applications\/([^/]+?)\/interviews\/([^/]+?)\/questions\/?$/,
				params: [{"name":"applicationId","optional":false,"rest":false,"chained":false},{"name":"interviewId","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CINsiiXA.js'))
			},
			{
				id: "/api/auth/check-session",
				pattern: /^\/api\/auth\/check-session\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CRnpi01C.js'))
			},
			{
				id: "/api/auth/forgot-password",
				pattern: /^\/api\/auth\/forgot-password\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Btkq1kQh.js'))
			},
			{
				id: "/api/auth/google",
				pattern: /^\/api\/auth\/google\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Dqr3z5dZ.js'))
			},
			{
				id: "/api/auth/linkedin",
				pattern: /^\/api\/auth\/linkedin\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BSSNWPVT.js'))
			},
			{
				id: "/api/auth/login",
				pattern: /^\/api\/auth\/login\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-C8sLd6eZ.js'))
			},
			{
				id: "/api/auth/logout",
				pattern: /^\/api\/auth\/logout\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DiHCyv8d.js'))
			},
			{
				id: "/api/auth/refresh-session",
				pattern: /^\/api\/auth\/refresh-session\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CVFicSQ6.js'))
			},
			{
				id: "/api/auth/resend-verification",
				pattern: /^\/api\/auth\/resend-verification\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CFU7NsUV.js'))
			},
			{
				id: "/api/auth/reset-password",
				pattern: /^\/api\/auth\/reset-password\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-awDSlQgK.js'))
			},
			{
				id: "/api/auth/signup",
				pattern: /^\/api\/auth\/signup\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-K4GpADO4.js'))
			},
			{
				id: "/api/auth/verify-token",
				pattern: /^\/api\/auth\/verify-token\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-TyLb9xN8.js'))
			},
			{
				id: "/api/auth/verify",
				pattern: /^\/api\/auth\/verify\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-7qBrzJRX.js'))
			},
			{
				id: "/api/automation/runs",
				pattern: /^\/api\/automation\/runs\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-B_W2e21p.js'))
			},
			{
				id: "/api/automation/runs/[id]",
				pattern: /^\/api\/automation\/runs\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-zWKEIwip.js'))
			},
			{
				id: "/api/automation/runs/[id]/jobs",
				pattern: /^\/api\/automation\/runs\/([^/]+?)\/jobs\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BNH-Tl-h.js'))
			},
			{
				id: "/api/automation/runs/[id]/settings",
				pattern: /^\/api\/automation\/runs\/([^/]+?)\/settings\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BXO_XrWN.js'))
			},
			{
				id: "/api/automation/runs/[id]/stop",
				pattern: /^\/api\/automation\/runs\/([^/]+?)\/stop\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-C6NcCdLF.js'))
			},
			{
				id: "/api/automation/ws",
				pattern: /^\/api\/automation\/ws\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-HKvmIiny.js'))
			},
			{
				id: "/api/billing/cancel-subscription",
				pattern: /^\/api\/billing\/cancel-subscription\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-ZA4BnfcS.js'))
			},
			{
				id: "/api/billing/create-checkout-session",
				pattern: /^\/api\/billing\/create-checkout-session\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-k9kHjeQ-.js'))
			},
			{
				id: "/api/billing/create-portal-session",
				pattern: /^\/api\/billing\/create-portal-session\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BLm9eUkV.js'))
			},
			{
				id: "/api/billing/create-setup-intent",
				pattern: /^\/api\/billing\/create-setup-intent\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-W4pMa2At.js'))
			},
			{
				id: "/api/billing/data",
				pattern: /^\/api\/billing\/data\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DdgyAWMM.js'))
			},
			{
				id: "/api/billing/delete-payment-method",
				pattern: /^\/api\/billing\/delete-payment-method\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Xj6vJX4v.js'))
			},
			{
				id: "/api/billing/get-payment-methods",
				pattern: /^\/api\/billing\/get-payment-methods\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BT7RCUb-.js'))
			},
			{
				id: "/api/billing/payment-methods",
				pattern: /^\/api\/billing\/payment-methods\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-vjElHMno.js'))
			},
			{
				id: "/api/billing/resume-subscription",
				pattern: /^\/api\/billing\/resume-subscription\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-C3q5j8iz.js'))
			},
			{
				id: "/api/billing/set-default-payment-method",
				pattern: /^\/api\/billing\/set-default-payment-method\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-B3Gq25tw.js'))
			},
			{
				id: "/api/checkout",
				pattern: /^\/api\/checkout\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-D03-c8fC.js'))
			},
			{
				id: "/api/companies",
				pattern: /^\/api\/companies\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-B4Fsquxo.js'))
			},
			{
				id: "/api/companies/featured",
				pattern: /^\/api\/companies\/featured\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DcB1sQJq.js'))
			},
			{
				id: "/api/documents",
				pattern: /^\/api\/documents\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CatcHFUl.js'))
			},
			{
				id: "/api/documents/[id]",
				pattern: /^\/api\/documents\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DTma_jhC.js'))
			},
			{
				id: "/api/documents/[id]/parse",
				pattern: /^\/api\/documents\/([^/]+?)\/parse\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DKRk11xn.js'))
			},
			{
				id: "/api/documents/[id]/view",
				pattern: /^\/api\/documents\/([^/]+?)\/view\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-bGc5-k6j.js'))
			},
			{
				id: "/api/document/upload",
				pattern: /^\/api\/document\/upload\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DfRZBZ1W.js'))
			},
			{
				id: "/api/document/[id]",
				pattern: /^\/api\/document\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-zFt-jXvS.js'))
			},
			{
				id: "/api/email/analytics",
				pattern: /^\/api\/email\/analytics\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BiJFM7SY.js'))
			},
			{
				id: "/api/email/analytics/check",
				pattern: /^\/api\/email\/analytics\/check\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BBXYcWpa.js'))
			},
			{
				id: "/api/email/analytics/events",
				pattern: /^\/api\/email\/analytics\/events\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BKkID-ND.js'))
			},
			{
				id: "/api/email/analytics/export",
				pattern: /^\/api\/email\/analytics\/export\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DLjPh16S.js'))
			},
			{
				id: "/api/email/analytics/stats",
				pattern: /^\/api\/email\/analytics\/stats\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BGakHp2j.js'))
			},
			{
				id: "/api/email/audiences",
				pattern: /^\/api\/email\/audiences\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CufoRDlF.js'))
			},
			{
				id: "/api/email/audiences/contacts",
				pattern: /^\/api\/email\/audiences\/contacts\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-PqF92J5B.js'))
			},
			{
				id: "/api/email/audiences/contacts/import",
				pattern: /^\/api\/email\/audiences\/contacts\/import\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-C6VFKDdf.js'))
			},
			{
				id: "/api/email/audiences/[id]",
				pattern: /^\/api\/email\/audiences\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BirfmjkE.js'))
			},
			{
				id: "/api/email/audiences/[id]/contacts",
				pattern: /^\/api\/email\/audiences\/([^/]+?)\/contacts\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-nBEiJJY3.js'))
			},
			{
				id: "/api/email/audiences/[id]/import",
				pattern: /^\/api\/email\/audiences\/([^/]+?)\/import\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-kuI6RI4d.js'))
			},
			{
				id: "/api/email/broadcasts",
				pattern: /^\/api\/email\/broadcasts\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-oIHq0t2n.js'))
			},
			{
				id: "/api/email/broadcasts/cancel",
				pattern: /^\/api\/email\/broadcasts\/cancel\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CosdxYdJ.js'))
			},
			{
				id: "/api/email/clear-failed",
				pattern: /^\/api\/email\/clear-failed\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-X-etYvj1.js'))
			},
			{
				id: "/api/email/config",
				pattern: /^\/api\/email\/config\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CWu15XzX.js'))
			},
			{
				id: "/api/email/process-queue",
				pattern: /^\/api\/email\/process-queue\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BIoYONNl.js'))
			},
			{
				id: "/api/email/queue-status",
				pattern: /^\/api\/email\/queue-status\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-B1niV8hL.js'))
			},
			{
				id: "/api/email/queue",
				pattern: /^\/api\/email\/queue\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BRtyy4QU.js'))
			},
			{
				id: "/api/email/retry-failed",
				pattern: /^\/api\/email\/retry-failed\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-PDPdnKr9.js'))
			},
			{
				id: "/api/email/status",
				pattern: /^\/api\/email\/status\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-hz9j4dV7.js'))
			},
			{
				id: "/api/email/templates/list",
				pattern: /^\/api\/email\/templates\/list\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Ce7eknxL.js'))
			},
			{
				id: "/api/email/webhook",
				pattern: /^\/api\/email\/webhook\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CGWZs9Rw.js'))
			},
			{
				id: "/api/email/webhook/setup",
				pattern: /^\/api\/email\/webhook\/setup\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 12 },
				endpoint: null
			},
			{
				id: "/api/email/worker",
				pattern: /^\/api\/email\/worker\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BTNQgkMt.js'))
			},
			{
				id: "/api/email/worker/metrics",
				pattern: /^\/api\/email\/worker\/metrics\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CyW2704f.js'))
			},
			{
				id: "/api/feature-access",
				pattern: /^\/api\/feature-access\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CrAo61ZW.js'))
			},
			{
				id: "/api/feature-check",
				pattern: /^\/api\/feature-check\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-cNLjQKyK.js'))
			},
			{
				id: "/api/feature-usage",
				pattern: /^\/api\/feature-usage\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CZ3g51-2.js'))
			},
			{
				id: "/api/feature-usage/reset",
				pattern: /^\/api\/feature-usage\/reset\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-556S24fi.js'))
			},
			{
				id: "/api/feature-usage/with-plan-limits",
				pattern: /^\/api\/feature-usage\/with-plan-limits\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-C3n8swH4.js'))
			},
			{
				id: "/api/graphql",
				pattern: /^\/api\/graphql\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-D8WyI8Kg.js'))
			},
			{
				id: "/api/health",
				pattern: /^\/api\/health\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DArZ7DvM.js'))
			},
			{
				id: "/api/health/collector",
				pattern: /^\/api\/health\/collector\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CJ4g0S2t.js'))
			},
			{
				id: "/api/health/report",
				pattern: /^\/api\/health\/report\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-i4wXduPG.js'))
			},
			{
				id: "/api/health/services",
				pattern: /^\/api\/health\/services\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-B1x7vL7y.js'))
			},
			{
				id: "/api/help",
				pattern: /^\/api\/help\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BUp2zQ3g.js'))
			},
			{
				id: "/api/help/categories",
				pattern: /^\/api\/help\/categories\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-i66Vn0Go.js'))
			},
			{
				id: "/api/help/search",
				pattern: /^\/api\/help\/search\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-15rhWGrg.js'))
			},
			{
				id: "/api/help/tags",
				pattern: /^\/api\/help\/tags\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BM7O3JeU.js'))
			},
			{
				id: "/api/help/[slug]",
				pattern: /^\/api\/help\/([^/]+?)\/?$/,
				params: [{"name":"slug","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DDCa-AUQ.js'))
			},
			{
				id: "/api/job-alerts",
				pattern: /^\/api\/job-alerts\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DXeb0JTL.js'))
			},
			{
				id: "/api/jobs",
				pattern: /^\/api\/jobs\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-tTKehg3r.js'))
			},
			{
				id: "/api/jobs/saved",
				pattern: /^\/api\/jobs\/saved\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DekP_rCc.js'))
			},
			{
				id: "/api/jobs/search",
				pattern: /^\/api\/jobs\/search\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DiVn79lU.js'))
			},
			{
				id: "/api/jobs/search/status",
				pattern: /^\/api\/jobs\/search\/status\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DyRZbP4C.js'))
			},
			{
				id: "/api/jobs/[id]",
				pattern: /^\/api\/jobs\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CywBwJO7.js'))
			},
			{
				id: "/api/jobs/[id]/apply",
				pattern: /^\/api\/jobs\/([^/]+?)\/apply\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BLJR8yk_.js'))
			},
			{
				id: "/api/jobs/[id]/dismiss",
				pattern: /^\/api\/jobs\/([^/]+?)\/dismiss\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-D3wJ3b76.js'))
			},
			{
				id: "/api/jobs/[id]/is-saved",
				pattern: /^\/api\/jobs\/([^/]+?)\/is-saved\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-rXCSeRhm.js'))
			},
			{
				id: "/api/jobs/[id]/report",
				pattern: /^\/api\/jobs\/([^/]+?)\/report\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-C9y943j5.js'))
			},
			{
				id: "/api/jobs/[id]/save",
				pattern: /^\/api\/jobs\/([^/]+?)\/save\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CjfjR6Ja.js'))
			},
			{
				id: "/api/languages",
				pattern: /^\/api\/languages\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DivX2TAZ.js'))
			},
			{
				id: "/api/locations",
				pattern: /^\/api\/locations\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-0edZ54cv.js'))
			},
			{
				id: "/api/locations/resolve",
				pattern: /^\/api\/locations\/resolve\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CCAOh3cQ.js'))
			},
			{
				id: "/api/maintenance",
				pattern: /^\/api\/maintenance\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-LwhaExPT.js'))
			},
			{
				id: "/api/maintenance/[id]/history",
				pattern: /^\/api\/maintenance\/([^/]+?)\/history\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CyjQFGnR.js'))
			},
			{
				id: "/api/metrics/[service]/history",
				pattern: /^\/api\/metrics\/([^/]+?)\/history\/?$/,
				params: [{"name":"service","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DA_KnlBk.js'))
			},
			{
				id: "/api/notifications",
				pattern: /^\/api\/notifications\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-B1rVcY7G.js'))
			},
			{
				id: "/api/notifications/history",
				pattern: /^\/api\/notifications\/history\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-C1d3v3wt.js'))
			},
			{
				id: "/api/notifications/mark-all-read",
				pattern: /^\/api\/notifications\/mark-all-read\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CcCEJYJT.js'))
			},
			{
				id: "/api/notifications/send",
				pattern: /^\/api\/notifications\/send\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-qsfNk61h.js'))
			},
			{
				id: "/api/notifications/settings",
				pattern: /^\/api\/notifications\/settings\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BAr1OkPX.js'))
			},
			{
				id: "/api/occupations",
				pattern: /^\/api\/occupations\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-B0gK_0Es.js'))
			},
			{
				id: "/api/occupations/resolve",
				pattern: /^\/api\/occupations\/resolve\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-c2m6uRte.js'))
			},
			{
				id: "/api/passkeys",
				pattern: /^\/api\/passkeys\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DqkLfq3q.js'))
			},
			{
				id: "/api/profile-picture",
				pattern: /^\/api\/profile-picture\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BPY7YTk9.js'))
			},
			{
				id: "/api/profiles",
				pattern: /^\/api\/profiles\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DoUYZTNa.js'))
			},
			{
				id: "/api/profiles/[id]/publish",
				pattern: /^\/api\/profiles\/([^/]+?)\/publish\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DtLYWU-O.js'))
			},
			{
				id: "/api/profiles/[id]/unpublish",
				pattern: /^\/api\/profiles\/([^/]+?)\/unpublish\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Dwk2QcKu.js'))
			},
			{
				id: "/api/profile",
				pattern: /^\/api\/profile\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BJCu2mKJ.js'))
			},
			{
				id: "/api/profile/[id]",
				pattern: /^\/api\/profile\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BhjGshOo.js'))
			},
			{
				id: "/api/profile/[id]/data",
				pattern: /^\/api\/profile\/([^/]+?)\/data\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BqQO3jpg.js'))
			},
			{
				id: "/api/profile/[id]/parsing-status",
				pattern: /^\/api\/profile\/([^/]+?)\/parsing-status\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-gEAYWwWj.js'))
			},
			{
				id: "/api/push/vapid-key",
				pattern: /^\/api\/push\/vapid-key\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BQrD31PD.js'))
			},
			{
				id: "/api/push/[action]",
				pattern: /^\/api\/push\/([^/]+?)\/?$/,
				params: [{"name":"action","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DD4jdJ0l.js'))
			},
			{
				id: "/api/referrals",
				pattern: /^\/api\/referrals\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-C4Ay56uj.js'))
			},
			{
				id: "/api/referrals/analytics",
				pattern: /^\/api\/referrals\/analytics\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CRxE29A5.js'))
			},
			{
				id: "/api/referrals/validate",
				pattern: /^\/api\/referrals\/validate\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Dn53ipB8.js'))
			},
			{
				id: "/api/resumes",
				pattern: /^\/api\/resumes\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BFAf4tjV.js'))
			},
			{
				id: "/api/resume/create",
				pattern: /^\/api\/resume\/create\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CPR3SMTU.js'))
			},
			{
				id: "/api/resume/default",
				pattern: /^\/api\/resume\/default\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BG469vh2.js'))
			},
			{
				id: "/api/resume/duplicate",
				pattern: /^\/api\/resume\/duplicate\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-C_uOfiGa.js'))
			},
			{
				id: "/api/resume/fix-parsing",
				pattern: /^\/api\/resume\/fix-parsing\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Vy0oaL9e.js'))
			},
			{
				id: "/api/resume/generate/status",
				pattern: /^\/api\/resume\/generate\/status\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DV40Yetk.js'))
			},
			{
				id: "/api/resume/manual-parse",
				pattern: /^\/api\/resume\/manual-parse\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DEJjvEfJ.js'))
			},
			{
				id: "/api/resume/optimize",
				pattern: /^\/api\/resume\/optimize\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-r2kB-OsJ.js'))
			},
			{
				id: "/api/resume/profile/[profileId]",
				pattern: /^\/api\/resume\/profile\/([^/]+?)\/?$/,
				params: [{"name":"profileId","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CcIEn6Iv.js'))
			},
			{
				id: "/api/resume/rename",
				pattern: /^\/api\/resume\/rename\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CYIPLSGZ.js'))
			},
			{
				id: "/api/resume/scanner/status",
				pattern: /^\/api\/resume\/scanner\/status\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-o3ltphBj.js'))
			},
			{
				id: "/api/resume/templates",
				pattern: /^\/api\/resume\/templates\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-5D3698ZI.js'))
			},
			{
				id: "/api/resume/upload",
				pattern: /^\/api\/resume\/upload\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-D5-qNj19.js'))
			},
			{
				id: "/api/resume/[id]",
				pattern: /^\/api\/resume\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DDpd7SIj.js'))
			},
			{
				id: "/api/resume/[id]/data",
				pattern: /^\/api\/resume\/([^/]+?)\/data\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BB5fCgZ5.js'))
			},
			{
				id: "/api/resume/[id]/download",
				pattern: /^\/api\/resume\/([^/]+?)\/download\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Ce6WAvjv.js'))
			},
			{
				id: "/api/resume/[id]/optimize",
				pattern: /^\/api\/resume\/([^/]+?)\/optimize\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-D32AVaz3.js'))
			},
			{
				id: "/api/resume/[id]/parse",
				pattern: /^\/api\/resume\/([^/]+?)\/parse\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DZJFOm8b.js'))
			},
			{
				id: "/api/resume/[id]/parsing-status",
				pattern: /^\/api\/resume\/([^/]+?)\/parsing-status\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Cg_yO9HI.js'))
			},
			{
				id: "/api/resume/[id]/status",
				pattern: /^\/api\/resume\/([^/]+?)\/status\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-B8sIoAQf.js'))
			},
			{
				id: "/api/resume/[id]/update-status",
				pattern: /^\/api\/resume\/([^/]+?)\/update-status\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DnYQAsno.js'))
			},
			{
				id: "/api/saved-jobs",
				pattern: /^\/api\/saved-jobs\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CodQWVFj.js'))
			},
			{
				id: "/api/schools",
				pattern: /^\/api\/schools\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CVG_qiMt.js'))
			},
			{
				id: "/api/search",
				pattern: /^\/api\/search\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Bi2DT42j.js'))
			},
			{
				id: "/api/search/global",
				pattern: /^\/api\/search\/global\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Msm2EkAt.js'))
			},
			{
				id: "/api/search/users",
				pattern: /^\/api\/search\/users\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-COhtlpez.js'))
			},
			{
				id: "/api/skills",
				pattern: /^\/api\/skills\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-blp2CMce.js'))
			},
			{
				id: "/api/submit",
				pattern: /^\/api\/submit\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Dqxl4bjo.js'))
			},
			{
				id: "/api/system/memory",
				pattern: /^\/api\/system\/memory\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CH3VhRQT.js'))
			},
			{
				id: "/api/upgrade",
				pattern: /^\/api\/upgrade\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-C6HlqolW.js'))
			},
			{
				id: "/api/usage",
				pattern: /^\/api\/usage\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Swbi3I9g.js'))
			},
			{
				id: "/api/usage/analytics",
				pattern: /^\/api\/usage\/analytics\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CIS6dLdH.js'))
			},
			{
				id: "/api/user",
				pattern: /^\/api\/user\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BaWwEAwq.js'))
			},
			{
				id: "/api/user/me",
				pattern: /^\/api\/user\/me\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BacqD7xR.js'))
			},
			{
				id: "/api/user/set-admin",
				pattern: /^\/api\/user\/set-admin\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-GJbwYEo_.js'))
			},
			{
				id: "/api/user/status",
				pattern: /^\/api\/user\/status\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-D-UxiXT_.js'))
			},
			{
				id: "/api/webhooks/stripe",
				pattern: /^\/api\/webhooks\/stripe\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-DCl7Lvjo.js'))
			},
			{
				id: "/api/websocket",
				pattern: /^\/api\/websocket\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BFmQWM_h.js'))
			},
			{
				id: "/api/worker-process",
				pattern: /^\/api\/worker-process\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-B6BpThHV.js'))
			},
			{
				id: "/api/worker-process/[id]",
				pattern: /^\/api\/worker-process\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-7WdXV9oJ.js'))
			},
			{
				id: "/api/worker/health",
				pattern: /^\/api\/worker\/health\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Dj9hwjC3.js'))
			},
			{
				id: "/api/ws",
				pattern: /^\/api\/ws\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-CLZSFM5c.js'))
			},
			{
				id: "/auth/forgot-password",
				pattern: /^\/auth\/forgot-password\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 13 },
				endpoint: null
			},
			{
				id: "/auth/google-callback",
				pattern: /^\/auth\/google-callback\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 14 },
				endpoint: null
			},
			{
				id: "/auth/linkedin-callback",
				pattern: /^\/auth\/linkedin-callback\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 15 },
				endpoint: null
			},
			{
				id: "/auth/passkey",
				pattern: /^\/auth\/passkey\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-BqhvpCVo.js'))
			},
			{
				id: "/auth/reset-password",
				pattern: /^\/auth\/reset-password\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 16 },
				endpoint: null
			},
			{
				id: "/auth/sign-in",
				pattern: /^\/auth\/sign-in\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 17 },
				endpoint: null
			},
			{
				id: "/auth/sign-up",
				pattern: /^\/auth\/sign-up\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 18 },
				endpoint: null
			},
			{
				id: "/auth/verified",
				pattern: /^\/auth\/verified\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 19 },
				endpoint: null
			},
			{
				id: "/auth/verify",
				pattern: /^\/auth\/verify\/?$/,
				params: [],
				page: { layouts: [0,2,], errors: [1,,], leaf: 20 },
				endpoint: null
			},
			{
				id: "/auto-apply",
				pattern: /^\/auto-apply\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 21 },
				endpoint: null
			},
			{
				id: "/blog",
				pattern: /^\/blog\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 22 },
				endpoint: null
			},
			{
				id: "/blog/[slug]",
				pattern: /^\/blog\/([^/]+?)\/?$/,
				params: [{"name":"slug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,], errors: [1,], leaf: 23 },
				endpoint: null
			},
			{
				id: "/co-pilot",
				pattern: /^\/co-pilot\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 24 },
				endpoint: null
			},
			{
				id: "/contact",
				pattern: /^\/contact\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 25 },
				endpoint: null
			},
			{
				id: "/dashboard",
				pattern: /^\/dashboard\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 26 },
				endpoint: null
			},
			{
				id: "/dashboard/automation",
				pattern: /^\/dashboard\/automation\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 27 },
				endpoint: null
			},
			{
				id: "/dashboard/automation/[id]",
				pattern: /^\/dashboard\/automation\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 28 },
				endpoint: null
			},
			{
				id: "/dashboard/builder",
				pattern: /^\/dashboard\/builder\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 29 },
				endpoint: null
			},
			{
				id: "/dashboard/builder/superform/[id]",
				pattern: /^\/dashboard\/builder\/superform\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 30 },
				endpoint: null
			},
			{
				id: "/dashboard/builder/[id]",
				pattern: /^\/dashboard\/builder\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 31 },
				endpoint: null
			},
			{
				id: "/dashboard/documents",
				pattern: /^\/dashboard\/documents\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 32 },
				endpoint: null
			},
			{
				id: "/dashboard/documents/[id]",
				pattern: /^\/dashboard\/documents\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 33 },
				endpoint: null
			},
			{
				id: "/dashboard/documents/[id]/ats",
				pattern: /^\/dashboard\/documents\/([^/]+?)\/ats\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 34 },
				endpoint: null
			},
			{
				id: "/dashboard/features",
				pattern: /^\/dashboard\/features\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 35 },
				endpoint: null
			},
			{
				id: "/dashboard/jobs",
				pattern: /^\/dashboard\/jobs\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 36 },
				endpoint: null
			},
			{
				id: "/dashboard/jobs/[id]",
				pattern: /^\/dashboard\/jobs\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 37 },
				endpoint: null
			},
			{
				id: "/dashboard/matches",
				pattern: /^\/dashboard\/matches\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 38 },
				endpoint: null
			},
			{
				id: "/dashboard/notifications",
				pattern: /^\/dashboard\/notifications\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 39 },
				endpoint: null
			},
			{
				id: "/dashboard/resumes",
				pattern: /^\/dashboard\/resumes\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 40 },
				endpoint: null
			},
			{
				id: "/dashboard/resumes/[id]",
				pattern: /^\/dashboard\/resumes\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 41 },
				endpoint: null
			},
			{
				id: "/dashboard/resumes/[id]/optimize",
				pattern: /^\/dashboard\/resumes\/([^/]+?)\/optimize\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,], errors: [1,,], leaf: 42 },
				endpoint: null
			},
			{
				id: "/dashboard/settings",
				pattern: /^\/dashboard\/settings\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 43 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/account",
				pattern: /^\/dashboard\/settings\/account\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 44 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin",
				pattern: /^\/dashboard\/settings\/admin\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,], errors: [1,,,,], leaf: 45 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/email",
				pattern: /^\/dashboard\/settings\/admin\/email\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,6,], errors: [1,,,,,], leaf: 46 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/email/analytics",
				pattern: /^\/dashboard\/settings\/admin\/email\/analytics\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,6,], errors: [1,,,,,], leaf: 47 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/email/audiences",
				pattern: /^\/dashboard\/settings\/admin\/email\/audiences\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,6,], errors: [1,,,,,], leaf: 48 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/email/broadcast",
				pattern: /^\/dashboard\/settings\/admin\/email\/broadcast\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,6,], errors: [1,,,,,], leaf: 49 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/email/queue",
				pattern: /^\/dashboard\/settings\/admin\/email\/queue\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,6,], errors: [1,,,,,], leaf: 50 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/feature-usage",
				pattern: /^\/dashboard\/settings\/admin\/feature-usage\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,], errors: [1,,,,], leaf: 51 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/features",
				pattern: /^\/dashboard\/settings\/admin\/features\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,], errors: [1,,,,], leaf: 52 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/maintenance",
				pattern: /^\/dashboard\/settings\/admin\/maintenance\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,], errors: [1,,,,], leaf: 53 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/notifications",
				pattern: /^\/dashboard\/settings\/admin\/notifications\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,], errors: [1,,,,], leaf: 54 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/plans",
				pattern: /^\/dashboard\/settings\/admin\/plans\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,], errors: [1,,,,], leaf: 55 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/seed-features",
				pattern: /^\/dashboard\/settings\/admin\/seed-features\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,], errors: [1,,,,], leaf: 56 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/admin/subscriptions",
				pattern: /^\/dashboard\/settings\/admin\/subscriptions\/?$/,
				params: [],
				page: { layouts: [0,3,4,5,], errors: [1,,,,], leaf: 57 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/analysis",
				pattern: /^\/dashboard\/settings\/analysis\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 58 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/billing",
				pattern: /^\/dashboard\/settings\/billing\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 59 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/email",
				pattern: /^\/dashboard\/settings\/email\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 60 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/general",
				pattern: /^\/dashboard\/settings\/general\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 61 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/interview-coach",
				pattern: /^\/dashboard\/settings\/interview-coach\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 62 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/make-admin",
				pattern: /^\/dashboard\/settings\/make-admin\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 63 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/notifications",
				pattern: /^\/dashboard\/settings\/notifications\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 64 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/profile",
				pattern: /^\/dashboard\/settings\/profile\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 65 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/profile/[id]",
				pattern: /^\/dashboard\/settings\/profile\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 66 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/referrals",
				pattern: /^\/dashboard\/settings\/referrals\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 67 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/security",
				pattern: /^\/dashboard\/settings\/security\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 68 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/team",
				pattern: /^\/dashboard\/settings\/team\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 69 },
				endpoint: null
			},
			{
				id: "/dashboard/settings/usage",
				pattern: /^\/dashboard\/settings\/usage\/?$/,
				params: [],
				page: { layouts: [0,3,4,], errors: [1,,,], leaf: 70 },
				endpoint: null
			},
			{
				id: "/dashboard/tracker",
				pattern: /^\/dashboard\/tracker\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 71 },
				endpoint: null
			},
			{
				id: "/dashboard/usage",
				pattern: /^\/dashboard\/usage\/?$/,
				params: [],
				page: { layouts: [0,3,], errors: [1,,], leaf: 72 },
				endpoint: __memo(() => import('./chunks/_server.ts-ClhU6mR5.js'))
			},
			{
				id: "/employers",
				pattern: /^\/employers\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 73 },
				endpoint: null
			},
			{
				id: "/health",
				pattern: /^\/health\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Czo1hb0S.js'))
			},
			{
				id: "/help",
				pattern: /^\/help\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 74 },
				endpoint: null
			},
			{
				id: "/help/category/[slug]",
				pattern: /^\/help\/category\/([^/]+?)\/?$/,
				params: [{"name":"slug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,], errors: [1,], leaf: 75 },
				endpoint: null
			},
			{
				id: "/help/quick-start",
				pattern: /^\/help\/quick-start\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 76 },
				endpoint: null
			},
			{
				id: "/help/search",
				pattern: /^\/help\/search\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 77 },
				endpoint: null
			},
			{
				id: "/help/[slug]",
				pattern: /^\/help\/([^/]+?)\/?$/,
				params: [{"name":"slug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,], errors: [1,], leaf: 78 },
				endpoint: null
			},
			{
				id: "/job-tracker",
				pattern: /^\/job-tracker\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 79 },
				endpoint: null
			},
			{
				id: "/jobs",
				pattern: /^\/jobs\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 80 },
				endpoint: null
			},
			{
				id: "/legal",
				pattern: /^\/legal\/?$/,
				params: [],
				page: { layouts: [0,7,], errors: [1,,], leaf: 81 },
				endpoint: null
			},
			{
				id: "/legal/[slug]",
				pattern: /^\/legal\/([^/]+?)\/?$/,
				params: [{"name":"slug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,7,], errors: [1,,], leaf: 82 },
				endpoint: null
			},
			{
				id: "/press",
				pattern: /^\/press\/?$/,
				params: [],
				page: { layouts: [0,8,], errors: [1,,], leaf: 83 },
				endpoint: null
			},
			{
				id: "/press/coverage",
				pattern: /^\/press\/coverage\/?$/,
				params: [],
				page: { layouts: [0,8,], errors: [1,,], leaf: 84 },
				endpoint: null
			},
			{
				id: "/press/images",
				pattern: /^\/press\/images\/?$/,
				params: [],
				page: { layouts: [0,8,], errors: [1,,], leaf: 85 },
				endpoint: null
			},
			{
				id: "/press/releases",
				pattern: /^\/press\/releases\/?$/,
				params: [],
				page: { layouts: [0,8,], errors: [1,,], leaf: 86 },
				endpoint: null
			},
			{
				id: "/press/releases/[slug]",
				pattern: /^\/press\/releases\/([^/]+?)\/?$/,
				params: [{"name":"slug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,8,], errors: [1,,], leaf: 87 },
				endpoint: null
			},
			{
				id: "/pricing",
				pattern: /^\/pricing\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 88 },
				endpoint: null
			},
			{
				id: "/profile/[id]",
				pattern: /^\/profile\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,], errors: [1,], leaf: 89 },
				endpoint: null
			},
			{
				id: "/recruiters",
				pattern: /^\/recruiters\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 90 },
				endpoint: null
			},
			{
				id: "/resources",
				pattern: /^\/resources\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 91 },
				endpoint: null
			},
			{
				id: "/resources/[slug]",
				pattern: /^\/resources\/([^/]+?)\/?$/,
				params: [{"name":"slug","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,], errors: [1,], leaf: 92 },
				endpoint: null
			},
			{
				id: "/resume-builder",
				pattern: /^\/resume-builder\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 93 },
				endpoint: null
			},
			{
				id: "/robots.txt",
				pattern: /^\/robots\.txt\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-Bz3HAlUK.js'))
			},
			{
				id: "/sitemap.xml",
				pattern: /^\/sitemap\.xml\/?$/,
				params: [],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-B3hQowIH.js'))
			},
			{
				id: "/studio",
				pattern: /^\/studio\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 94 },
				endpoint: __memo(() => import('./chunks/_server.ts-c22AfIpF.js'))
			},
			{
				id: "/studio/[path]",
				pattern: /^\/studio\/([^/]+?)\/?$/,
				params: [{"name":"path","optional":false,"rest":false,"chained":false}],
				page: null,
				endpoint: __memo(() => import('./chunks/_server.ts-_pXMVOtK.js'))
			},
			{
				id: "/system-status",
				pattern: /^\/system-status\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 95 },
				endpoint: null
			},
			{
				id: "/system-status/history",
				pattern: /^\/system-status\/history\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 96 },
				endpoint: null
			}
		],
		prerendered_routes: new Set([]),
		matchers: async () => {
			
			return {  };
		},
		server_assets: {}
	}
}
})();

const prerendered = new Set([]);

const base = "";

export { base, manifest, prerendered };
//# sourceMappingURL=manifest.js.map
