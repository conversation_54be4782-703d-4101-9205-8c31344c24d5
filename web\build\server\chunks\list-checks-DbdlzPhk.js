import { X as sanitize_props, R as spread_props, a0 as slot } from './index3-CqUPEnZw.js';
import { I as Icon } from './Icon-A4vzmk-O.js';

function List_checks($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    ["path", { "d": "m3 17 2 2 4-4" }],
    ["path", { "d": "m3 7 2 2 4-4" }],
    ["path", { "d": "M13 6h8" }],
    ["path", { "d": "M13 12h8" }],
    ["path", { "d": "M13 18h8" }]
  ];
  Icon($$payload, spread_props([
    { name: "list-checks" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}

export { List_checks as L };
//# sourceMappingURL=list-checks-DbdlzPhk.js.map
