{"version": 3, "file": "61-5VRg0PVz.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/general/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/61.js"], "sourcesContent": ["import { r as redirect, f as fail } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { g as getUserFromToken } from \"../../../../../chunks/auth.js\";\nimport { s as superValidate } from \"../../../../../chunks/superValidate.js\";\nimport \"ts-deepmerge\";\nimport \"memoize-weak\";\nimport { z as zod } from \"../../../../../chunks/zod.js\";\nimport { z } from \"zod\";\nconst generalSettingsSchema = z.object({\n  siteName: z.string().min(1, \"Site name is required\"),\n  siteDescription: z.string().optional(),\n  contactEmail: z.string().email(\"Invalid email address\"),\n  timezone: z.string(),\n  dateFormat: z.string(),\n  timeFormat: z.string(),\n  language: z.string()\n});\nconst load = async ({ cookies, locals }) => {\n  const tokenData = getUserFromToken(cookies);\n  if (!tokenData || !tokenData.email) {\n    throw redirect(302, \"/auth/sign-in\");\n  }\n  const userData = await prisma.user.findUnique({\n    where: { email: tokenData.email }\n  });\n  if (!userData) {\n    throw redirect(302, \"/auth/sign-in\");\n  }\n  locals.user = userData;\n  const preferences = userData.preferences ? userData.preferences : {};\n  const form = await superValidate(\n    {\n      siteName: preferences.siteName || \"Hirli\",\n      siteDescription: preferences.siteDescription || \"Your job application automation assistant\",\n      contactEmail: preferences.contactEmail || userData.email,\n      timezone: preferences.timezone || \"UTC\",\n      dateFormat: preferences.dateFormat || \"MM/DD/YYYY\",\n      timeFormat: preferences.timeFormat || \"12h\",\n      language: preferences.language || \"en\"\n    },\n    zod(generalSettingsSchema)\n  );\n  return {\n    user: userData,\n    form\n  };\n};\nconst actions = {\n  default: async ({ request, cookies, locals }) => {\n    const tokenData = getUserFromToken(cookies);\n    if (!tokenData || !tokenData.email) {\n      throw redirect(302, \"/auth/sign-in\");\n    }\n    const userData = await prisma.user.findUnique({\n      where: { email: tokenData.email }\n    });\n    if (!userData) {\n      throw redirect(302, \"/auth/sign-in\");\n    }\n    const form = await superValidate(request, zod(generalSettingsSchema));\n    if (!form.valid) {\n      return fail(400, { form });\n    }\n    try {\n      await prisma.user.update({\n        where: { id: userData.id },\n        data: {\n          preferences: {\n            ...userData.preferences,\n            siteName: form.data.siteName,\n            siteDescription: form.data.siteDescription,\n            contactEmail: form.data.contactEmail,\n            timezone: form.data.timezone,\n            dateFormat: form.data.dateFormat,\n            timeFormat: form.data.timeFormat,\n            language: form.data.language\n          }\n        }\n      });\n      return { form, success: true };\n    } catch (error) {\n      console.error(\"Error updating general settings:\", error);\n      return fail(500, { form, error: \"Failed to update general settings\" });\n    }\n  }\n};\nexport {\n  actions,\n  load\n};\n", "import * as server from '../entries/pages/dashboard/settings/general/_page.server.ts.js';\n\nexport const index = 61;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/settings/general/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/settings/general/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/61.DYJy4CcD.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/DDUgF6Ik.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/Cdn-N1RY.js\",\"_app/immutable/chunks/BkJY4La4.js\",\"_app/immutable/chunks/GwmmX_iF.js\",\"_app/immutable/chunks/D50jIuLr.js\",\"_app/immutable/chunks/FeejBSkx.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/DMTMHyMa.js\",\"_app/immutable/chunks/CzsE_FAw.js\",\"_app/immutable/chunks/VNuMAkuB.js\",\"_app/immutable/chunks/C0-qpl0T.js\",\"_app/immutable/chunks/DQC1iFs0.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/CeJyStlM.js\",\"_app/immutable/chunks/DjPYYl4Z.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/BoNCRmBc.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/CXvW3J0s.js\",\"_app/immutable/chunks/BvvicRXk.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/ByFxH6T3.js\",\"_app/immutable/chunks/D1zde6Ej.js\",\"_app/immutable/chunks/rNI1Perp.js\",\"_app/immutable/chunks/hA0h0kTo.js\",\"_app/immutable/chunks/BSHZ37s_.js\"];\nexport const stylesheets = [\"_app/immutable/assets/Toaster.DKF17Rty.css\"];\nexport const fonts = [];\n"], "names": ["z.object", "z.string"], "mappings": ";;;;;;;;;;;;;AAQA,MAAM,qBAAqB,GAAGA,UAAQ,CAAC;AACvC,EAAE,QAAQ,EAAEC,UAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,uBAAuB,CAAC;AACtD,EAAE,eAAe,EAAEA,UAAQ,EAAE,CAAC,QAAQ,EAAE;AACxC,EAAE,YAAY,EAAEA,UAAQ,EAAE,CAAC,KAAK,CAAC,uBAAuB,CAAC;AACzD,EAAE,QAAQ,EAAEA,UAAQ,EAAE;AACtB,EAAE,UAAU,EAAEA,UAAQ,EAAE;AACxB,EAAE,UAAU,EAAEA,UAAQ,EAAE;AACxB,EAAE,QAAQ,EAAEA,UAAQ;AACpB,CAAC,CAAC;AACF,MAAM,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,MAAM,SAAS,GAAG,gBAAgB,CAAC,OAAO,CAAC;AAC7C,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;AACtC,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACxC;AACA,EAAE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAChD,IAAI,KAAK,EAAE,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK;AACnC,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACxC;AACA,EAAE,MAAM,CAAC,IAAI,GAAG,QAAQ;AACxB,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,GAAG,EAAE;AACtE,EAAE,MAAM,IAAI,GAAG,MAAM,aAAa;AAClC,IAAI;AACJ,MAAM,QAAQ,EAAE,WAAW,CAAC,QAAQ,IAAI,OAAO;AAC/C,MAAM,eAAe,EAAE,WAAW,CAAC,eAAe,IAAI,2CAA2C;AACjG,MAAM,YAAY,EAAE,WAAW,CAAC,YAAY,IAAI,QAAQ,CAAC,KAAK;AAC9D,MAAM,QAAQ,EAAE,WAAW,CAAC,QAAQ,IAAI,KAAK;AAC7C,MAAM,UAAU,EAAE,WAAW,CAAC,UAAU,IAAI,YAAY;AACxD,MAAM,UAAU,EAAE,WAAW,CAAC,UAAU,IAAI,KAAK;AACjD,MAAM,QAAQ,EAAE,WAAW,CAAC,QAAQ,IAAI;AACxC,KAAK;AACL,IAAI,GAAG,CAAC,qBAAqB;AAC7B,GAAG;AACH,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI;AACJ,GAAG;AACH,CAAC;AACD,MAAM,OAAO,GAAG;AAChB,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AACnD,IAAI,MAAM,SAAS,GAAG,gBAAgB,CAAC,OAAO,CAAC;AAC/C,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;AACxC,MAAM,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AAC1C;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK;AACrC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AAC1C;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,aAAa,CAAC,OAAO,EAAE,GAAG,CAAC,qBAAqB,CAAC,CAAC;AACzE,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACrB,MAAM,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC;AAChC;AACA,IAAI,IAAI;AACR,MAAM,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/B,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAClC,QAAQ,IAAI,EAAE;AACd,UAAU,WAAW,EAAE;AACvB,YAAY,GAAG,QAAQ,CAAC,WAAW;AACnC,YAAY,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ;AACxC,YAAY,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe;AACtD,YAAY,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY;AAChD,YAAY,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ;AACxC,YAAY,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU;AAC5C,YAAY,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU;AAC5C,YAAY,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC;AAChC;AACA;AACA,OAAO,CAAC;AACR,MAAM,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;AACpC,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC9D,MAAM,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,mCAAmC,EAAE,CAAC;AAC5E;AACA;AACA,CAAC;;;;;;;;ACnFW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA6D,CAAC,EAAE;AAE3H,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACzqD,MAAC,WAAW,GAAG,CAAC,4CAA4C;AAC5D,MAAC,KAAK,GAAG;;;;"}