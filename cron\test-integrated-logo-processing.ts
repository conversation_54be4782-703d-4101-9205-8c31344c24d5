import { logger } from "./utils/logger";
import { prisma } from "./lib/prismaClient";
import { ensureCompanyLogosWithBingSearch } from "./lib/enrichCompanyData";

/**
 * Test the integrated logo processing with Bing search
 */
async function testIntegratedLogoProcessing() {
  logger.info("🎨 Testing integrated logo processing with Bing search");

  try {
    // Test the new Bing logo search function
    const bingLogosAdded = await ensureCompanyLogosWithBingSearch(prisma);
    
    logger.info(`✅ Test completed: ${bingLogosAdded} logos added via Bing search`);
    
    return {
      success: true,
      bingLogosAdded,
    };
  } catch (error) {
    logger.error("❌ Test failed:", error);
    return {
      success: false,
      error: error.message,
    };
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testIntegratedLogoProcessing()
  .then((result) => {
    if (result.success) {
      logger.info("✅ Integrated logo processing test completed successfully");
      process.exit(0);
    } else {
      logger.error("❌ Integrated logo processing test failed");
      process.exit(1);
    }
  })
  .catch((error) => {
    logger.error("💥 Unexpected error in test:", error);
    process.exit(1);
  });
