import { createClient } from '@sanity/client';

const projectId = "fqw18aoo";
const dataset = "production";
const apiVersion = "2023-05-03";
createClient({
  projectId,
  dataset,
  apiVersion,
  useCdn: process.env.NODE_ENV === "production"
  // Use CDN in production for better performance
});
function urlFor(source, options) {
  return imageUrl(source, options || {});
}
function imageUrl(source, options = {}) {
  if (!source || !source.asset) {
    return "";
  }
  const refId = source.asset._ref || "";
  if (!refId.startsWith("image-")) {
    return "";
  }
  const [, id, dimensions, format] = refId.split("-");
  if (!id) {
    return "";
  }
  let url = `https://cdn.sanity.io/images/${projectId}/${dataset}/${id}`;
  if (format) {
    url += `.${format}`;
  } else {
    url += ".jpg";
  }
  const params = [];
  const width = options.width || 800;
  params.push(`w=${width}`);
  if (options.height) {
    params.push(`h=${options.height}`);
  }
  const fit = options.fit || "max";
  params.push(`fit=${fit}`);
  params.push("auto=format");
  const quality = options.quality || 80;
  params.push(`q=${quality}`);
  if (params.length > 0) {
    url += `?${params.join("&")}`;
  }
  return url;
}

export { urlFor as u };
//# sourceMappingURL=sanityClient-BQ6Z_2a-.js.map
