import { j as json } from './index-Ddp2AB5f.js';
import { g as getRedisClient } from './redis-DxlM1ibh.js';
import 'ioredis';

const WORKER_TYPES = [
  "resume-parsing",
  "resume-optimization",
  "search",
  "ats-analysis",
  "job-specific-analysis",
  "email",
  "automation"
];
const WORKER_STATUS_TYPES = ["healthy", "degraded", "unhealthy", "unknown"];
function validateWorkerHealth(body) {
  const { workerType, status, metrics } = body;
  if (!workerType || !metrics) {
    return {
      valid: false,
      error: "Missing required fields: workerType and metrics are required"
    };
  }
  if (!WORKER_TYPES.includes(workerType)) {
    return {
      valid: false,
      error: `Invalid worker type: ${workerType}`
    };
  }
  const workerStatus = status ?? "healthy";
  if (!WORKER_STATUS_TYPES.includes(workerStatus)) {
    return {
      valid: false,
      error: `Invalid status: ${status}`
    };
  }
  const requiredMetricFields = [
    "cpu",
    "memory",
    "queueSize",
    "processingCount",
    "responseTime",
    "errorRate",
    "successRate",
    "capacity"
  ];
  for (const field of requiredMetricFields) {
    if (metrics[field] === void 0) {
      return {
        valid: false,
        error: `Missing required metric: ${field}`
      };
    }
  }
  return { valid: true };
}
async function updateWorkerHealthInRedis(redis, workerType, status, metrics) {
  try {
    const healthy = metrics.cpu < 90 && metrics.memory < 90 && metrics.errorRate < 10 && metrics.capacity > 20;
    const healthData = {
      status,
      healthy,
      lastHeartbeat: (/* @__PURE__ */ new Date()).toISOString()
    };
    await redis.hset("worker:health", workerType, JSON.stringify(healthData));
    await redis.hset("worker:metrics", workerType, JSON.stringify(metrics));
    return true;
  } catch (error) {
    console.error(`Error updating health for worker ${workerType}:`, error);
    return false;
  }
}
const POST = async ({ request, locals }) => {
  try {
    const user = locals.user;
    const isProduction = process.env.NODE_ENV === "production";
    if (isProduction && !user) {
      return new Response("Unauthorized", { status: 401 });
    }
    const body = await request.json();
    const validation = validateWorkerHealth(body);
    if (!validation.valid) {
      return json(
        {
          success: false,
          error: validation.error
        },
        { status: 400 }
      );
    }
    const redis = await getRedisClient();
    if (!redis) {
      return json(
        {
          success: false,
          error: "Redis client not available"
        },
        { status: 500 }
      );
    }
    const { workerType, status = "healthy", metrics } = body;
    const result = await updateWorkerHealthInRedis(redis, workerType, status, metrics);
    if (!result) {
      return json(
        {
          success: false,
          error: "Failed to update worker health"
        },
        { status: 500 }
      );
    }
    return json({
      success: true,
      message: `Health status updated for worker: ${workerType}`
    });
  } catch (error) {
    console.error("Error updating worker health:", error);
    return json(
      {
        success: false,
        error: "Failed to update worker health",
        details: String(error)
      },
      { status: 500 }
    );
  }
};

export { POST };
//# sourceMappingURL=_server.ts-Dj9hwjC3.js.map
