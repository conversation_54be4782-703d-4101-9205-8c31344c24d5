import { X as sanitize_props, R as spread_props, a0 as slot } from './index3-CqUPEnZw.js';
import { I as Icon } from './Icon-A4vzmk-O.js';

function Frown($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    [
      "circle",
      { "cx": "12", "cy": "12", "r": "10" }
    ],
    ["path", { "d": "M16 16s-1.5-2-4-2-4 2-4 2" }],
    [
      "line",
      {
        "x1": "9",
        "x2": "9.01",
        "y1": "9",
        "y2": "9"
      }
    ],
    [
      "line",
      {
        "x1": "15",
        "x2": "15.01",
        "y1": "9",
        "y2": "9"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "frown" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}
function Sliders_vertical($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    [
      "line",
      {
        "x1": "4",
        "x2": "4",
        "y1": "21",
        "y2": "14"
      }
    ],
    [
      "line",
      { "x1": "4", "x2": "4", "y1": "10", "y2": "3" }
    ],
    [
      "line",
      {
        "x1": "12",
        "x2": "12",
        "y1": "21",
        "y2": "12"
      }
    ],
    [
      "line",
      {
        "x1": "12",
        "x2": "12",
        "y1": "8",
        "y2": "3"
      }
    ],
    [
      "line",
      {
        "x1": "20",
        "x2": "20",
        "y1": "21",
        "y2": "16"
      }
    ],
    [
      "line",
      {
        "x1": "20",
        "x2": "20",
        "y1": "12",
        "y2": "3"
      }
    ],
    [
      "line",
      {
        "x1": "2",
        "x2": "6",
        "y1": "14",
        "y2": "14"
      }
    ],
    [
      "line",
      {
        "x1": "10",
        "x2": "14",
        "y1": "8",
        "y2": "8"
      }
    ],
    [
      "line",
      {
        "x1": "18",
        "x2": "22",
        "y1": "16",
        "y2": "16"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "sliders-vertical" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}

export { Frown as F, Sliders_vertical as S };
//# sourceMappingURL=sliders-vertical-or4TQDCk.js.map
