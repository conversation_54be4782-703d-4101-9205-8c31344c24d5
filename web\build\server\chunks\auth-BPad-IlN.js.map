{"version": 3, "file": "auth-BPad-IlN.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/auth.js"], "sourcesContent": ["import { p as prisma } from \"./prisma.js\";\nimport jwt from \"jsonwebtoken\";\nimport * as U<PERSON>arser from \"ua-parser-js\";\nimport { d as dev } from \"./index4.js\";\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key\";\nconst SESSION_EXPIRY_DAYS = 30;\nasync function getLocationFromRequest(request) {\n  let ip = \"127.0.0.1\";\n  let location = \"Unknown Location\";\n  if (!request) {\n    return { ip, location };\n  }\n  ip = request.headers.get(\"x-forwarded-for\") || request.headers.get(\"x-real-ip\") || \"127.0.0.1\";\n  if (ip.includes(\",\")) {\n    ip = ip.split(\",\")[0].trim();\n  }\n  if (ip === \"127.0.0.1\" || ip.startsWith(\"192.168.\") || ip.startsWith(\"10.\")) {\n    return { ip, location: \"Local Network\" };\n  }\n  try {\n    const response = await fetch(`https://ipapi.co/${ip}/json/`);\n    if (response.ok) {\n      const data = await response.json();\n      if (data.error) {\n        console.error(\"IP geolocation error:\", data.error);\n      } else if (data.city && data.country_name) {\n        location = `${data.city}, ${data.country_name}`;\n      } else if (data.country_name) {\n        location = data.country_name;\n      }\n    }\n  } catch (error) {\n    console.error(\"Error getting location from IP:\", error);\n  }\n  return { ip, location };\n}\nfunction cleanLocation(location) {\n  if (!location) return \"Unknown Location\";\n  if (location === \"Local Network\") return location;\n  if (location === \"Unknown Location\") return location;\n  const cleaned = location.trim();\n  const cityStateMatch = cleaned.match(/([A-Z][a-zA-Z\\s]+),\\s*([A-Z]{2})/i);\n  if (cityStateMatch) {\n    return `${cityStateMatch[1]}, ${cityStateMatch[2].toUpperCase()}`;\n  }\n  const cityCountryMatch = cleaned.match(/([A-Z][a-zA-Z\\s]+),\\s*([A-Z][a-zA-Z\\s]+)/i);\n  if (cityCountryMatch) {\n    return cleaned;\n  }\n  return cleaned;\n}\nasync function getAuthLocation(request) {\n  const { ip, location: rawLocation } = await getLocationFromRequest(request);\n  const location = cleanLocation(rawLocation);\n  return { ip, location, rawLocation };\n}\nasync function createSession(userId, request) {\n  const userAgent = request?.headers?.get(\"user-agent\") || \"\";\n  const parser = new UAParser.UAParser(userAgent);\n  const browser = parser.getBrowser();\n  const os = parser.getOS();\n  const device = parser.getDevice();\n  const { ip, location } = await getLocationFromRequest(request);\n  const expiresAt = /* @__PURE__ */ new Date();\n  expiresAt.setDate(expiresAt.getDate() + SESSION_EXPIRY_DAYS);\n  const token = jwt.sign({ userId }, JWT_SECRET, {\n    expiresIn: `${SESSION_EXPIRY_DAYS}d`\n  });\n  const browserName = (browser.name || \"\").toLowerCase().trim();\n  const osName = (os.name || \"\").toLowerCase().trim();\n  const deviceType = (device.type || \"desktop\").toLowerCase().trim();\n  const browserString = `${browser.name || \"\"} ${browser.version || \"\"}`.trim();\n  const osString = `${os.name || \"\"} ${os.version || \"\"}`.trim();\n  const deviceString = device.vendor ? `${device.vendor} ${device.model || \"\"}` : device.type || \"Desktop\";\n  console.log(\n    `Session fingerprint - Browser: ${browserName}, OS: ${osName}, Device: ${deviceType}, IP: ${ip}`\n  );\n  const existingSession = await prisma.session.findFirst({\n    where: {\n      userId,\n      isRevoked: false,\n      expires: { gt: /* @__PURE__ */ new Date() },\n      OR: [\n        // Match by IP address if it's not a local network\n        ...ip !== \"127.0.0.1\" && !ip.startsWith(\"192.168.\") && !ip.startsWith(\"10.\") ? [{ ip }] : [],\n        // Match on browser name (without version) and OS name\n        {\n          browser: { contains: browserName, mode: \"insensitive\" },\n          os: { contains: osName, mode: \"insensitive\" },\n          isRevoked: false\n        },\n        // Match on device type and OS\n        {\n          device: { contains: deviceType, mode: \"insensitive\" },\n          os: { contains: osName, mode: \"insensitive\" },\n          isRevoked: false\n        }\n      ]\n    },\n    orderBy: {\n      lastActive: \"desc\"\n      // Get the most recently active session\n    }\n  });\n  if (existingSession) {\n    console.log(\n      `Found existing session for user ${userId} on same device. Session ID: ${existingSession.id}`\n    );\n    console.log(\n      `Existing session details - Browser: ${existingSession.browser}, OS: ${existingSession.os}, Device: ${existingSession.device}`\n    );\n    const updatedSession = await prisma.session.update({\n      where: { id: existingSession.id },\n      data: {\n        sessionToken: token,\n        expires: expiresAt,\n        lastActive: /* @__PURE__ */ new Date(),\n        ip,\n        // Update IP in case it changed\n        location,\n        // Update location in case it changed\n        userAgent\n        // Update user agent as it might have changed slightly\n      }\n    });\n    console.log(`Updated existing session with new token. Session ID: ${updatedSession.id}`);\n    return updatedSession;\n  }\n  console.log(`No existing session found. Creating new session for user ${userId}`);\n  const session = await prisma.session.create({\n    data: {\n      userId,\n      sessionToken: token,\n      expires: expiresAt,\n      userAgent,\n      browser: browserString,\n      os: osString,\n      device: deviceString,\n      ip,\n      location\n    }\n  });\n  console.log(`New session created. Session ID: ${session.id}`);\n  return session;\n}\nasync function createSessionToken(user, request) {\n  try {\n    console.log(\"Creating session token for user:\", user.id);\n    try {\n      await cleanupUserSessions(user.id);\n    } catch (cleanupError) {\n      console.error(\"Error cleaning up old sessions:\", cleanupError);\n    }\n    const session = await createSession(user.id, request);\n    console.log(\"Session created/updated successfully:\", session.id);\n    return session.sessionToken;\n  } catch (error) {\n    console.error(\"Error creating session token:\", error);\n    console.log(\"Falling back to simple JWT token\");\n    return jwt.sign({ userId: user.id }, JWT_SECRET, {\n      expiresIn: `${SESSION_EXPIRY_DAYS}d`\n    });\n  }\n}\nasync function verifySessionToken(token) {\n  try {\n    if (!token || typeof token !== \"string\" || token.trim() === \"\") {\n      console.log(\"Invalid token format: token is empty or not a string\");\n      return null;\n    }\n    try {\n      const decoded = jwt.verify(token, JWT_SECRET);\n      const session = await prisma.session.findFirst({\n        where: {\n          sessionToken: token,\n          isRevoked: false,\n          expires: { gt: /* @__PURE__ */ new Date() }\n        },\n        include: { user: true }\n      });\n      if (!session) {\n        console.log(\"Session not found in database or is invalid\");\n        if (dev) ;\n        return null;\n      }\n      await prisma.session.update({\n        where: { id: session.id },\n        data: { lastActive: /* @__PURE__ */ new Date() }\n      });\n      return session.user;\n    } catch (jwtError) {\n      console.error(\"JWT verification failed:\", jwtError.message);\n      return null;\n    }\n  } catch (error) {\n    console.error(\"Error verifying session token:\", error);\n    return null;\n  }\n}\nasync function getUserFromToken(cookies) {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return null;\n  try {\n    return await verifySessionToken(token);\n  } catch (error) {\n    console.error(\"Error getting user from token:\", error);\n    return null;\n  }\n}\nasync function cleanupUserSessions(userId) {\n  try {\n    const now = /* @__PURE__ */ new Date();\n    const expiredResult = await prisma.session.deleteMany({\n      where: {\n        userId,\n        expires: { lt: now }\n      }\n    });\n    const thirtyDaysAgo = /* @__PURE__ */ new Date();\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n    const revokedResult = await prisma.session.deleteMany({\n      where: {\n        userId,\n        isRevoked: true,\n        lastActive: { lt: thirtyDaysAgo }\n        // Use lastActive instead of updatedAt\n      }\n    });\n    const duplicatesResult = await cleanupDuplicateSessions(userId);\n    console.log(\n      `Cleaned up ${expiredResult.count} expired, ${revokedResult.count} revoked, and ${duplicatesResult.count} duplicate sessions for user ${userId}`\n    );\n    return {\n      expired: expiredResult.count,\n      revoked: revokedResult.count,\n      duplicates: duplicatesResult.count\n    };\n  } catch (error) {\n    console.error(\"Error cleaning up sessions:\", error);\n    return { expired: 0, revoked: 0, duplicates: 0, error };\n  }\n}\nasync function cleanupDuplicateSessions(userId) {\n  try {\n    const sessions = await prisma.session.findMany({\n      where: {\n        userId,\n        isRevoked: false,\n        expires: { gt: /* @__PURE__ */ new Date() }\n      },\n      orderBy: { lastActive: \"desc\" }\n    });\n    console.log(`Found ${sessions.length} active sessions for user ${userId}`);\n    const sessionsByIP = /* @__PURE__ */ new Map();\n    const sessionsByDevice = /* @__PURE__ */ new Map();\n    const sessionsByBrowserOS = /* @__PURE__ */ new Map();\n    const sessionsToDelete = /* @__PURE__ */ new Set();\n    for (const session of sessions) {\n      const ip = session.ip || \"\";\n      if (ip && ip !== \"127.0.0.1\" && !ip.startsWith(\"192.168.\") && !ip.startsWith(\"10.\")) {\n        if (!sessionsByIP.has(ip)) {\n          sessionsByIP.set(ip, session.id);\n        } else if (sessionsByIP.get(ip) !== session.id) {\n          sessionsToDelete.add(session.id);\n        }\n      }\n    }\n    for (const session of sessions) {\n      if (sessionsToDelete.has(session.id)) continue;\n      const deviceType = (session.device || \"\").toLowerCase();\n      if (deviceType && deviceType !== \"unknown\") {\n        if (!sessionsByDevice.has(deviceType)) {\n          sessionsByDevice.set(deviceType, session.id);\n        } else if (sessionsByDevice.get(deviceType) !== session.id) {\n          sessionsToDelete.add(session.id);\n        }\n      }\n    }\n    for (const session of sessions) {\n      if (sessionsToDelete.has(session.id)) continue;\n      const browserName = (session.browser || \"\").split(\" \")[0].toLowerCase();\n      const osName = (session.os || \"\").split(\" \")[0].toLowerCase();\n      if (browserName && osName) {\n        const browserOsKey = `${browserName}|${osName}`;\n        if (!sessionsByBrowserOS.has(browserOsKey)) {\n          sessionsByBrowserOS.set(browserOsKey, session.id);\n        } else if (sessionsByBrowserOS.get(browserOsKey) !== session.id) {\n          sessionsToDelete.add(session.id);\n        }\n      }\n    }\n    const sessionsToDeleteArray = Array.from(sessionsToDelete);\n    if (sessionsToDeleteArray.length > 0) {\n      console.log(`Deleting ${sessionsToDeleteArray.length} duplicate sessions for user ${userId}`);\n      console.log(\"Sessions to delete:\", sessionsToDeleteArray);\n      const result = await prisma.session.deleteMany({\n        where: {\n          id: { in: sessionsToDeleteArray }\n        }\n      });\n      console.log(`Successfully deleted ${result.count} duplicate sessions`);\n      return { count: result.count };\n    }\n    console.log(\"No duplicate sessions found for user\", userId);\n    return { count: 0 };\n  } catch (error) {\n    console.error(\"Error cleaning up duplicate sessions:\", error);\n    return { count: 0, error };\n  }\n}\nexport {\n  getAuthLocation as a,\n  cleanupUserSessions as b,\n  createSessionToken as c,\n  getUserFromToken as g,\n  verifySessionToken as v\n};\n"], "names": [], "mappings": ";;;;;AAIA,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,iBAAiB;AAC9D,MAAM,mBAAmB,GAAG,EAAE;AAC9B,eAAe,sBAAsB,CAAC,OAAO,EAAE;AAC/C,EAAE,IAAI,EAAE,GAAG,WAAW;AACtB,EAAE,IAAI,QAAQ,GAAG,kBAAkB;AACnC,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE;AAC3B;AACA,EAAE,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,WAAW;AAChG,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACxB,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;AAChC;AACA,EAAE,IAAI,EAAE,KAAK,WAAW,IAAI,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;AAC/E,IAAI,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE,eAAe,EAAE;AAC5C;AACA,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,CAAC,iBAAiB,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;AAChE,IAAI,IAAI,QAAQ,CAAC,EAAE,EAAE;AACrB,MAAM,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE;AACxC,MAAM,IAAI,IAAI,CAAC,KAAK,EAAE;AACtB,QAAQ,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,IAAI,CAAC,KAAK,CAAC;AAC1D,OAAO,MAAM,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;AACjD,QAAQ,QAAQ,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AACvD,OAAO,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;AACpC,QAAQ,QAAQ,GAAG,IAAI,CAAC,YAAY;AACpC;AACA;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC3D;AACA,EAAE,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE;AACzB;AACA,SAAS,aAAa,CAAC,QAAQ,EAAE;AACjC,EAAE,IAAI,CAAC,QAAQ,EAAE,OAAO,kBAAkB;AAC1C,EAAE,IAAI,QAAQ,KAAK,eAAe,EAAE,OAAO,QAAQ;AACnD,EAAE,IAAI,QAAQ,KAAK,kBAAkB,EAAE,OAAO,QAAQ;AACtD,EAAE,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE;AACjC,EAAE,MAAM,cAAc,GAAG,OAAO,CAAC,KAAK,CAAC,mCAAmC,CAAC;AAC3E,EAAE,IAAI,cAAc,EAAE;AACtB,IAAI,OAAO,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;AACrE;AACA,EAAE,MAAM,gBAAgB,GAAG,OAAO,CAAC,KAAK,CAAC,2CAA2C,CAAC;AACrF,EAAE,IAAI,gBAAgB,EAAE;AACxB,IAAI,OAAO,OAAO;AAClB;AACA,EAAE,OAAO,OAAO;AAChB;AACA,eAAe,eAAe,CAAC,OAAO,EAAE;AACxC,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,MAAM,sBAAsB,CAAC,OAAO,CAAC;AAC7E,EAAE,MAAM,QAAQ,GAAG,aAAa,CAAC,WAAW,CAAC;AAC7C,EAAE,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE;AACtC;AACA,eAAe,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE;AAC9C,EAAE,MAAM,SAAS,GAAG,OAAO,EAAE,OAAO,EAAE,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;AAC7D,EAAE,MAAM,MAAM,GAAG,IAAI,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC;AACjD,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE;AACrC,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,KAAK,EAAE;AAC3B,EAAE,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE;AACnC,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,MAAM,sBAAsB,CAAC,OAAO,CAAC;AAChE,EAAE,MAAM,SAAS,mBAAmB,IAAI,IAAI,EAAE;AAC9C,EAAE,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,mBAAmB,CAAC;AAC9D,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,UAAU,EAAE;AACjD,IAAI,SAAS,EAAE,CAAC,EAAE,mBAAmB,CAAC,CAAC;AACvC,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE,CAAC,IAAI,EAAE;AAC/D,EAAE,MAAM,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE,CAAC,IAAI,EAAE;AACrD,EAAE,MAAM,UAAU,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,SAAS,EAAE,WAAW,EAAE,CAAC,IAAI,EAAE;AACpE,EAAE,MAAM,aAAa,GAAG,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE;AAC/E,EAAE,MAAM,QAAQ,GAAG,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE;AAChE,EAAE,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,IAAI,SAAS;AAC1G,EAAE,OAAO,CAAC,GAAG;AACb,IAAI,CAAC,+BAA+B,EAAE,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC;AACnG,GAAG;AACH,EAAE,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;AACzD,IAAI,KAAK,EAAE;AACX,MAAM,MAAM;AACZ,MAAM,SAAS,EAAE,KAAK;AACtB,MAAM,OAAO,EAAE,EAAE,EAAE,kBAAkB,IAAI,IAAI,EAAE,EAAE;AACjD,MAAM,EAAE,EAAE;AACV;AACA,QAAQ,GAAG,EAAE,KAAK,WAAW,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE;AACpG;AACA,QAAQ;AACR,UAAU,OAAO,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,aAAa,EAAE;AACjE,UAAU,EAAE,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE;AACvD,UAAU,SAAS,EAAE;AACrB,SAAS;AACT;AACA,QAAQ;AACR,UAAU,MAAM,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,EAAE,aAAa,EAAE;AAC/D,UAAU,EAAE,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE;AACvD,UAAU,SAAS,EAAE;AACrB;AACA;AACA,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,UAAU,EAAE;AAClB;AACA;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,eAAe,EAAE;AACvB,IAAI,OAAO,CAAC,GAAG;AACf,MAAM,CAAC,gCAAgC,EAAE,MAAM,CAAC,6BAA6B,EAAE,eAAe,CAAC,EAAE,CAAC;AAClG,KAAK;AACL,IAAI,OAAO,CAAC,GAAG;AACf,MAAM,CAAC,oCAAoC,EAAE,eAAe,CAAC,OAAO,CAAC,MAAM,EAAE,eAAe,CAAC,EAAE,CAAC,UAAU,EAAE,eAAe,CAAC,MAAM,CAAC;AACnI,KAAK;AACL,IAAI,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;AACvD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,eAAe,CAAC,EAAE,EAAE;AACvC,MAAM,IAAI,EAAE;AACZ,QAAQ,YAAY,EAAE,KAAK;AAC3B,QAAQ,OAAO,EAAE,SAAS;AAC1B,QAAQ,UAAU,kBAAkB,IAAI,IAAI,EAAE;AAC9C,QAAQ,EAAE;AACV;AACA,QAAQ,QAAQ;AAChB;AACA,QAAQ;AACR;AACA;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,qDAAqD,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5F,IAAI,OAAO,cAAc;AACzB;AACA,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,yDAAyD,EAAE,MAAM,CAAC,CAAC,CAAC;AACnF,EAAE,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;AAC9C,IAAI,IAAI,EAAE;AACV,MAAM,MAAM;AACZ,MAAM,YAAY,EAAE,KAAK;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,SAAS;AACf,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,EAAE,EAAE,QAAQ;AAClB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,EAAE;AACR,MAAM;AACN;AACA,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/D,EAAE,OAAO,OAAO;AAChB;AACA,eAAe,kBAAkB,CAAC,IAAI,EAAE,OAAO,EAAE;AACjD,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC,EAAE,CAAC;AAC5D,IAAI,IAAI;AACR,MAAM,MAAM,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;AACxC,KAAK,CAAC,OAAO,YAAY,EAAE;AAC3B,MAAM,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,YAAY,CAAC;AACpE;AACA,IAAI,MAAM,OAAO,GAAG,MAAM,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC;AACzD,IAAI,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,OAAO,CAAC,EAAE,CAAC;AACpE,IAAI,OAAO,OAAO,CAAC,YAAY;AAC/B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC;AACzD,IAAI,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC;AACnD,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE;AACrD,MAAM,SAAS,EAAE,CAAC,EAAE,mBAAmB,CAAC,CAAC;AACzC,KAAK,CAAC;AACN;AACA;AACA,eAAe,kBAAkB,CAAC,KAAK,EAAE;AACzC,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;AACpE,MAAM,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC;AACzE,MAAM,OAAO,IAAI;AACjB;AACA,IAAI,IAAI;AACR,MAAM,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC;AACnD,MAAM,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;AACrD,QAAQ,KAAK,EAAE;AACf,UAAU,YAAY,EAAE,KAAK;AAC7B,UAAU,SAAS,EAAE,KAAK;AAC1B,UAAU,OAAO,EAAE,EAAE,EAAE,kBAAkB,IAAI,IAAI,EAAE;AACnD,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI;AAC7B,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,OAAO,EAAE;AACpB,QAAQ,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC;AAClE,QAAQ,IAAI,GAAG,EAAE;AACjB,QAAQ,OAAO,IAAI;AACnB;AACA,MAAM,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;AAClC,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;AACjC,QAAQ,IAAI,EAAE,EAAE,UAAU,kBAAkB,IAAI,IAAI,EAAE;AACtD,OAAO,CAAC;AACR,MAAM,OAAO,OAAO,CAAC,IAAI;AACzB,KAAK,CAAC,OAAO,QAAQ,EAAE;AACvB,MAAM,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,QAAQ,CAAC,OAAO,CAAC;AACjE,MAAM,OAAO,IAAI;AACjB;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,IAAI;AACf;AACA;AACA,eAAe,gBAAgB,CAAC,OAAO,EAAE;AACzC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI;AACzB,EAAE,IAAI;AACN,IAAI,OAAO,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAC1C,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,IAAI;AACf;AACA;AACA,eAAe,mBAAmB,CAAC,MAAM,EAAE;AAC3C,EAAE,IAAI;AACN,IAAI,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAC1C,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AAC1D,MAAM,KAAK,EAAE;AACb,QAAQ,MAAM;AACd,QAAQ,OAAO,EAAE,EAAE,EAAE,EAAE,GAAG;AAC1B;AACA,KAAK,CAAC;AACN,IAAI,MAAM,aAAa,mBAAmB,IAAI,IAAI,EAAE;AACpD,IAAI,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC;AACvD,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AAC1D,MAAM,KAAK,EAAE;AACb,QAAQ,MAAM;AACd,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,UAAU,EAAE,EAAE,EAAE,EAAE,aAAa;AACvC;AACA;AACA,KAAK,CAAC;AACN,IAAI,MAAM,gBAAgB,GAAG,MAAM,wBAAwB,CAAC,MAAM,CAAC;AACnE,IAAI,OAAO,CAAC,GAAG;AACf,MAAM,CAAC,WAAW,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,EAAE,aAAa,CAAC,KAAK,CAAC,cAAc,EAAE,gBAAgB,CAAC,KAAK,CAAC,6BAA6B,EAAE,MAAM,CAAC;AACrJ,KAAK;AACL,IAAI,OAAO;AACX,MAAM,OAAO,EAAE,aAAa,CAAC,KAAK;AAClC,MAAM,OAAO,EAAE,aAAa,CAAC,KAAK;AAClC,MAAM,UAAU,EAAE,gBAAgB,CAAC;AACnC,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACvD,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,KAAK,EAAE;AAC3D;AACA;AACA,eAAe,wBAAwB,CAAC,MAAM,EAAE;AAChD,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;AACnD,MAAM,KAAK,EAAE;AACb,QAAQ,MAAM;AACd,QAAQ,SAAS,EAAE,KAAK;AACxB,QAAQ,OAAO,EAAE,EAAE,EAAE,kBAAkB,IAAI,IAAI,EAAE;AACjD,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,UAAU,EAAE,MAAM;AACnC,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,0BAA0B,EAAE,MAAM,CAAC,CAAC,CAAC;AAC9E,IAAI,MAAM,YAAY,mBAAmB,IAAI,GAAG,EAAE;AAClD,IAAI,MAAM,gBAAgB,mBAAmB,IAAI,GAAG,EAAE;AACtD,IAAI,MAAM,mBAAmB,mBAAmB,IAAI,GAAG,EAAE;AACzD,IAAI,MAAM,gBAAgB,mBAAmB,IAAI,GAAG,EAAE;AACtD,IAAI,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AACpC,MAAM,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,IAAI,EAAE;AACjC,MAAM,IAAI,EAAE,IAAI,EAAE,KAAK,WAAW,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;AAC3F,QAAQ,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;AACnC,UAAU,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,CAAC;AAC1C,SAAS,MAAM,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,OAAO,CAAC,EAAE,EAAE;AACxD,UAAU,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;AAC1C;AACA;AACA;AACA,IAAI,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AACpC,MAAM,IAAI,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;AAC5C,MAAM,MAAM,UAAU,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,EAAE,WAAW,EAAE;AAC7D,MAAM,IAAI,UAAU,IAAI,UAAU,KAAK,SAAS,EAAE;AAClD,QAAQ,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;AAC/C,UAAU,gBAAgB,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,CAAC;AACtD,SAAS,MAAM,IAAI,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,OAAO,CAAC,EAAE,EAAE;AACpE,UAAU,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;AAC1C;AACA;AACA;AACA,IAAI,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AACpC,MAAM,IAAI,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;AAC5C,MAAM,MAAM,WAAW,GAAG,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;AAC7E,MAAM,MAAM,MAAM,GAAG,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;AACnE,MAAM,IAAI,WAAW,IAAI,MAAM,EAAE;AACjC,QAAQ,MAAM,YAAY,GAAG,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACvD,QAAQ,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;AACpD,UAAU,mBAAmB,CAAC,GAAG,CAAC,YAAY,EAAE,OAAO,CAAC,EAAE,CAAC;AAC3D,SAAS,MAAM,IAAI,mBAAmB,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,OAAO,CAAC,EAAE,EAAE;AACzE,UAAU,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;AAC1C;AACA;AACA;AACA,IAAI,MAAM,qBAAqB,GAAG,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC;AAC9D,IAAI,IAAI,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1C,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,qBAAqB,CAAC,MAAM,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAC,CAAC;AACnG,MAAM,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,qBAAqB,CAAC;AAC/D,MAAM,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AACrD,QAAQ,KAAK,EAAE;AACf,UAAU,EAAE,EAAE,EAAE,EAAE,EAAE,qBAAqB;AACzC;AACA,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;AAC5E,MAAM,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE;AACpC;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,MAAM,CAAC;AAC/D,IAAI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE;AACvB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC;AACjE,IAAI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE;AAC9B;AACA;;;;"}