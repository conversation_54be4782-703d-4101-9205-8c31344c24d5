import { p as push, V as copy_payload, W as assign_payload, Q as bind_props, q as pop, R as spread_props } from './index3-CqUPEnZw.js';
import { D as Dialog_trigger$1 } from './dialog-trigger-CNXm7UD7.js';

function Dialog_trigger($$payload, $$props) {
  push();
  let { ref = null, $$slots, $$events, ...restProps } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Dialog_trigger$1($$payload2, spread_props([
      { "data-slot": "dialog-trigger" },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}

export { Dialog_trigger as D };
//# sourceMappingURL=dialog-trigger2-C3bA-tk4.js.map
