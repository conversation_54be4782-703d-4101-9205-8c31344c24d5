import { p as push, R as spread_props, q as pop } from './index3-CqUPEnZw.js';
import { I as Icon } from './Icon2-DkOdBr51.js';

function Chevron_right($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [["path", { "d": "m9 18 6-6-6-6" }]];
  Icon($$payload, spread_props([
    { name: "chevron-right" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}

export { Chevron_right as C };
//# sourceMappingURL=chevron-right-C2rn-JeO.js.map
