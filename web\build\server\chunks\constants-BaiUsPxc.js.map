{"version": 3, "file": "constants-BaiUsPxc.js", "sources": ["../../../node_modules/devalue/src/base64.js", "../../../node_modules/devalue/src/constants.js"], "sourcesContent": ["/**\n * Base64 Encodes an arraybuffer\n * @param {ArrayBuffer} arraybuffer\n * @returns {string}\n */\nexport function encode64(arraybuffer) {\n  const dv = new DataView(arraybuffer);\n  let binaryString = \"\";\n\n  for (let i = 0; i < arraybuffer.byteLength; i++) {\n    binaryString += String.fromCharCode(dv.getUint8(i));\n  }\n\n  return binaryToAscii(binaryString);\n}\n\n/**\n * Decodes a base64 string into an arraybuffer\n * @param {string} string\n * @returns {ArrayBuffer}\n */\nexport function decode64(string) {\n  const binaryString = asciiToBinary(string);\n  const arraybuffer = new ArrayBuffer(binaryString.length);\n  const dv = new DataView(arraybuffer);\n\n  for (let i = 0; i < arraybuffer.byteLength; i++) {\n    dv.setUint8(i, binaryString.charCodeAt(i));\n  }\n\n  return arraybuffer;\n}\n\nconst KEY_STRING =\n  \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdef<PERSON>ijklmnopqrstuvwxyz0123456789+/\";\n\n/**\n * Substitute for atob since it's deprecated in node.\n * Does not do any input validation.\n *\n * @see https://github.com/jsdom/abab/blob/master/lib/atob.js\n *\n * @param {string} data\n * @returns {string}\n */\nfunction asciiToBinary(data) {\n  if (data.length % 4 === 0) {\n    data = data.replace(/==?$/, \"\");\n  }\n\n  let output = \"\";\n  let buffer = 0;\n  let accumulatedBits = 0;\n\n  for (let i = 0; i < data.length; i++) {\n    buffer <<= 6;\n    buffer |= KEY_STRING.indexOf(data[i]);\n    accumulatedBits += 6;\n    if (accumulatedBits === 24) {\n      output += String.fromCharCode((buffer & 0xff0000) >> 16);\n      output += String.fromCharCode((buffer & 0xff00) >> 8);\n      output += String.fromCharCode(buffer & 0xff);\n      buffer = accumulatedBits = 0;\n    }\n  }\n  if (accumulatedBits === 12) {\n    buffer >>= 4;\n    output += String.fromCharCode(buffer);\n  } else if (accumulatedBits === 18) {\n    buffer >>= 2;\n    output += String.fromCharCode((buffer & 0xff00) >> 8);\n    output += String.fromCharCode(buffer & 0xff);\n  }\n  return output;\n}\n\n/**\n * Substitute for btoa since it's deprecated in node.\n * Does not do any input validation.\n *\n * @see https://github.com/jsdom/abab/blob/master/lib/btoa.js\n *\n * @param {string} str\n * @returns {string}\n */\nfunction binaryToAscii(str) {\n  let out = \"\";\n  for (let i = 0; i < str.length; i += 3) {\n    /** @type {[number, number, number, number]} */\n    const groupsOfSix = [undefined, undefined, undefined, undefined];\n    groupsOfSix[0] = str.charCodeAt(i) >> 2;\n    groupsOfSix[1] = (str.charCodeAt(i) & 0x03) << 4;\n    if (str.length > i + 1) {\n      groupsOfSix[1] |= str.charCodeAt(i + 1) >> 4;\n      groupsOfSix[2] = (str.charCodeAt(i + 1) & 0x0f) << 2;\n    }\n    if (str.length > i + 2) {\n      groupsOfSix[2] |= str.charCodeAt(i + 2) >> 6;\n      groupsOfSix[3] = str.charCodeAt(i + 2) & 0x3f;\n    }\n    for (let j = 0; j < groupsOfSix.length; j++) {\n      if (typeof groupsOfSix[j] === \"undefined\") {\n        out += \"=\";\n      } else {\n        out += KEY_STRING[groupsOfSix[j]];\n      }\n    }\n  }\n  return out;\n}\n", "export const UNDEFINED = -1;\nexport const HOLE = -2;\nexport const NAN = -3;\nexport const POSITIVE_INFINITY = -4;\nexport const NEGATIVE_INFINITY = -5;\nexport const NEGATIVE_ZERO = -6;\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,WAAW,EAAE;AACtC,EAAE,MAAM,EAAE,GAAG,IAAI,QAAQ,CAAC,WAAW,CAAC;AACtC,EAAE,IAAI,YAAY,GAAG,EAAE;;AAEvB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;AACnD,IAAI,YAAY,IAAI,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AACvD;;AAEA,EAAE,OAAO,aAAa,CAAC,YAAY,CAAC;AACpC;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,MAAM,EAAE;AACjC,EAAE,MAAM,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC;AAC5C,EAAE,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC;AAC1D,EAAE,MAAM,EAAE,GAAG,IAAI,QAAQ,CAAC,WAAW,CAAC;;AAEtC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;AACnD,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC9C;;AAEA,EAAE,OAAO,WAAW;AACpB;;AAEA,MAAM,UAAU;AAChB,EAAE,kEAAkE;;AAEpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,aAAa,CAAC,IAAI,EAAE;AAC7B,EAAE,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;AAC7B,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;AACnC;;AAEA,EAAE,IAAI,MAAM,GAAG,EAAE;AACjB,EAAE,IAAI,MAAM,GAAG,CAAC;AAChB,EAAE,IAAI,eAAe,GAAG,CAAC;;AAEzB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,IAAI,MAAM,KAAK,CAAC;AAChB,IAAI,MAAM,IAAI,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACzC,IAAI,eAAe,IAAI,CAAC;AACxB,IAAI,IAAI,eAAe,KAAK,EAAE,EAAE;AAChC,MAAM,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,MAAM,GAAG,QAAQ,KAAK,EAAE,CAAC;AAC9D,MAAM,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,CAAC;AAC3D,MAAM,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;AAClD,MAAM,MAAM,GAAG,eAAe,GAAG,CAAC;AAClC;AACA;AACA,EAAE,IAAI,eAAe,KAAK,EAAE,EAAE;AAC9B,IAAI,MAAM,KAAK,CAAC;AAChB,IAAI,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AACzC,GAAG,MAAM,IAAI,eAAe,KAAK,EAAE,EAAE;AACrC,IAAI,MAAM,KAAK,CAAC;AAChB,IAAI,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,CAAC;AACzD,IAAI,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;AAChD;AACA,EAAE,OAAO,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,aAAa,CAAC,GAAG,EAAE;AAC5B,EAAE,IAAI,GAAG,GAAG,EAAE;AACd,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AAC1C;AACA,IAAI,MAAM,WAAW,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;AACpE,IAAI,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC;AAC3C,IAAI,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;AACpD,IAAI,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE;AAC5B,MAAM,WAAW,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;AAClD,MAAM,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;AAC1D;AACA,IAAI,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE;AAC5B,MAAM,WAAW,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;AAClD,MAAM,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;AACnD;AACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,MAAM,IAAI,OAAO,WAAW,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;AACjD,QAAQ,GAAG,IAAI,GAAG;AAClB,OAAO,MAAM;AACb,QAAQ,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACzC;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ;;AC7GY,MAAC,SAAS,GAAG;AACb,MAAC,IAAI,GAAG;AACR,MAAC,GAAG,GAAG;AACP,MAAC,iBAAiB,GAAG;AACrB,MAAC,iBAAiB,GAAG;AACrB,MAAC,aAAa,GAAG;;;;", "x_google_ignoreList": [0, 1]}