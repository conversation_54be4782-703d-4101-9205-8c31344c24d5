import { X as sanitize_props, R as spread_props, a0 as slot } from './index3-CqUPEnZw.js';
import { I as Icon } from './Icon-A4vzmk-O.js';

function Cookie($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    [
      "path",
      {
        "d": "M12 2a10 10 0 1 0 10 10 4 4 0 0 1-5-5 4 4 0 0 1-5-5"
      }
    ],
    ["path", { "d": "M8.5 8.5v.01" }],
    ["path", { "d": "M16 15.5v.01" }],
    ["path", { "d": "M12 12v.01" }],
    ["path", { "d": "M11 17v.01" }],
    ["path", { "d": "M7 14v.01" }]
  ];
  Icon($$payload, spread_props([
    { name: "cookie" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}

export { Cookie as C };
//# sourceMappingURL=cookie-CTvRQAsw.js.map
