{"version": 3, "file": "profileHelpers-m3Uw-RPd.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/profileHelpers.js"], "sourcesContent": ["function parseProfileData(data) {\n  try {\n    if (!data) return {};\n    if (typeof data === \"string\") {\n      return JSON.parse(data);\n    }\n    if (data.data && typeof data.data === \"string\") {\n      return JSON.parse(data.data);\n    }\n    if (data.data && typeof data.data === \"object\") {\n      return data.data;\n    }\n    return data;\n  } catch (err) {\n    console.error(\"Error parsing profile data:\", err);\n    return {};\n  }\n}\nfunction migrateProfileData(data) {\n  const migratedData = { ...data };\n  if (!migratedData.personalInfo) {\n    migratedData.personalInfo = {\n      fullName: data.fullName,\n      email: data.email,\n      phone: data.phone,\n      location: data.location,\n      website: data.website,\n      summary: data.summary,\n      jobTitle: data.jobType\n    };\n  }\n  if (!migratedData.skillsData && data.skills) {\n    let skillsArray = [];\n    if (Array.isArray(data.skills)) {\n      skillsArray = data.skills;\n    } else if (typeof data.skills === \"string\") {\n      skillsArray = data.skills.split(\",\").map((s) => s.trim()).filter(Boolean);\n    }\n    migratedData.skillsData = {\n      list: skillsArray,\n      technical: skillsArray\n    };\n  }\n  if (!migratedData.workExperience) migratedData.workExperience = [];\n  if (!migratedData.education) migratedData.education = [];\n  if (!migratedData.projects) migratedData.projects = [];\n  if (!migratedData.certifications) migratedData.certifications = [];\n  if (!migratedData.languages) migratedData.languages = [];\n  if (!migratedData.achievements) migratedData.achievements = [];\n  if (!migratedData.jobPreferences) {\n    migratedData.jobPreferences = {\n      valueInRole: [],\n      interestedRoles: [],\n      roleSpecializations: [],\n      preferredLocations: [],\n      experienceLevel: \"\",\n      companySize: \"\",\n      desiredIndustries: [],\n      avoidIndustries: [],\n      preferredSkills: [],\n      avoidSkills: [],\n      minimumSalary: \"\",\n      securityClearance: \"\",\n      jobSearchStatus: \"\"\n    };\n  }\n  return migratedData;\n}\nfunction calculateProfileCompletion(profileData) {\n  let completed = 0;\n  const total = 7;\n  const hasPersonalInfo = profileData.personalInfo?.fullName || profileData.fullName || profileData.header?.fullName;\n  if (hasPersonalInfo) completed++;\n  const hasSummary = profileData.personalInfo?.summary || profileData.summary || profileData.personalInfo?.headline || profileData.header?.headline;\n  if (hasSummary) completed++;\n  const hasExperience = profileData.workExperience && profileData.workExperience.length > 0 || profileData.workExperiences && profileData.workExperiences.length > 0;\n  if (hasExperience) completed++;\n  const hasEducation = profileData.education && profileData.education.length > 0 || profileData.educations && profileData.educations.length > 0;\n  if (hasEducation) completed++;\n  const hasSkills = profileData.skillsData?.list && profileData.skillsData.list.length > 0 || Array.isArray(profileData.skills) && profileData.skills.length > 0 || profileData.skills?.skills && profileData.skills.skills.length > 0;\n  if (hasSkills) completed++;\n  const hasProjects = profileData.projects && profileData.projects.length > 0;\n  if (hasProjects) completed++;\n  const hasJobPreferences = profileData.jobPreferences?.preferredLocations && profileData.jobPreferences.preferredLocations.length > 0 || profileData.jobPreferences?.interestedRoles && profileData.jobPreferences.interestedRoles.length > 0;\n  if (hasJobPreferences) completed++;\n  return Math.round(completed / total * 100);\n}\nfunction checkAutomationEligibility(profile, minCompletionPercentage = 70) {\n  const missingRequirements = [];\n  if (!profile.data?.data) {\n    return {\n      isEligible: false,\n      completionPercentage: 0,\n      missingRequirements: [\"Profile data is missing\"],\n      hasResume: false\n    };\n  }\n  let profileData = profile.data.data;\n  if (typeof profileData === \"string\") {\n    try {\n      profileData = JSON.parse(profileData);\n    } catch (e) {\n      console.error(\"Error parsing profile data JSON:\", e);\n      return {\n        isEligible: false,\n        completionPercentage: 0,\n        missingRequirements: [\"Profile data is corrupted\"],\n        hasResume: false\n      };\n    }\n  }\n  const completionPercentage = calculateProfileCompletion(profileData);\n  if (completionPercentage < minCompletionPercentage) {\n    missingRequirements.push(\n      `Profile completion is ${completionPercentage}%, minimum required is ${minCompletionPercentage}%`\n    );\n  }\n  const hasResume = profile.documents && profile.documents.length > 0;\n  if (!hasResume) {\n    missingRequirements.push(\"At least one resume is required\");\n  }\n  if (!profileData.personalInfo?.fullName && !profileData.fullName) {\n    missingRequirements.push(\"Full name is required\");\n  }\n  if (!profileData.personalInfo?.email && !profileData.email) {\n    missingRequirements.push(\"Email address is required\");\n  }\n  const hasSkills = profileData.skillsData?.list && profileData.skillsData.list.length > 0 || Array.isArray(profileData.skills) && profileData.skills.length > 0;\n  if (!hasSkills) {\n    missingRequirements.push(\"Skills are required for job matching\");\n  }\n  if (!profileData.workExperience || profileData.workExperience.length === 0) {\n    missingRequirements.push(\"Work experience is required for job matching\");\n  }\n  const isEligible = missingRequirements.length === 0;\n  return {\n    isEligible,\n    completionPercentage,\n    missingRequirements,\n    hasResume\n  };\n}\nexport {\n  checkAutomationEligibility as a,\n  calculateProfileCompletion as c,\n  migrateProfileData as m,\n  parseProfileData as p\n};\n"], "names": [], "mappings": "AAAA,SAAS,gBAAgB,CAAC,IAAI,EAAE;AAChC,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE;AACxB,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAClC,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;AAC7B;AACA,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;AACpD,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;AAClC;AACA,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;AACpD,MAAM,OAAO,IAAI,CAAC,IAAI;AACtB;AACA,IAAI,OAAO,IAAI;AACf,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,GAAG,CAAC;AACrD,IAAI,OAAO,EAAE;AACb;AACA;AACA,SAAS,kBAAkB,CAAC,IAAI,EAAE;AAClC,EAAE,MAAM,YAAY,GAAG,EAAE,GAAG,IAAI,EAAE;AAClC,EAAE,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE;AAClC,IAAI,YAAY,CAAC,YAAY,GAAG;AAChC,MAAM,QAAQ,EAAE,IAAI,CAAC,QAAQ;AAC7B,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK;AACvB,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK;AACvB,MAAM,QAAQ,EAAE,IAAI,CAAC,QAAQ;AAC7B,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;AAC3B,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;AAC3B,MAAM,QAAQ,EAAE,IAAI,CAAC;AACrB,KAAK;AACL;AACA,EAAE,IAAI,CAAC,YAAY,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,EAAE;AAC/C,IAAI,IAAI,WAAW,GAAG,EAAE;AACxB,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AACpC,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM;AAC/B,KAAK,MAAM,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE;AAChD,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/E;AACA,IAAI,YAAY,CAAC,UAAU,GAAG;AAC9B,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,SAAS,EAAE;AACjB,KAAK;AACL;AACA,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,YAAY,CAAC,cAAc,GAAG,EAAE;AACpE,EAAE,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,SAAS,GAAG,EAAE;AAC1D,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,YAAY,CAAC,QAAQ,GAAG,EAAE;AACxD,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,YAAY,CAAC,cAAc,GAAG,EAAE;AACpE,EAAE,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,SAAS,GAAG,EAAE;AAC1D,EAAE,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC,YAAY,GAAG,EAAE;AAChE,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE;AACpC,IAAI,YAAY,CAAC,cAAc,GAAG;AAClC,MAAM,WAAW,EAAE,EAAE;AACrB,MAAM,eAAe,EAAE,EAAE;AACzB,MAAM,mBAAmB,EAAE,EAAE;AAC7B,MAAM,kBAAkB,EAAE,EAAE;AAC5B,MAAM,eAAe,EAAE,EAAE;AACzB,MAAM,WAAW,EAAE,EAAE;AACrB,MAAM,iBAAiB,EAAE,EAAE;AAC3B,MAAM,eAAe,EAAE,EAAE;AACzB,MAAM,eAAe,EAAE,EAAE;AACzB,MAAM,WAAW,EAAE,EAAE;AACrB,MAAM,aAAa,EAAE,EAAE;AACvB,MAAM,iBAAiB,EAAE,EAAE;AAC3B,MAAM,eAAe,EAAE;AACvB,KAAK;AACL;AACA,EAAE,OAAO,YAAY;AACrB;AACA,SAAS,0BAA0B,CAAC,WAAW,EAAE;AACjD,EAAE,IAAI,SAAS,GAAG,CAAC;AACnB,EAAE,MAAM,KAAK,GAAG,CAAC;AACjB,EAAE,MAAM,eAAe,GAAG,WAAW,CAAC,YAAY,EAAE,QAAQ,IAAI,WAAW,CAAC,QAAQ,IAAI,WAAW,CAAC,MAAM,EAAE,QAAQ;AACpH,EAAE,IAAI,eAAe,EAAE,SAAS,EAAE;AAClC,EAAE,MAAM,UAAU,GAAG,WAAW,CAAC,YAAY,EAAE,OAAO,IAAI,WAAW,CAAC,OAAO,IAAI,WAAW,CAAC,YAAY,EAAE,QAAQ,IAAI,WAAW,CAAC,MAAM,EAAE,QAAQ;AACnJ,EAAE,IAAI,UAAU,EAAE,SAAS,EAAE;AAC7B,EAAE,MAAM,aAAa,GAAG,WAAW,CAAC,cAAc,IAAI,WAAW,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,CAAC,eAAe,IAAI,WAAW,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC;AACpK,EAAE,IAAI,aAAa,EAAE,SAAS,EAAE;AAChC,EAAE,MAAM,YAAY,GAAG,WAAW,CAAC,SAAS,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,CAAC,UAAU,IAAI,WAAW,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC;AAC/I,EAAE,IAAI,YAAY,EAAE,SAAS,EAAE;AAC/B,EAAE,MAAM,SAAS,GAAG,WAAW,CAAC,UAAU,EAAE,IAAI,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,CAAC,MAAM,EAAE,MAAM,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC;AACtO,EAAE,IAAI,SAAS,EAAE,SAAS,EAAE;AAC5B,EAAE,MAAM,WAAW,GAAG,WAAW,CAAC,QAAQ,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;AAC7E,EAAE,IAAI,WAAW,EAAE,SAAS,EAAE;AAC9B,EAAE,MAAM,iBAAiB,GAAG,WAAW,CAAC,cAAc,EAAE,kBAAkB,IAAI,WAAW,CAAC,cAAc,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,CAAC,cAAc,EAAE,eAAe,IAAI,WAAW,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC;AAC9O,EAAE,IAAI,iBAAiB,EAAE,SAAS,EAAE;AACpC,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,GAAG,GAAG,CAAC;AAC5C;AACA,SAAS,0BAA0B,CAAC,OAAO,EAAE,uBAAuB,GAAG,EAAE,EAAE;AAC3E,EAAE,MAAM,mBAAmB,GAAG,EAAE;AAChC,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE;AAC3B,IAAI,OAAO;AACX,MAAM,UAAU,EAAE,KAAK;AACvB,MAAM,oBAAoB,EAAE,CAAC;AAC7B,MAAM,mBAAmB,EAAE,CAAC,yBAAyB,CAAC;AACtD,MAAM,SAAS,EAAE;AACjB,KAAK;AACL;AACA,EAAE,IAAI,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI;AACrC,EAAE,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;AACvC,IAAI,IAAI;AACR,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;AAC3C,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,MAAM,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,CAAC,CAAC;AAC1D,MAAM,OAAO;AACb,QAAQ,UAAU,EAAE,KAAK;AACzB,QAAQ,oBAAoB,EAAE,CAAC;AAC/B,QAAQ,mBAAmB,EAAE,CAAC,2BAA2B,CAAC;AAC1D,QAAQ,SAAS,EAAE;AACnB,OAAO;AACP;AACA;AACA,EAAE,MAAM,oBAAoB,GAAG,0BAA0B,CAAC,WAAW,CAAC;AACtE,EAAE,IAAI,oBAAoB,GAAG,uBAAuB,EAAE;AACtD,IAAI,mBAAmB,CAAC,IAAI;AAC5B,MAAM,CAAC,sBAAsB,EAAE,oBAAoB,CAAC,uBAAuB,EAAE,uBAAuB,CAAC,CAAC;AACtG,KAAK;AACL;AACA,EAAE,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC;AACrE,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,mBAAmB,CAAC,IAAI,CAAC,iCAAiC,CAAC;AAC/D;AACA,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,QAAQ,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;AACpE,IAAI,mBAAmB,CAAC,IAAI,CAAC,uBAAuB,CAAC;AACrD;AACA,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;AAC9D,IAAI,mBAAmB,CAAC,IAAI,CAAC,2BAA2B,CAAC;AACzD;AACA,EAAE,MAAM,SAAS,GAAG,WAAW,CAAC,UAAU,EAAE,IAAI,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC;AAChK,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,mBAAmB,CAAC,IAAI,CAAC,sCAAsC,CAAC;AACpE;AACA,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc,IAAI,WAAW,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;AAC9E,IAAI,mBAAmB,CAAC,IAAI,CAAC,8CAA8C,CAAC;AAC5E;AACA,EAAE,MAAM,UAAU,GAAG,mBAAmB,CAAC,MAAM,KAAK,CAAC;AACrD,EAAE,OAAO;AACT,IAAI,UAAU;AACd,IAAI,oBAAoB;AACxB,IAAI,mBAAmB;AACvB,IAAI;AACJ,GAAG;AACH;;;;"}