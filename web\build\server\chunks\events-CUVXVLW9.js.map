{"version": 3, "file": "events-CUVXVLW9.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/events.js"], "sourcesContent": ["import { D as set_active_reaction, E as set_active_effect, F as active_reaction, a as active_effect, G as queue_micro_task, t as define_property, I as is_array } from \"./index3.js\";\nfunction without_reactive_context(fn) {\n  var previous_reaction = active_reaction;\n  var previous_effect = active_effect;\n  set_active_reaction(null);\n  set_active_effect(null);\n  try {\n    return fn();\n  } finally {\n    set_active_reaction(previous_reaction);\n    set_active_effect(previous_effect);\n  }\n}\nconst all_registered_events = /* @__PURE__ */ new Set();\nconst root_event_handles = /* @__PURE__ */ new Set();\nfunction create_event(event_name, dom, handler, options = {}) {\n  function target_handler(event) {\n    if (!options.capture) {\n      handle_event_propagation.call(dom, event);\n    }\n    if (!event.cancelBubble) {\n      return without_reactive_context(() => {\n        return handler?.call(this, event);\n      });\n    }\n  }\n  if (event_name.startsWith(\"pointer\") || event_name.startsWith(\"touch\") || event_name === \"wheel\") {\n    queue_micro_task(() => {\n      dom.addEventListener(event_name, target_handler, options);\n    });\n  } else {\n    dom.addEventListener(event_name, target_handler, options);\n  }\n  return target_handler;\n}\nfunction on(element, type, handler, options = {}) {\n  var target_handler = create_event(type, element, handler, options);\n  return () => {\n    element.removeEventListener(type, target_handler, options);\n  };\n}\nfunction handle_event_propagation(event) {\n  var handler_element = this;\n  var owner_document = (\n    /** @type {Node} */\n    handler_element.ownerDocument\n  );\n  var event_name = event.type;\n  var path = event.composedPath?.() || [];\n  var current_target = (\n    /** @type {null | Element} */\n    path[0] || event.target\n  );\n  var path_idx = 0;\n  var handled_at = event.__root;\n  if (handled_at) {\n    var at_idx = path.indexOf(handled_at);\n    if (at_idx !== -1 && (handler_element === document || handler_element === /** @type {any} */\n    window)) {\n      event.__root = handler_element;\n      return;\n    }\n    var handler_idx = path.indexOf(handler_element);\n    if (handler_idx === -1) {\n      return;\n    }\n    if (at_idx <= handler_idx) {\n      path_idx = at_idx;\n    }\n  }\n  current_target = /** @type {Element} */\n  path[path_idx] || event.target;\n  if (current_target === handler_element) return;\n  define_property(event, \"currentTarget\", {\n    configurable: true,\n    get() {\n      return current_target || owner_document;\n    }\n  });\n  var previous_reaction = active_reaction;\n  var previous_effect = active_effect;\n  set_active_reaction(null);\n  set_active_effect(null);\n  try {\n    var throw_error;\n    var other_errors = [];\n    while (current_target !== null) {\n      var parent_element = current_target.assignedSlot || current_target.parentNode || /** @type {any} */\n      current_target.host || null;\n      try {\n        var delegated = current_target[\"__\" + event_name];\n        if (delegated != null && (!/** @type {any} */\n        current_target.disabled || // DOM could've been updated already by the time this is reached, so we check this as well\n        // -> the target could not have been disabled because it emits the event in the first place\n        event.target === current_target)) {\n          if (is_array(delegated)) {\n            var [fn, ...data] = delegated;\n            fn.apply(current_target, [event, ...data]);\n          } else {\n            delegated.call(current_target, event);\n          }\n        }\n      } catch (error) {\n        if (throw_error) {\n          other_errors.push(error);\n        } else {\n          throw_error = error;\n        }\n      }\n      if (event.cancelBubble || parent_element === handler_element || parent_element === null) {\n        break;\n      }\n      current_target = parent_element;\n    }\n    if (throw_error) {\n      for (let error of other_errors) {\n        queueMicrotask(() => {\n          throw error;\n        });\n      }\n      throw throw_error;\n    }\n  } finally {\n    event.__root = handler_element;\n    delete event.currentTarget;\n    set_active_reaction(previous_reaction);\n    set_active_effect(previous_effect);\n  }\n}\nexport {\n  all_registered_events as a,\n  handle_event_propagation as h,\n  on as o,\n  root_event_handles as r\n};\n"], "names": [], "mappings": ";;AACA,SAAS,wBAAwB,CAAC,EAAE,EAAE;AACtC,EAAE,IAAI,iBAAiB,GAAG,eAAe;AACzC,EAAE,IAAI,eAAe,GAAG,aAAa;AACrC,EAAE,mBAAmB,CAAC,IAAI,CAAC;AAC3B,EAAE,iBAAiB,CAAC,IAAI,CAAC;AACzB,EAAE,IAAI;AACN,IAAI,OAAO,EAAE,EAAE;AACf,GAAG,SAAS;AACZ,IAAI,mBAAmB,CAAC,iBAAiB,CAAC;AAC1C,IAAI,iBAAiB,CAAC,eAAe,CAAC;AACtC;AACA;AACK,MAAC,qBAAqB,mBAAmB,IAAI,GAAG;AAChD,MAAC,kBAAkB,mBAAmB,IAAI,GAAG;AAClD,SAAS,YAAY,CAAC,UAAU,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,GAAG,EAAE,EAAE;AAC9D,EAAE,SAAS,cAAc,CAAC,KAAK,EAAE;AACjC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AAC1B,MAAM,wBAAwB,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC;AAC/C;AACA,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;AAC7B,MAAM,OAAO,wBAAwB,CAAC,MAAM;AAC5C,QAAQ,OAAO,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC;AACzC,OAAO,CAAC;AACR;AACA;AACA,EAAE,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,UAAU,KAAK,OAAO,EAAE;AACpG,IAAI,gBAAgB,CAAC,MAAM;AAC3B,MAAM,GAAG,CAAC,gBAAgB,CAAC,UAAU,EAAE,cAAc,EAAE,OAAO,CAAC;AAC/D,KAAK,CAAC;AACN,GAAG,MAAM;AACT,IAAI,GAAG,CAAC,gBAAgB,CAAC,UAAU,EAAE,cAAc,EAAE,OAAO,CAAC;AAC7D;AACA,EAAE,OAAO,cAAc;AACvB;AACA,SAAS,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,GAAG,EAAE,EAAE;AAClD,EAAE,IAAI,cAAc,GAAG,YAAY,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;AACpE,EAAE,OAAO,MAAM;AACf,IAAI,OAAO,CAAC,mBAAmB,CAAC,IAAI,EAAE,cAAc,EAAE,OAAO,CAAC;AAC9D,GAAG;AACH;AACA,SAAS,wBAAwB,CAAC,KAAK,EAAE;AACzC,EAAE,IAAI,eAAe,GAAG,IAAI;AAC5B,EAAE,IAAI,cAAc;AACpB;AACA,IAAI,eAAe,CAAC;AACpB,GAAG;AACH,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI;AAC7B,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,YAAY,IAAI,IAAI,EAAE;AACzC,EAAE,IAAI,cAAc;AACpB;AACA,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;AACrB,GAAG;AACH,EAAE,IAAI,QAAQ,GAAG,CAAC;AAClB,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,MAAM;AAC/B,EAAE,IAAI,UAAU,EAAE;AAClB,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;AACzC,IAAI,IAAI,MAAM,KAAK,EAAE,KAAK,eAAe,KAAK,QAAQ,IAAI,eAAe;AACzE,IAAI,MAAM,CAAC,EAAE;AACb,MAAM,KAAK,CAAC,MAAM,GAAG,eAAe;AACpC,MAAM;AACN;AACA,IAAI,IAAI,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;AACnD,IAAI,IAAI,WAAW,KAAK,EAAE,EAAE;AAC5B,MAAM;AACN;AACA,IAAI,IAAI,MAAM,IAAI,WAAW,EAAE;AAC/B,MAAM,QAAQ,GAAG,MAAM;AACvB;AACA;AACA,EAAE,cAAc;AAChB,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,MAAM;AAChC,EAAE,IAAI,cAAc,KAAK,eAAe,EAAE;AAC1C,EAAE,eAAe,CAAC,KAAK,EAAE,eAAe,EAAE;AAC1C,IAAI,YAAY,EAAE,IAAI;AACtB,IAAI,GAAG,GAAG;AACV,MAAM,OAAO,cAAc,IAAI,cAAc;AAC7C;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,iBAAiB,GAAG,eAAe;AACzC,EAAE,IAAI,eAAe,GAAG,aAAa;AACrC,EAAE,mBAAmB,CAAC,IAAI,CAAC;AAC3B,EAAE,iBAAiB,CAAC,IAAI,CAAC;AACzB,EAAE,IAAI;AACN,IAAI,IAAI,WAAW;AACnB,IAAI,IAAI,YAAY,GAAG,EAAE;AACzB,IAAI,OAAO,cAAc,KAAK,IAAI,EAAE;AACpC,MAAM,IAAI,cAAc,GAAG,cAAc,CAAC,YAAY,IAAI,cAAc,CAAC,UAAU;AACnF,MAAM,cAAc,CAAC,IAAI,IAAI,IAAI;AACjC,MAAM,IAAI;AACV,QAAQ,IAAI,SAAS,GAAG,cAAc,CAAC,IAAI,GAAG,UAAU,CAAC;AACzD,QAAQ,IAAI,SAAS,IAAI,IAAI,KAAK;AAClC,QAAQ,cAAc,CAAC,QAAQ;AAC/B;AACA,QAAQ,KAAK,CAAC,MAAM,KAAK,cAAc,CAAC,EAAE;AAC1C,UAAU,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;AACnC,YAAY,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,GAAG,SAAS;AACzC,YAAY,EAAE,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;AACtD,WAAW,MAAM;AACjB,YAAY,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC;AACjD;AACA;AACA,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,IAAI,WAAW,EAAE;AACzB,UAAU,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;AAClC,SAAS,MAAM;AACf,UAAU,WAAW,GAAG,KAAK;AAC7B;AACA;AACA,MAAM,IAAI,KAAK,CAAC,YAAY,IAAI,cAAc,KAAK,eAAe,IAAI,cAAc,KAAK,IAAI,EAAE;AAC/F,QAAQ;AACR;AACA,MAAM,cAAc,GAAG,cAAc;AACrC;AACA,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,KAAK,IAAI,KAAK,IAAI,YAAY,EAAE;AACtC,QAAQ,cAAc,CAAC,MAAM;AAC7B,UAAU,MAAM,KAAK;AACrB,SAAS,CAAC;AACV;AACA,MAAM,MAAM,WAAW;AACvB;AACA,GAAG,SAAS;AACZ,IAAI,KAAK,CAAC,MAAM,GAAG,eAAe;AAClC,IAAI,OAAO,KAAK,CAAC,aAAa;AAC9B,IAAI,mBAAmB,CAAC,iBAAiB,CAAC;AAC1C,IAAI,iBAAiB,CAAC,eAAe,CAAC;AACtC;AACA;;;;"}