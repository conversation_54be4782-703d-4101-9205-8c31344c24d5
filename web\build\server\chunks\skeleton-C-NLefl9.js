import { p as push, Z as spread_attributes, _ as clsx, Q as bind_props, q as pop } from './index3-CqUPEnZw.js';
import { c as cn } from './utils-pWl1tgmi.js';

function Skeleton($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out += `<div${spread_attributes(
    {
      "data-slot": "skeleton",
      class: clsx(cn("bg-accent animate-pulse rounded-md", className)),
      ...restProps
    },
    null
  )}></div>`;
  bind_props($$props, { ref });
  pop();
}

export { Skeleton as S };
//# sourceMappingURL=skeleton-C-NLefl9.js.map
