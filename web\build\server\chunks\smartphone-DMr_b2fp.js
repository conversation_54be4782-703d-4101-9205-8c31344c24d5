import { X as sanitize_props, R as spread_props, a0 as slot } from './index3-CqUPEnZw.js';
import { I as Icon } from './Icon-A4vzmk-O.js';

function Computer($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    [
      "rect",
      {
        "width": "14",
        "height": "8",
        "x": "5",
        "y": "2",
        "rx": "2"
      }
    ],
    [
      "rect",
      {
        "width": "20",
        "height": "8",
        "x": "2",
        "y": "14",
        "rx": "2"
      }
    ],
    ["path", { "d": "M6 18h2" }],
    ["path", { "d": "M12 18h6" }]
  ];
  Icon($$payload, spread_props([
    { name: "computer" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}
function Fingerprint($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    [
      "path",
      {
        "d": "M12 10a2 2 0 0 0-2 2c0 1.02-.1 2.51-.26 4"
      }
    ],
    [
      "path",
      { "d": "M14 13.12c0 2.38 0 6.38-1 8.88" }
    ],
    [
      "path",
      { "d": "M17.29 21.02c.12-.6.43-2.3.5-3.02" }
    ],
    ["path", { "d": "M2 12a10 10 0 0 1 18-6" }],
    ["path", { "d": "M2 16h.01" }],
    [
      "path",
      { "d": "M21.8 16c.2-2 .131-5.354 0-6" }
    ],
    [
      "path",
      {
        "d": "M5 19.5C5.5 18 6 15 6 12a6 6 0 0 1 .34-2"
      }
    ],
    [
      "path",
      { "d": "M8.65 22c.21-.66.45-1.32.57-2" }
    ],
    ["path", { "d": "M9 6.8a6 6 0 0 1 9 5.2v2" }]
  ];
  Icon($$payload, spread_props([
    { name: "fingerprint" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}
function Key($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    [
      "path",
      {
        "d": "m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4"
      }
    ],
    ["path", { "d": "m21 2-9.6 9.6" }],
    [
      "circle",
      { "cx": "7.5", "cy": "15.5", "r": "5.5" }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "key" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}
function Smartphone($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    [
      "rect",
      {
        "width": "14",
        "height": "20",
        "x": "5",
        "y": "2",
        "rx": "2",
        "ry": "2"
      }
    ],
    ["path", { "d": "M12 18h.01" }]
  ];
  Icon($$payload, spread_props([
    { name: "smartphone" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}

export { Computer as C, Fingerprint as F, Key as K, Smartphone as S };
//# sourceMappingURL=smartphone-DMr_b2fp.js.map
