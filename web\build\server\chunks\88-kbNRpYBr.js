import { v as verifySessionToken } from './auth-BPad-IlN.js';
import { i as initializePlansInDatabase, a as getPlansFromDatabase } from './plan-sync-CZNz1Ayv.js';
import './prisma-Cit_HrSw.js';
import '@prisma/client';
import 'jsonwebtoken';
import 'ua-parser-js';
import './index4-HpJcNJHQ.js';
import './false-CRHihH2U.js';
import './features-SWeUHekJ.js';
import './stripe-Dj5-FP5W.js';
import './stripe.esm.node-BpZO3rKl.js';
import 'crypto';
import 'events';
import 'http';
import 'https';
import './_commonjsHelpers-BFTU3MAI.js';
import 'util';
import 'child_process';

const load = async ({ cookies, locals, url }) => {
  const token = cookies.get("auth_token");
  if (token) {
    const user = await verifySessionToken(token);
    if (user) {
      locals.user = user;
    }
  }
  if (!locals.user) {
    locals.user = null;
  }
  const preselectedPlanId = url.searchParams.get("plan");
  const preselectedBillingCycle = url.searchParams.get("billing") === "annual" ? "annual" : "monthly";
  const preselectedSection = url.searchParams.get("section") === "teams" ? "teams" : "pro";
  await initializePlansInDatabase();
  let plans;
  try {
    plans = await getPlansFromDatabase();
    console.log("Plans from database:", plans);
    console.log("Number of plans:", plans.length);
    console.log(
      "Individual plans:",
      plans.filter((p) => p.section === "pro").map((p) => p.id)
    );
    const freePlanExists = plans.some((p) => p.id === "free");
    console.log("Free plan exists:", freePlanExists);
    if (!freePlanExists) {
      console.log("Adding free plan manually");
      plans.push({
        id: "free",
        name: "Free",
        description: "Basic features for personal use",
        section: "pro",
        monthlyPrice: 0,
        annualPrice: 0,
        popular: false,
        features: []
      });
    }
  } catch (error) {
    console.error("Error loading plans:", error);
    plans = [
      {
        id: "free",
        name: "Free",
        description: "Basic features for personal use",
        section: "pro",
        monthlyPrice: 0,
        annualPrice: 0,
        features: []
      }
    ];
  }
  return {
    user: locals.user,
    plans,
    preselectedPlanId,
    preselectedBillingCycle,
    preselectedSection
  };
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 88;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-CjS45z2I.js')).default;
const server_id = "src/routes/pricing/+page.server.ts";
const imports = ["_app/immutable/nodes/88.BR2ogsIe.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/0ykhD7u6.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/BhzFx1Wy.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/BNEH2jqx.js","_app/immutable/chunks/BuYRPDDz.js","_app/immutable/chunks/CnpHcmx3.js","_app/immutable/chunks/D9yI7a4E.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/BjCTmJLi.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/tdzGgazS.js","_app/immutable/chunks/BaVT73bJ.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/Cb-3cdbh.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/DMoa_yM9.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/DUwkOMSE.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/CWmzcjye.js","_app/immutable/chunks/T7uRAIbG.js","_app/immutable/chunks/BniYvUIG.js","_app/immutable/chunks/DrQfh6BY.js","_app/immutable/chunks/DxW95yuQ.js","_app/immutable/chunks/sDlmbjaf.js","_app/immutable/chunks/DQC1iFs0.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/CY_6SfHi.js","_app/immutable/chunks/CodWuqwu.js","_app/immutable/chunks/CQeqUgF6.js","_app/immutable/chunks/CKh8VGVX.js","_app/immutable/chunks/BKLOCbjP.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css","_app/immutable/assets/88.B3t7FN5M.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=88-kbNRpYBr.js.map
