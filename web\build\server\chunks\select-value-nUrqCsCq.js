import { p as push, Z as spread_attributes, O as escape_html, _ as clsx, q as pop } from './index3-CqUPEnZw.js';
import { c as cn } from './utils-pWl1tgmi.js';

function Select_value($$payload, $$props) {
  push();
  let {
    class: className,
    placeholder,
    onSelect = void 0,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  $$payload.out += `<span${spread_attributes(
    {
      class: clsx(cn("text-sm", className)),
      ...restProps
    },
    null
  )}>${escape_html(placeholder)}</span>`;
  pop();
}

export { Select_value as S };
//# sourceMappingURL=select-value-nUrqCsCq.js.map
