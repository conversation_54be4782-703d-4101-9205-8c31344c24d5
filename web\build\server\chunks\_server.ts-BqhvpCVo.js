import { j as json } from './index-Ddp2AB5f.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import { a as generatePasskeyAuthenticationOptions, b as verifyPasskeyAuthentication } from './webauthn-8Tn9JPcG.js';
import { b as getAuthLocation, a as createSessionToken } from './auth-BPad-IlN.js';
import { d as dev } from './index4-HpJcNJHQ.js';
import '@prisma/client';
import '@simplewebauthn/server';
import 'jsonwebtoken';
import 'ua-parser-js';
import './false-CRHihH2U.js';

const challenges = /* @__PURE__ */ new Map();
async function POST({ request, cookies }) {
  try {
    const { action } = await request.json();
    if (action === "getOptions") {
      const users = await prisma.user.findMany({
        where: {
          preferences: {
            path: ["security", "passkeys"],
            not: null
          }
        },
        select: {
          id: true,
          email: true,
          preferences: true
        }
      });
      const allPasskeys = [];
      for (const user of users) {
        const preferences = user.preferences;
        const securityPrefs = preferences?.security || {};
        const passkeys = securityPrefs.passkeys || [];
        if (passkeys && passkeys.length > 0) {
          const userPasskeys = passkeys.map((passkey) => ({
            ...passkey,
            userId: user.id
          }));
          allPasskeys.push(...userPasskeys);
        }
      }
      console.log(`Found ${allPasskeys.length} registered passkeys in the database`);
      allPasskeys.forEach((passkey, index) => {
        console.log(`Passkey ${index + 1}:`, {
          userId: passkey.userId,
          credentialID: passkey.credentialID,
          id: passkey.id,
          name: passkey.name,
          transports: passkey.transports
        });
      });
      console.log("Raw passkey data from database:");
      allPasskeys.forEach((passkey, index) => {
        console.log(`Passkey ${index + 1} raw data:`, passkey);
      });
      const options = await generatePasskeyAuthenticationOptions(allPasskeys);
      const challengeBase64 = Buffer.from(options.challenge).toString("base64");
      challenges.set("current", challengeBase64);
      console.log("Generated authentication options with challenge:", challengeBase64);
      console.log("Final authentication options:", {
        ...options,
        challenge: "ArrayBuffer (binary data)",
        allowCredentials: options.allowCredentials ? `${options.allowCredentials.length} credentials` : "omitted"
      });
      return json(options);
    } else if (action === "verify") {
      const { authenticationResponse } = await request.json();
      if (!authenticationResponse) {
        return json({ error: "Invalid authentication response" }, { status: 400 });
      }
      const challengeBase64 = challenges.get("current");
      if (!challengeBase64) {
        return json({ error: "No challenge found" }, { status: 400 });
      }
      console.log("Retrieved stored challenge:", challengeBase64);
      const users = await prisma.user.findMany({
        where: {
          preferences: {
            path: ["security", "passkeys"],
            not: null
          }
        },
        select: {
          id: true,
          email: true,
          name: true,
          preferences: true
        }
      });
      let matchedUser = null;
      let matchedPasskey = null;
      for (const user of users) {
        const preferences2 = user.preferences;
        const securityPrefs2 = preferences2?.security || {};
        const passkeys2 = securityPrefs2.passkeys || [];
        console.log("Looking for credential ID:", authenticationResponse.id);
        passkeys2.forEach((p, i) => {
          console.log(`Passkey ${i} credentialID:`, p.credentialID);
          console.log(`Passkey ${i} id:`, p.id);
        });
        const passkey = passkeys2.find(
          (p) => p.credentialID === authenticationResponse.id || p.id === authenticationResponse.id
        );
        if (passkey) {
          matchedUser = user;
          matchedPasskey = passkey;
          break;
        }
      }
      if (!matchedUser || !matchedPasskey) {
        return json({ error: "Passkey not found" }, { status: 404 });
      }
      const verification = await verifyPasskeyAuthentication(
        authenticationResponse,
        challengeBase64,
        matchedPasskey.credentialPublicKey,
        matchedPasskey.counter
      );
      console.log("Verification result:", verification);
      if (!verification.verified) {
        return json({ error: "Passkey verification failed" }, { status: 400 });
      }
      const preferences = matchedUser.preferences;
      const securityPrefs = preferences.security || {};
      const passkeys = securityPrefs.passkeys || [];
      const updatedPasskeys = passkeys.map((p) => {
        if (p.credentialID === authenticationResponse.id) {
          return {
            ...p,
            counter: verification.authenticationInfo.newCounter,
            lastUsed: (/* @__PURE__ */ new Date()).toISOString()
          };
        }
        return p;
      });
      await prisma.user.update({
        where: { id: matchedUser.id },
        data: {
          preferences: {
            ...preferences,
            security: {
              ...securityPrefs,
              passkeys: updatedPasskeys
            }
          }
        }
      });
      const { ip, location } = await getAuthLocation(request);
      try {
        await prisma.$executeRaw`INSERT INTO security_logs (user_id, action, ip, location, user_agent, details, created_at)
          VALUES (${matchedUser.id}, 'LOGIN_SUCCESS', ${ip}, ${location}, ${request.headers.get("user-agent") || "Unknown"},
          ${JSON.stringify({ method: "PASSKEY" })}, ${/* @__PURE__ */ new Date()})`;
      } catch (error) {
        console.error("Failed to log security event:", error);
      }
      const token = await createSessionToken(matchedUser, request);
      cookies.set("session", token, {
        path: "/",
        httpOnly: true,
        sameSite: "strict",
        secure: !dev,
        maxAge: 60 * 60 * 24 * 30
        // 30 days
      });
      challenges.delete("current");
      return json({
        success: true,
        user: {
          name: matchedUser.name || matchedUser.email,
          email: matchedUser.email
        },
        redirectUrl: "/dashboard"
      });
    }
    return json({ error: "Invalid action" }, { status: 400 });
  } catch (error) {
    console.error("Passkey authentication error:", error);
    return json({ error: "An error occurred during passkey authentication" }, { status: 500 });
  }
}

export { POST };
//# sourceMappingURL=_server.ts-BqhvpCVo.js.map
