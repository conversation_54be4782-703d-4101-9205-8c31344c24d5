import { p as push, V as copy_payload, W as assign_payload, Q as bind_props, q as pop, P as stringify, R as spread_props, O as escape_html, M as ensure_array_like, Z as spread_attributes } from './index3-CqUPEnZw.js';
import { R as Root, P as Popover_trigger, a as Popover_content } from './index14-C2WSwUih.js';
import { B as Button } from './button-CrucCo1G.js';
import { o as onDestroy, t as tick } from './index-server-CezSOnuG.js';
import { c as cn } from './utils-pWl1tgmi.js';
import { a as activeDropdownId, C as Command, b as Command_list, h as useCommandInput, c as Command_empty, g as Command_item } from './dropdown-store-B4Dfz2ZI.js';
import { I as Icon } from './Icon2-DkOdBr51.js';
import { b as box, m as mergeProps } from './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import { u as useId } from './use-id-CcFpwo20.js';
import { C as Check } from './check-WP_4Msti.js';
import { C as Chevron_down } from './chevron-down-xGjWLrZH.js';

function Command_input$1($$payload, $$props) {
  push();
  let {
    value = "",
    autofocus = false,
    id = useId(),
    ref = null,
    child,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const inputState = useCommandInput({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v),
    value: box.with(() => value, (v) => {
      value = v;
    }),
    autofocus: box.with(() => autofocus ?? false)
  });
  const mergedProps = mergeProps(restProps, inputState.props);
  if (child) {
    $$payload.out += "<!--[-->";
    child($$payload, { props: mergedProps });
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<input${spread_attributes({ ...mergedProps, value }, null)}/>`;
  }
  $$payload.out += `<!--]-->`;
  bind_props($$props, { value, ref });
  pop();
}
function Search($$payload, $$props) {
  push();
  let { $$slots, $$events, ...props } = $$props;
  const iconNode = [
    [
      "circle",
      { "cx": "11", "cy": "11", "r": "8" }
    ],
    ["path", { "d": "m21 21-4.3-4.3" }]
  ];
  Icon($$payload, spread_props([
    { name: "search" },
    props,
    {
      iconNode,
      children: ($$payload2) => {
        props.children?.($$payload2);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  pop();
}
function Command_input($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    value = "",
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<div class="flex h-9 items-center gap-2 border-b px-3" data-slot="command-input-wrapper">`;
    Search($$payload2, { class: "size-4 shrink-0 opacity-50" });
    $$payload2.out += `<!----> <!---->`;
    Command_input$1($$payload2, spread_props([
      {
        "data-slot": "command-input",
        class: cn("placeholder:text-muted-foreground outline-hidden flex h-10 w-full rounded-md bg-transparent py-3 text-sm disabled:cursor-not-allowed disabled:opacity-50", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        },
        get value() {
          return value;
        },
        set value($$value) {
          value = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out += `<!----></div>`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref, value });
  pop();
}
function Multi_combobox($$payload, $$props) {
  push();
  const {
    options = [],
    selectedValues: initialValues = [],
    placeholder = "Select items...",
    searchPlaceholder = "Search...",
    emptyMessage = "No items found.",
    width = "w-[200px]",
    disabled = false,
    paramName = "",
    // URL parameter name (handled by parent component)
    maxDisplayItems = 2,
    // Maximum number of items to display in the button
    onSelectedValuesChange = void 0,
    // Callback for when selected values change
    searchOptions = function() {
      return Promise.resolve([]);
    }
    // Callback for external search
  } = $$props;
  let selectedValues = [...initialValues];
  let open = false;
  let filteredOptions = [];
  let searchValue = "";
  let highlightedIndex = -1;
  let triggerRef = null;
  let isSearching = false;
  let searchTimeout = null;
  const dropdownId = `multi-combobox-${Math.random().toString(36).substring(2, 9)}`;
  function setSelectedValues(values) {
    selectedValues = values;
    dispatchChange();
  }
  function getSelectedValues() {
    return [...selectedValues];
  }
  const selectedLabels = selectedValues.map((value) => options.find((option) => option.value === value)?.label || value);
  function dispatchChange() {
    if (onSelectedValuesChange) {
      console.log(`MultiCombobox: Calling onSelectedValuesChange with values:`, selectedValues);
      onSelectedValuesChange(selectedValues);
    }
  }
  function dispatchSearch(value) {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
      searchTimeout = null;
    }
    isSearching = true;
    searchTimeout = setTimeout(
      () => {
        if (searchOptions && typeof searchOptions === "function") {
          searchOptions(value).then((searchResults) => {
            if (Array.isArray(searchResults)) {
              filteredOptions = [...searchResults];
            } else {
              console.warn("searchOptions did not return an array:", searchResults);
              filteredOptions = value ? [
                ...options.filter((option) => option.label.toLowerCase().includes(value.toLowerCase()))
              ] : [...options];
            }
          }).catch((error) => {
            console.error("Error in searchOptions:", error);
            filteredOptions = value ? [
              ...options.filter((option) => option.label.toLowerCase().includes(value.toLowerCase()))
            ] : [...options];
          }).finally(() => {
            isSearching = false;
            searchTimeout = null;
          });
        } else {
          filteredOptions = value ? [
            ...options.filter((option) => option.label.toLowerCase().includes(value.toLowerCase()))
          ] : [...options];
          isSearching = false;
          searchTimeout = null;
        }
      },
      300
    );
  }
  function toggleItem(value) {
    if (selectedValues.includes(value)) {
      selectedValues = selectedValues.filter((v) => v !== value);
    } else {
      selectedValues = [...selectedValues, value];
    }
    dispatchChange();
  }
  function clearAll() {
    selectedValues = [];
    dispatchChange();
  }
  function closeAndFocusTrigger() {
    open = false;
    highlightedIndex = -1;
    tick().then(() => {
      if (triggerRef) {
        triggerRef.focus();
      }
    });
  }
  function handleKeyDown(event) {
    if (!open) return;
    if (event.altKey && event.key === "c") {
      event.preventDefault();
      if (selectedValues.length > 0) {
        clearAll();
        closeAndFocusTrigger();
      }
      return;
    }
    switch (event.key) {
      case "ArrowDown":
        event.preventDefault();
        highlightedIndex = Math.min(highlightedIndex + 1, filteredOptions.length - 1);
        break;
      case "ArrowUp":
        event.preventDefault();
        highlightedIndex = Math.max(highlightedIndex - 1, -1);
        break;
      case "Enter":
        event.preventDefault();
        if (highlightedIndex >= 0 && highlightedIndex < filteredOptions.length) {
          toggleItem(filteredOptions[highlightedIndex].value);
        }
        break;
      case "Escape":
        event.preventDefault();
        closeAndFocusTrigger();
        break;
      case "Tab":
        if (!event.shiftKey) {
          closeAndFocusTrigger();
        }
        break;
    }
  }
  onDestroy(() => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
      searchTimeout = null;
    }
  });
  function handleOpenChange(newOpenState) {
    open = newOpenState;
    if (newOpenState) {
      highlightedIndex = -1;
      activeDropdownId.set(dropdownId);
    } else {
      activeDropdownId.update((currentId) => currentId === dropdownId ? null : currentId);
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Root($$payload2, {
      onopenchange: (e) => handleOpenChange(e.detail),
      get open() {
        return open;
      },
      set open($$value) {
        open = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        {
          let child = function($$payload4, { props }) {
            Button($$payload4, spread_props([
              {
                variant: "outline",
                role: "combobox",
                "aria-expanded": open,
                "aria-haspopup": "listbox",
                "aria-controls": "multi-combobox-options",
                "aria-label": placeholder,
                disabled
              },
              props,
              {
                children: ($$payload5) => {
                  $$payload5.out += `<div class="flex flex-1 items-center gap-1 overflow-hidden align-middle">`;
                  if (selectedValues.length === 0) {
                    $$payload5.out += "<!--[-->";
                    $$payload5.out += `<span class="text-muted-foreground">${escape_html(placeholder)}</span>`;
                  } else if (selectedValues.length <= maxDisplayItems) {
                    $$payload5.out += "<!--[1-->";
                    const each_array = ensure_array_like(selectedLabels);
                    $$payload5.out += `<!--[-->`;
                    for (let i = 0, $$length = each_array.length; i < $$length; i++) {
                      let label = each_array[i];
                      $$payload5.out += `<span class="truncate">${escape_html(label)}${escape_html(i < selectedLabels.length - 1 ? ", " : "")}</span>`;
                    }
                    $$payload5.out += `<!--]-->`;
                  } else {
                    $$payload5.out += "<!--[!-->";
                    const each_array_1 = ensure_array_like(selectedLabels.slice(0, maxDisplayItems));
                    $$payload5.out += `<!--[-->`;
                    for (let i = 0, $$length = each_array_1.length; i < $$length; i++) {
                      let label = each_array_1[i];
                      $$payload5.out += `<span class="truncate">${escape_html(label)}${escape_html(i < maxDisplayItems - 1 ? ", " : "")}</span>`;
                    }
                    $$payload5.out += `<!--]--> <span class="text-muted-foreground ml-1 text-xs">+${escape_html(selectedValues.length - maxDisplayItems)} more</span>`;
                  }
                  $$payload5.out += `<!--]--></div> `;
                  Chevron_down($$payload5, { class: "ml-2 size-4 opacity-50" });
                  $$payload5.out += `<!---->`;
                },
                $$slots: { default: true }
              }
            ]));
          };
          Popover_trigger($$payload3, {
            class: "!overflow-hidden",
            get ref() {
              return triggerRef;
            },
            set ref($$value) {
              triggerRef = $$value;
              $$settled = false;
            },
            child,
            $$slots: { child: true }
          });
        }
        $$payload3.out += `<!----> <!---->`;
        Popover_content($$payload3, {
          class: `${stringify(width)} rounded-none p-0`,
          align: "start",
          sideOffset: 8,
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            {
              $$payload4.out += `<!---->`;
              Command($$payload4, {
                shouldFilter: false,
                children: ($$payload5) => {
                  $$payload5.out += `<!---->`;
                  Command_input($$payload5, {
                    placeholder: searchPlaceholder,
                    oninput: () => dispatchSearch(searchValue),
                    onkeydown: handleKeyDown,
                    get value() {
                      return searchValue;
                    },
                    set value($$value) {
                      searchValue = $$value;
                      $$settled = false;
                    }
                  });
                  $$payload5.out += `<!----> <!---->`;
                  Command_list($$payload5, {
                    class: "py-1",
                    children: ($$payload6) => {
                      if (isSearching) {
                        $$payload6.out += "<!--[-->";
                        $$payload6.out += `<!---->`;
                        Command_empty($$payload6, {
                          class: "p-2",
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->Searching...`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!---->`;
                      } else if (filteredOptions.length === 0) {
                        $$payload6.out += "<!--[1-->";
                        $$payload6.out += `<!---->`;
                        Command_empty($$payload6, {
                          class: "p-2",
                          children: ($$payload7) => {
                            $$payload7.out += `<!---->No results found.`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload6.out += `<!---->`;
                      } else {
                        $$payload6.out += "<!--[!-->";
                        const each_array_2 = ensure_array_like(filteredOptions);
                        $$payload6.out += `<!--[-->`;
                        for (let index = 0, $$length = each_array_2.length; index < $$length; index++) {
                          let option = each_array_2[index];
                          $$payload6.out += `<!---->`;
                          Command_item($$payload6, {
                            value: option.value,
                            onSelect: () => toggleItem(option.value),
                            children: ($$payload7) => {
                              $$payload7.out += `<div class="flex w-full items-center justify-between"><span>${escape_html(option.label)}</span> `;
                              Check($$payload7, {
                                class: cn("h-4 w-4", !selectedValues.includes(option.value) && "text-transparent")
                              });
                              $$payload7.out += `<!----></div>`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload6.out += `<!---->`;
                        }
                        $$payload6.out += `<!--]-->`;
                      }
                      $$payload6.out += `<!--]-->`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----> `;
                  if (selectedValues.length > 0) {
                    $$payload5.out += "<!--[-->";
                    $$payload5.out += `<div class="border-t px-2">`;
                    Button($$payload5, {
                      type: "button",
                      size: "sm",
                      variant: "ghost",
                      class: "my-2 h-6 w-full rounded-none p-1 text-center text-xs",
                      "aria-label": "Clear all selections",
                      onclick: () => {
                        clearAll();
                        closeAndFocusTrigger();
                      },
                      onkeydown: (e) => {
                        if (e.key === "Enter" || e.key === " ") {
                          e.preventDefault();
                          clearAll();
                          closeAndFocusTrigger();
                        }
                      },
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->Clear selections`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----></div>`;
                  } else {
                    $$payload5.out += "<!--[!-->";
                  }
                  $$payload5.out += `<!--]-->`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            }
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { setSelectedValues, getSelectedValues });
  pop();
}

export { Multi_combobox as M };
//# sourceMappingURL=multi-combobox-BJ-pW9qf.js.map
