function getDataOpenClosed(condition) {
  return condition ? "open" : "closed";
}
function getDataChecked(condition) {
  return condition ? "checked" : "unchecked";
}
function getAriaDisabled(condition) {
  return condition ? "true" : "false";
}
function getAriaReadonly(condition) {
  return condition ? "true" : "false";
}
function getAriaExpanded(condition) {
  return condition ? "true" : "false";
}
function getDataDisabled(condition) {
  return condition ? "" : void 0;
}
function getAriaRequired(condition) {
  return condition ? "true" : "false";
}
function getAriaSelected(condition) {
  return condition ? "true" : "false";
}
function getAriaChecked(checked, indeterminate) {
  if (indeterminate) {
    return "mixed";
  }
  return checked ? "true" : "false";
}
function getAriaOrientation(orientation) {
  return orientation;
}
function getAriaHidden(condition) {
  return condition ? "true" : void 0;
}
function getDataOrientation(orientation) {
  return orientation;
}
function getDataInvalid(condition) {
  return condition ? "" : void 0;
}
function getDataRequired(condition) {
  return condition ? "" : void 0;
}
function getDataReadonly(condition) {
  return condition ? "" : void 0;
}
function getDataSelected(condition) {
  return condition ? "" : void 0;
}
function getDataUnavailable(condition) {
  return condition ? "" : void 0;
}
function getHidden(condition) {
  return condition ? true : void 0;
}
function getDisabled(condition) {
  return condition ? true : void 0;
}
function getRequired(condition) {
  return condition ? true : void 0;
}
const ARROW_DOWN = "ArrowDown";
const ARROW_LEFT = "ArrowLeft";
const ARROW_RIGHT = "ArrowRight";
const ARROW_UP = "ArrowUp";
const END = "End";
const ENTER = "Enter";
const ESCAPE = "Escape";
const HOME = "Home";
const PAGE_DOWN = "PageDown";
const PAGE_UP = "PageUp";
const SPACE = " ";
const TAB = "Tab";
const p = "p";
const n = "n";
const j = "j";
const k = "k";

export { ARROW_LEFT as A, getDataReadonly as B, getDataInvalid as C, getAriaReadonly as D, ESCAPE as E, getDataUnavailable as F, HOME as H, PAGE_UP as P, SPACE as S, TAB as T, getDataOpenClosed as a, ARROW_RIGHT as b, ARROW_DOWN as c, getAriaExpanded as d, getDataDisabled as e, END as f, getDataOrientation as g, ARROW_UP as h, ENTER as i, PAGE_DOWN as j, getAriaOrientation as k, getAriaDisabled as l, k as m, j as n, n as o, p, getDataSelected as q, getAriaSelected as r, getDisabled as s, getHidden as t, getDataRequired as u, getDataChecked as v, getAriaRequired as w, getAriaChecked as x, getAriaHidden as y, getRequired as z };
//# sourceMappingURL=kbd-constants-Ch6RKbNZ.js.map
