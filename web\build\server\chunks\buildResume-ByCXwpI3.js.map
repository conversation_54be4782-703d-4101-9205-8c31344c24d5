{"version": 3, "file": "buildResume-ByCXwpI3.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/buildResume.js"], "sourcesContent": ["import { z } from \"zod\";\nconst designFormSchema = z.object({\n  alignment: z.string({ required_error: \"Please select an alignment\" }),\n  margin: z.string({ required_error: \"Please select a margin size\" }),\n  pageSize: z.string({ required_error: \"Please select a page size\" }),\n  font: z.string({ required_error: \"Please select a font\" }),\n  fontSize: z.string({ required_error: \"Please select a font size\" }),\n  lineHeight: z.string({ required_error: \"Please select a line height\" }),\n  bulletIcon: z.string({ required_error: \"Please select a bullet icon\" }),\n  // Add new fields\n  layout: z.string().optional(),\n  primaryColor: z.string().optional(),\n  accentColor: z.string().optional(),\n  textColor: z.string().optional(),\n  backgroundColor: z.string().optional(),\n  headerStyle: z.string().optional(),\n  sectionStyle: z.string().optional(),\n  paperSize: z.string().optional()\n});\nconst designDefaultValues = {\n  alignment: \"Left\",\n  margin: \"Medium\",\n  pageSize: \"Letter (8.5 x 11 in)\",\n  font: \"Roboto\",\n  fontSize: \"12px\",\n  lineHeight: \"1.5\",\n  bulletIcon: \"•\",\n  // Default values for new fields\n  layout: \"classic\",\n  primaryColor: \"#2563eb\",\n  accentColor: \"#4b5563\",\n  textColor: \"#111827\",\n  backgroundColor: \"#ffffff\",\n  headerStyle: \"centered\",\n  sectionStyle: \"bordered\",\n  paperSize: \"letter\"\n};\nconst resumeFormSchema = z.object({\n  header: z.object({\n    name: z.string().default(\"\"),\n    email: z.string().email().default(\"\"),\n    phone: z.string().default(\"\")\n  }),\n  summary: z.object({\n    content: z.string().default(\"\")\n    // You can rename `content` to whatever makes sense later\n  }).default({}),\n  education: z.array(\n    z.object({\n      school: z.string().default(\"\"),\n      degree: z.string().default(\"\"),\n      major: z.string().default(\"\"),\n      gpa: z.string().default(\"\"),\n      startDate: z.string().default(\"\"),\n      endDate: z.string().default(\"\")\n    })\n  ).default([]),\n  certifications: z.array(\n    z.object({\n      description: z.string().default(\"\")\n    })\n  ).default([]),\n  experience: z.array(\n    z.object({\n      jobTitle: z.string().default(\"\"),\n      company: z.string().default(\"\"),\n      startDate: z.string().default(\"\"),\n      endDate: z.string().default(\"\"),\n      description: z.string().default(\"\")\n    })\n  ).default([]),\n  projects: z.array(\n    z.object({\n      name: z.string().default(\"\"),\n      description: z.string().default(\"\")\n    })\n  ).default([]),\n  skills: z.array(\n    z.object({\n      name: z.string().default(\"\"),\n      years: z.string().default(\"\")\n    })\n  ).default([])\n});\nexport {\n  designFormSchema as a,\n  designDefaultValues as d,\n  resumeFormSchema as r\n};\n"], "names": ["z.object", "z.string", "z.array"], "mappings": ";;AACK,MAAC,gBAAgB,GAAGA,UAAQ,CAAC;AAClC,EAAE,SAAS,EAAEC,UAAQ,CAAC,EAAE,cAAc,EAAE,4BAA4B,EAAE,CAAC;AACvE,EAAE,MAAM,EAAEA,UAAQ,CAAC,EAAE,cAAc,EAAE,6BAA6B,EAAE,CAAC;AACrE,EAAE,QAAQ,EAAEA,UAAQ,CAAC,EAAE,cAAc,EAAE,2BAA2B,EAAE,CAAC;AACrE,EAAE,IAAI,EAAEA,UAAQ,CAAC,EAAE,cAAc,EAAE,sBAAsB,EAAE,CAAC;AAC5D,EAAE,QAAQ,EAAEA,UAAQ,CAAC,EAAE,cAAc,EAAE,2BAA2B,EAAE,CAAC;AACrE,EAAE,UAAU,EAAEA,UAAQ,CAAC,EAAE,cAAc,EAAE,6BAA6B,EAAE,CAAC;AACzE,EAAE,UAAU,EAAEA,UAAQ,CAAC,EAAE,cAAc,EAAE,6BAA6B,EAAE,CAAC;AACzE;AACA,EAAE,MAAM,EAAEA,UAAQ,EAAE,CAAC,QAAQ,EAAE;AAC/B,EAAE,YAAY,EAAEA,UAAQ,EAAE,CAAC,QAAQ,EAAE;AACrC,EAAE,WAAW,EAAEA,UAAQ,EAAE,CAAC,QAAQ,EAAE;AACpC,EAAE,SAAS,EAAEA,UAAQ,EAAE,CAAC,QAAQ,EAAE;AAClC,EAAE,eAAe,EAAEA,UAAQ,EAAE,CAAC,QAAQ,EAAE;AACxC,EAAE,WAAW,EAAEA,UAAQ,EAAE,CAAC,QAAQ,EAAE;AACpC,EAAE,YAAY,EAAEA,UAAQ,EAAE,CAAC,QAAQ,EAAE;AACrC,EAAE,SAAS,EAAEA,UAAQ,EAAE,CAAC,QAAQ;AAChC,CAAC;AACI,MAAC,mBAAmB,GAAG;AAC5B,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,MAAM,EAAE,QAAQ;AAClB,EAAE,QAAQ,EAAE,sBAAsB;AAClC,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,QAAQ,EAAE,MAAM;AAClB,EAAE,UAAU,EAAE,KAAK;AACnB,EAAE,UAAU,EAAE,GAAG;AACjB;AACA,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,YAAY,EAAE,SAAS;AACzB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,eAAe,EAAE,SAAS;AAC5B,EAAE,WAAW,EAAE,UAAU;AACzB,EAAE,YAAY,EAAE,UAAU;AAC1B,EAAE,SAAS,EAAE;AACb;AACK,MAAC,gBAAgB,GAAGD,UAAQ,CAAC;AAClC,EAAE,MAAM,EAAEA,UAAQ,CAAC;AACnB,IAAI,IAAI,EAAEC,UAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;AAChC,IAAI,KAAK,EAAEA,UAAQ,EAAE,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;AACzC,IAAI,KAAK,EAAEA,UAAQ,EAAE,CAAC,OAAO,CAAC,EAAE;AAChC,GAAG,CAAC;AACJ,EAAE,OAAO,EAAED,UAAQ,CAAC;AACpB,IAAI,OAAO,EAAEC,UAAQ,EAAE,CAAC,OAAO,CAAC,EAAE;AAClC;AACA,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;AAChB,EAAE,SAAS,EAAEC,SAAO;AACpB,IAAIF,UAAQ,CAAC;AACb,MAAM,MAAM,EAAEC,UAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;AACpC,MAAM,MAAM,EAAEA,UAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;AACpC,MAAM,KAAK,EAAEA,UAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;AACnC,MAAM,GAAG,EAAEA,UAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;AACjC,MAAM,SAAS,EAAEA,UAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;AACvC,MAAM,OAAO,EAAEA,UAAQ,EAAE,CAAC,OAAO,CAAC,EAAE;AACpC,KAAK;AACL,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;AACf,EAAE,cAAc,EAAEC,SAAO;AACzB,IAAIF,UAAQ,CAAC;AACb,MAAM,WAAW,EAAEC,UAAQ,EAAE,CAAC,OAAO,CAAC,EAAE;AACxC,KAAK;AACL,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;AACf,EAAE,UAAU,EAAEC,SAAO;AACrB,IAAIF,UAAQ,CAAC;AACb,MAAM,QAAQ,EAAEC,UAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;AACtC,MAAM,OAAO,EAAEA,UAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;AACrC,MAAM,SAAS,EAAEA,UAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;AACvC,MAAM,OAAO,EAAEA,UAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;AACrC,MAAM,WAAW,EAAEA,UAAQ,EAAE,CAAC,OAAO,CAAC,EAAE;AACxC,KAAK;AACL,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;AACf,EAAE,QAAQ,EAAEC,SAAO;AACnB,IAAIF,UAAQ,CAAC;AACb,MAAM,IAAI,EAAEC,UAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;AAClC,MAAM,WAAW,EAAEA,UAAQ,EAAE,CAAC,OAAO,CAAC,EAAE;AACxC,KAAK;AACL,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;AACf,EAAE,MAAM,EAAEC,SAAO;AACjB,IAAIF,UAAQ,CAAC;AACb,MAAM,IAAI,EAAEC,UAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;AAClC,MAAM,KAAK,EAAEA,UAAQ,EAAE,CAAC,OAAO,CAAC,EAAE;AAClC,KAAK;AACL,GAAG,CAAC,OAAO,CAAC,EAAE;AACd,CAAC;;;;"}