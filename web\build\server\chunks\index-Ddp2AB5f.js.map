{"version": 3, "file": "index-Ddp2AB5f.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/index.js"], "sourcesContent": ["class HttpError {\n  /**\n   * @param {number} status\n   * @param {{message: string} extends App.Error ? (App.Error | string | undefined) : App.Error} body\n   */\n  constructor(status, body) {\n    this.status = status;\n    if (typeof body === \"string\") {\n      this.body = { message: body };\n    } else if (body) {\n      this.body = body;\n    } else {\n      this.body = { message: `Error: ${status}` };\n    }\n  }\n  toString() {\n    return JSON.stringify(this.body);\n  }\n}\nclass Redirect {\n  /**\n   * @param {300 | 301 | 302 | 303 | 304 | 305 | 306 | 307 | 308} status\n   * @param {string} location\n   */\n  constructor(status, location) {\n    this.status = status;\n    this.location = location;\n  }\n}\nclass SvelteKitError extends Error {\n  /**\n   * @param {number} status\n   * @param {string} text\n   * @param {string} message\n   */\n  constructor(status, text2, message) {\n    super(message);\n    this.status = status;\n    this.text = text2;\n  }\n}\nclass ActionFailure {\n  /**\n   * @param {number} status\n   * @param {T} data\n   */\n  constructor(status, data) {\n    this.status = status;\n    this.data = data;\n  }\n}\nfunction error(status, body) {\n  if (isNaN(status) || status < 400 || status > 599) {\n    throw new Error(`HTTP error status codes must be between 400 and 599 — ${status} is invalid`);\n  }\n  throw new HttpError(status, body);\n}\nfunction redirect(status, location) {\n  if (isNaN(status) || status < 300 || status > 308) {\n    throw new Error(\"Invalid status code\");\n  }\n  throw new Redirect(\n    // @ts-ignore\n    status,\n    location.toString()\n  );\n}\nfunction json(data, init) {\n  const body = JSON.stringify(data);\n  const headers = new Headers(init?.headers);\n  if (!headers.has(\"content-length\")) {\n    headers.set(\"content-length\", encoder.encode(body).byteLength.toString());\n  }\n  if (!headers.has(\"content-type\")) {\n    headers.set(\"content-type\", \"application/json\");\n  }\n  return new Response(body, {\n    ...init,\n    headers\n  });\n}\nconst encoder = new TextEncoder();\nfunction text(body, init) {\n  const headers = new Headers(init?.headers);\n  if (!headers.has(\"content-length\")) {\n    const encoded = encoder.encode(body);\n    headers.set(\"content-length\", encoded.byteLength.toString());\n    return new Response(encoded, {\n      ...init,\n      headers\n    });\n  }\n  return new Response(body, {\n    ...init,\n    headers\n  });\n}\nfunction fail(status, data) {\n  return new ActionFailure(status, data);\n}\nexport {\n  ActionFailure as A,\n  HttpError as H,\n  Redirect as R,\n  SvelteKitError as S,\n  error as e,\n  fail as f,\n  json as j,\n  redirect as r,\n  text as t\n};\n"], "names": [], "mappings": "AAAA,MAAM,SAAS,CAAC;AAChB;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE;AAC5B,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM;AACxB,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAClC,MAAM,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE;AACnC,KAAK,MAAM,IAAI,IAAI,EAAE;AACrB,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI;AACtB,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,EAAE;AACjD;AACA;AACA,EAAE,QAAQ,GAAG;AACb,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;AACpC;AACA;AACA,MAAM,QAAQ,CAAC;AACf;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE;AAChC,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM;AACxB,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ;AAC5B;AACA;AACA,MAAM,cAAc,SAAS,KAAK,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE;AACtC,IAAI,KAAK,CAAC,OAAO,CAAC;AAClB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM;AACxB,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK;AACrB;AACA;AACA,MAAM,aAAa,CAAC;AACpB;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE;AAC5B,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM;AACxB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB;AACA;AACA,SAAS,KAAK,CAAC,MAAM,EAAE,IAAI,EAAE;AAC7B,EAAE,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,MAAM,GAAG,GAAG,IAAI,MAAM,GAAG,GAAG,EAAE;AACrD,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,sDAAsD,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;AACjG;AACA,EAAE,MAAM,IAAI,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC;AACnC;AACA,SAAS,QAAQ,CAAC,MAAM,EAAE,QAAQ,EAAE;AACpC,EAAE,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,MAAM,GAAG,GAAG,IAAI,MAAM,GAAG,GAAG,EAAE;AACrD,IAAI,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC;AAC1C;AACA,EAAE,MAAM,IAAI,QAAQ;AACpB;AACA,IAAI,MAAM;AACV,IAAI,QAAQ,CAAC,QAAQ;AACrB,GAAG;AACH;AACA,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;AACnC,EAAE,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;AAC5C,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE;AACtC,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;AAC7E;AACA,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;AACpC,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC;AACnD;AACA,EAAE,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE;AAC5B,IAAI,GAAG,IAAI;AACX,IAAI;AACJ,GAAG,CAAC;AACJ;AACA,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE;AACjC,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,EAAE,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;AAC5C,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE;AACtC,IAAI,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;AACxC,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;AAChE,IAAI,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE;AACjC,MAAM,GAAG,IAAI;AACb,MAAM;AACN,KAAK,CAAC;AACN;AACA,EAAE,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE;AAC5B,IAAI,GAAG,IAAI;AACX,IAAI;AACJ,GAAG,CAAC;AACJ;AACA,SAAS,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE;AAC5B,EAAE,OAAO,IAAI,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC;AACxC;;;;"}