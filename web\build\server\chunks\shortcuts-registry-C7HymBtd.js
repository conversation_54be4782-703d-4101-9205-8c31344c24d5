import { X as sanitize_props, Y as rest_props, K as fallback, Z as spread_attributes, N as attr, _ as clsx, Q as bind_props } from './index3-CqUPEnZw.js';

function Logo($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["className", "fill", "stroke"]);
  let className = fallback($$props["className"], "");
  let fill = fallback($$props["fill"], "");
  let stroke = fallback($$props["stroke"], "currentColor");
  $$payload.out += `<svg${spread_attributes(
    {
      xmlns: "http://www.w3.org/2000/svg",
      class: clsx(className),
      fill,
      viewBox: "0 0 256 256",
      height: "32",
      width: "32",
      stroke,
      ...$$restProps
    },
    null,
    void 0,
    void 0,
    3
  )}><rect height="240"${attr("stroke", stroke)} stroke-width="26" width="240" x="8" y="8" rx="26"></rect><path d="M80 130 L110 160 L180 90" fill="none"${attr("stroke", stroke)} stroke-dasharray="300" stroke-dashoffset="300" stroke-linecap="round" stroke-linejoin="round" stroke-width="26"><animate attributeName="stroke-dashoffset" begin="0.2s" dur="0.6s" fill="freeze" from="300" to="0"></animate></path></svg>`;
  bind_props($$props, { className, fill, stroke });
}
var ModifierKey = /* @__PURE__ */ ((ModifierKey2) => {
  ModifierKey2["ALT"] = "Alt";
  ModifierKey2["WINDOWS"] = "Win";
  ModifierKey2["COMMAND"] = "⌘";
  ModifierKey2["CONTROL"] = "Ctrl";
  return ModifierKey2;
})(ModifierKey || {});
var ShortcutPage = /* @__PURE__ */ ((ShortcutPage2) => {
  ShortcutPage2["GLOBAL"] = "global";
  ShortcutPage2["DASHBOARD"] = "dashboard";
  ShortcutPage2["JOBS"] = "jobs";
  ShortcutPage2["APPLICATIONS"] = "applications";
  ShortcutPage2["RESUMES"] = "resumes";
  ShortcutPage2["DOCUMENTS"] = "documents";
  ShortcutPage2["TRACKER"] = "tracker";
  ShortcutPage2["AUTOMATION"] = "automation";
  ShortcutPage2["MATCHES"] = "matches";
  ShortcutPage2["SETTINGS"] = "settings";
  ShortcutPage2["ADMIN"] = "admin";
  ShortcutPage2["SYSTEM_STATUS"] = "system-status";
  ShortcutPage2["NOTIFICATIONS"] = "notifications";
  return ShortcutPage2;
})(ShortcutPage || {});
const navigationShortcuts = {
  id: "navigation",
  name: "Navigation",
  activePages: [ShortcutPage.GLOBAL],
  shortcuts: [
    {
      id: "nav-dashboard",
      action: "Go to Dashboard",
      keys: `${ModifierKey.ALT}+D`,
      handler: () => {
      },
      description: "Navigate to the dashboard page"
    },
    {
      id: "nav-jobs",
      action: "Go to Jobs",
      keys: `${ModifierKey.ALT}+J`,
      handler: () => {
      },
      description: "Navigate to the jobs page"
    },
    {
      id: "nav-applications",
      action: "Go to Applications",
      keys: `${ModifierKey.ALT}+A`,
      handler: () => {
      },
      description: "Navigate to the applications page"
    },
    {
      id: "nav-matches",
      action: "Go to Matches",
      keys: `${ModifierKey.ALT}+M`,
      handler: () => {
      },
      description: "Navigate to the job matches page"
    },
    {
      id: "nav-tracker",
      action: "Go to Job Tracker",
      keys: `${ModifierKey.ALT}+T`,
      handler: () => {
      },
      description: "Navigate to the job tracker page"
    },
    {
      id: "nav-documents",
      action: "Go to Documents",
      keys: `${ModifierKey.ALT}+O`,
      handler: () => {
      },
      description: "Navigate to the documents page"
    },
    {
      id: "nav-automation",
      action: "Go to Automation",
      keys: `${ModifierKey.ALT}+U`,
      handler: () => {
      },
      description: "Navigate to the automation page"
    },
    {
      id: "nav-settings",
      action: "Go to Settings",
      keys: `${ModifierKey.ALT}+S`,
      handler: () => {
      },
      description: "Navigate to the settings page"
    },
    {
      id: "nav-profile",
      action: "Go to Profile",
      keys: `${ModifierKey.ALT}+P`,
      handler: () => {
      },
      description: "Navigate to the profile page"
    },
    {
      id: "nav-billing",
      action: "Go to Billing",
      keys: `${ModifierKey.ALT}+B`,
      handler: () => {
      },
      description: "Navigate to the billing page"
    }
  ]
};
const uiShortcuts = {
  id: "ui",
  name: "User Interface",
  activePages: [ShortcutPage.GLOBAL],
  shortcuts: [
    {
      id: "ui-search",
      action: "Open Search",
      keys: `${ModifierKey.ALT}+K`,
      handler: (event) => {
        event.preventDefault();
      },
      description: "Open the global search dialog"
    },
    {
      id: "ui-shortcuts",
      action: "Show Keyboard Shortcuts",
      keys: `${ModifierKey.ALT}+/`,
      handler: (event) => {
        event.preventDefault();
      },
      description: "Show this keyboard shortcuts dialog"
    },
    {
      id: "ui-notifications",
      action: "Open Notifications",
      keys: `${ModifierKey.ALT}+N`,
      handler: () => {
      },
      description: "Open the notifications panel"
    },
    {
      id: "ui-feedback",
      action: "Open Feedback",
      keys: `${ModifierKey.ALT}+F`,
      handler: () => {
      },
      description: "Open the feedback panel"
    },
    {
      id: "ui-user-menu",
      action: "Open User Menu",
      keys: `${ModifierKey.ALT}+U`,
      handler: () => {
      },
      description: "Open the user menu"
    },
    {
      id: "ui-logout",
      action: "Log Out",
      keys: `${ModifierKey.ALT}+Q`,
      handler: () => {
      },
      description: "Log out of the application"
    },
    {
      id: "ui-refresh",
      action: "Refresh Page",
      keys: `${ModifierKey.ALT}+R`,
      handler: () => {
      },
      description: "Refresh the current page"
    }
  ]
};
const jobShortcuts = {
  id: "jobs",
  name: "Job Search",
  activePages: [ShortcutPage.JOBS],
  shortcuts: [
    {
      id: "job-save",
      action: "Save Job",
      keys: `${ModifierKey.ALT}+S`,
      handler: () => {
      },
      description: "Save the currently selected job"
    },
    {
      id: "job-apply",
      action: "Apply to Job",
      keys: `${ModifierKey.ALT}+Y`,
      handler: () => {
      },
      description: "Apply to the currently selected job"
    },
    {
      id: "job-filter",
      action: "Toggle Filters",
      keys: `${ModifierKey.ALT}+F`,
      handler: () => {
      },
      description: "Toggle job search filters"
    },
    {
      id: "job-refresh",
      action: "Refresh Jobs",
      keys: `${ModifierKey.ALT}+R`,
      handler: () => {
      },
      description: "Refresh job listings"
    },
    {
      id: "job-next",
      action: "Next Job",
      keys: `${ModifierKey.ALT}+ArrowDown`,
      handler: () => {
      },
      description: "Navigate to the next job in the list"
    },
    {
      id: "job-prev",
      action: "Previous Job",
      keys: `${ModifierKey.ALT}+ArrowUp`,
      handler: () => {
      },
      description: "Navigate to the previous job in the list"
    },
    {
      id: "job-details",
      action: "View Job Details",
      keys: `${ModifierKey.ALT}+Enter`,
      handler: () => {
      },
      description: "View details of the selected job"
    },
    {
      id: "job-share",
      action: "Share Job",
      keys: `${ModifierKey.ALT}+H`,
      handler: () => {
      },
      description: "Share the selected job"
    },
    {
      id: "job-clear-filters",
      action: "Clear Filters",
      keys: `${ModifierKey.ALT}+C`,
      handler: () => {
      },
      description: "Clear all job filters"
    }
  ]
};
const applicationShortcuts = {
  id: "applications",
  name: "Applications",
  activePages: [ShortcutPage.APPLICATIONS],
  shortcuts: [
    {
      id: "app-view",
      action: "View Application Details",
      keys: `${ModifierKey.ALT}+V`,
      handler: () => {
      },
      description: "View details of the selected application"
    },
    {
      id: "app-status",
      action: "Update Status",
      keys: `${ModifierKey.ALT}+U`,
      handler: () => {
      },
      description: "Update the status of the selected application"
    },
    {
      id: "app-note",
      action: "Add Note",
      keys: `${ModifierKey.ALT}+E`,
      handler: () => {
      },
      description: "Add a note to the selected application"
    },
    {
      id: "app-filter",
      action: "Toggle Filters",
      keys: `${ModifierKey.ALT}+F`,
      handler: () => {
      },
      description: "Toggle application filters"
    },
    {
      id: "app-next",
      action: "Next Application",
      keys: `${ModifierKey.ALT}+ArrowDown`,
      handler: () => {
      },
      description: "Navigate to the next application in the list"
    },
    {
      id: "app-prev",
      action: "Previous Application",
      keys: `${ModifierKey.ALT}+ArrowUp`,
      handler: () => {
      },
      description: "Navigate to the previous application in the list"
    },
    {
      id: "app-withdraw",
      action: "Withdraw Application",
      keys: `${ModifierKey.ALT}+W`,
      handler: () => {
      },
      description: "Withdraw the selected application"
    },
    {
      id: "app-refresh",
      action: "Refresh Applications",
      keys: `${ModifierKey.ALT}+R`,
      handler: () => {
      },
      description: "Refresh application listings"
    },
    {
      id: "app-clear-filters",
      action: "Clear Filters",
      keys: `${ModifierKey.ALT}+C`,
      handler: () => {
      },
      description: "Clear all application filters"
    }
  ]
};
const documentShortcuts = {
  id: "documents",
  name: "Documents",
  activePages: [ShortcutPage.RESUMES],
  shortcuts: [
    {
      id: "doc-new",
      action: "New Document",
      keys: `${ModifierKey.ALT}+N`,
      handler: () => {
      },
      description: "Create a new document"
    },
    {
      id: "doc-save",
      action: "Save Document",
      keys: `${ModifierKey.ALT}+S`,
      handler: () => {
      },
      description: "Save the current document"
    },
    {
      id: "doc-preview",
      action: "Preview Document",
      keys: `${ModifierKey.ALT}+V`,
      handler: () => {
      },
      description: "Preview the current document"
    },
    {
      id: "doc-download",
      action: "Download Document",
      keys: `${ModifierKey.ALT}+W`,
      handler: () => {
      },
      description: "Download the current document"
    },
    {
      id: "doc-next",
      action: "Next Document",
      keys: `${ModifierKey.ALT}+ArrowDown`,
      handler: () => {
      },
      description: "Navigate to the next document in the list"
    },
    {
      id: "doc-prev",
      action: "Previous Document",
      keys: `${ModifierKey.ALT}+ArrowUp`,
      handler: () => {
      },
      description: "Navigate to the previous document in the list"
    },
    {
      id: "doc-delete",
      action: "Delete Document",
      keys: `${ModifierKey.ALT}+Delete`,
      handler: () => {
      },
      description: "Delete the selected document"
    },
    {
      id: "doc-duplicate",
      action: "Duplicate Document",
      keys: `${ModifierKey.ALT}+D`,
      handler: () => {
      },
      description: "Duplicate the selected document"
    },
    {
      id: "doc-rename",
      action: "Rename Document",
      keys: `${ModifierKey.ALT}+R`,
      handler: () => {
      },
      description: "Rename the selected document"
    },
    {
      id: "doc-share",
      action: "Share Document",
      keys: `${ModifierKey.ALT}+H`,
      handler: () => {
      },
      description: "Share the selected document"
    }
  ]
};
const trackerShortcuts = {
  id: "tracker",
  name: "Job Tracker",
  activePages: [ShortcutPage.DASHBOARD],
  shortcuts: [
    {
      id: "tracker-new",
      action: "Add New Job",
      keys: `${ModifierKey.ALT}+N`,
      handler: () => {
      },
      description: "Add a new job to the tracker"
    },
    {
      id: "tracker-update",
      action: "Update Job Status",
      keys: `${ModifierKey.ALT}+U`,
      handler: () => {
      },
      description: "Update the status of the selected job"
    },
    {
      id: "tracker-note",
      action: "Add Note",
      keys: `${ModifierKey.ALT}+E`,
      handler: () => {
      },
      description: "Add a note to the selected job"
    },
    {
      id: "tracker-delete",
      action: "Delete Job",
      keys: `${ModifierKey.ALT}+Delete`,
      handler: () => {
      },
      description: "Delete the selected job from the tracker"
    },
    {
      id: "tracker-filter",
      action: "Toggle Filters",
      keys: `${ModifierKey.ALT}+F`,
      handler: () => {
      },
      description: "Toggle job tracker filters"
    },
    {
      id: "tracker-next",
      action: "Next Job",
      keys: `${ModifierKey.ALT}+ArrowDown`,
      handler: () => {
      },
      description: "Navigate to the next job in the tracker"
    },
    {
      id: "tracker-prev",
      action: "Previous Job",
      keys: `${ModifierKey.ALT}+ArrowUp`,
      handler: () => {
      },
      description: "Navigate to the previous job in the tracker"
    },
    {
      id: "tracker-refresh",
      action: "Refresh Tracker",
      keys: `${ModifierKey.ALT}+R`,
      handler: () => {
      },
      description: "Refresh the job tracker"
    }
  ]
};
const automationShortcuts = {
  id: "automation",
  name: "Automation",
  activePages: [ShortcutPage.DASHBOARD],
  shortcuts: [
    {
      id: "auto-new",
      action: "New Automation",
      keys: `${ModifierKey.ALT}+N`,
      handler: () => {
      },
      description: "Create a new automation"
    },
    {
      id: "auto-start",
      action: "Start Automation",
      keys: `${ModifierKey.ALT}+G`,
      handler: () => {
      },
      description: "Start the selected automation"
    },
    {
      id: "auto-stop",
      action: "Stop Automation",
      keys: `${ModifierKey.ALT}+X`,
      handler: () => {
      },
      description: "Stop the selected automation"
    },
    {
      id: "auto-edit",
      action: "Edit Automation",
      keys: `${ModifierKey.ALT}+E`,
      handler: () => {
      },
      description: "Edit the selected automation"
    },
    {
      id: "auto-delete",
      action: "Delete Automation",
      keys: `${ModifierKey.ALT}+Delete`,
      handler: () => {
      },
      description: "Delete the selected automation"
    },
    {
      id: "auto-logs",
      action: "View Logs",
      keys: `${ModifierKey.ALT}+L`,
      handler: () => {
      },
      description: "View logs for the selected automation"
    }
  ]
};
const shortcutGroups = [
  navigationShortcuts,
  uiShortcuts,
  jobShortcuts,
  applicationShortcuts,
  documentShortcuts,
  trackerShortcuts,
  automationShortcuts
];
function getShortcutsForPage(page) {
  return shortcutGroups.filter(
    (group) => group.activePages?.includes(ShortcutPage.GLOBAL) || group.activePages?.includes(page)
  );
}

export { Logo as L, ShortcutPage as S, getShortcutsForPage as g, shortcutGroups as s };
//# sourceMappingURL=shortcuts-registry-C7HymBtd.js.map
