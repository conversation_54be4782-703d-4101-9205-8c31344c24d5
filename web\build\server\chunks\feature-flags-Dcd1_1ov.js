import { d as dev } from './index4-HpJcNJHQ.js';
import './false-CRHihH2U.js';

const FEATURE_FLAGS = {
  // Core features
  dashboard: {
    enabled: true,
    bypassInDevelopment: true,
    description: "Main dashboard access"
  },
  profile: {
    enabled: true,
    bypassInDevelopment: true,
    description: "User profile management"
  },
  // Automation features
  automation: {
    enabled: true,
    bypassInDevelopment: true,
    description: "Job automation and application features"
  },
  // Resume features
  resume_scanner: {
    enabled: true,
    bypassInDevelopment: true,
    description: "Resume scanning and analysis"
  },
  ats_optimization: {
    enabled: true,
    bypassInDevelopment: true,
    description: "ATS optimization features"
  },
  // AI features
  cover_letter_generator: {
    enabled: true,
    bypassInDevelopment: true,
    description: "AI-powered cover letter generation"
  },
  ai_matching: {
    enabled: true,
    bypassInDevelopment: true,
    description: "AI job matching"
  },
  // Analytics features
  analytics: {
    enabled: true,
    bypassInDevelopment: true,
    description: "Job market analytics and insights"
  },
  // Team features
  team_collaboration: {
    enabled: true,
    bypassInDevelopment: true,
    description: "Team collaboration features"
  },
  // Integration features
  linkedin_integration: {
    enabled: true,
    bypassInDevelopment: true,
    description: "LinkedIn integration"
  },
  // Communication features
  email_support: {
    enabled: true,
    bypassInDevelopment: true,
    description: "Email support access"
  },
  // Advanced features
  api_access: {
    enabled: true,
    bypassInDevelopment: true,
    description: "API access for integrations"
  },
  custom_branding: {
    enabled: true,
    bypassInDevelopment: true,
    description: "Custom branding options"
  }
};
const ENVIRONMENT_CONFIG = {
  // Disable all feature limits in development
  DISABLE_ALL_LIMITS: typeof window !== "undefined" && localStorage.getItem("disable-feature-limits") === "true",
  // Disable specific features via environment (can be set via localStorage in browser)
  DISABLED_FEATURES: typeof window !== "undefined" ? (localStorage.getItem("disabled-features") || "").split(",").filter(Boolean) : [],
  // Enable all features for testing
  ENABLE_ALL_FEATURES: typeof window !== "undefined" && localStorage.getItem("enable-all-features") === "true",
  // Development mode bypass
  DEVELOPMENT_BYPASS: dev
};
function isFeatureEnabled(featureId) {
  if (ENVIRONMENT_CONFIG.DISABLED_FEATURES.includes("*")) {
    return false;
  }
  if (ENVIRONMENT_CONFIG.ENABLE_ALL_FEATURES) {
    return true;
  }
  if (ENVIRONMENT_CONFIG.DISABLED_FEATURES.includes(featureId)) {
    return false;
  }
  const featureConfig = FEATURE_FLAGS[featureId];
  if (!featureConfig) {
    console.warn(`Feature '${featureId}' not found in FEATURE_FLAGS`);
    return false;
  }
  return featureConfig.enabled;
}
function shouldBypassLimits(featureId) {
  if (ENVIRONMENT_CONFIG.DISABLE_ALL_LIMITS) {
    return true;
  }
  const featureConfig = FEATURE_FLAGS[featureId];
  if (featureConfig?.bypassInDevelopment && ENVIRONMENT_CONFIG.DEVELOPMENT_BYPASS) ;
  return false;
}
function getEnabledFeatures() {
  return Object.entries(FEATURE_FLAGS).filter(([featureId]) => isFeatureEnabled(featureId)).map(([featureId]) => featureId);
}
function getFeatureConfig(featureId) {
  return FEATURE_FLAGS[featureId] || null;
}
function toggleFeature(featureId, enabled) {
  if (FEATURE_FLAGS[featureId]) {
    FEATURE_FLAGS[featureId].enabled = enabled;
    console.log(`Feature '${featureId}' ${enabled ? "enabled" : "disabled"}`);
  }
}

export { ENVIRONMENT_CONFIG, FEATURE_FLAGS, getEnabledFeatures, getFeatureConfig, isFeatureEnabled, shouldBypassLimits, toggleFeature };
//# sourceMappingURL=feature-flags-Dcd1_1ov.js.map
