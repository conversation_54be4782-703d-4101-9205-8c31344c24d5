import { j as json } from './index-Ddp2AB5f.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import { l as logger } from './logger-B9AT-Fsg.js';
import '@prisma/client';

const GET = async ({ params, locals }) => {
  try {
    const user = locals.user;
    if (!user) {
      return new Response("Unauthorized", { status: 401 });
    }
    const id = params.id;
    if (!id) {
      return json({ error: "Worker process ID is required" }, { status: 400 });
    }
    const workerProcess = await prisma.workerProcess.findUnique({
      where: { id }
    });
    if (!workerProcess) {
      return json({ error: "Worker process not found" }, { status: 404 });
    }
    let parsedData = workerProcess.data;
    if (typeof parsedData === "string") {
      try {
        parsedData = JSON.parse(parsedData);
      } catch (parseError) {
        logger.warn(`Failed to parse worker process data for ${id}:`, parseError);
      }
    }
    return json({
      success: true,
      workerProcess: {
        ...workerProcess,
        data: parsedData
      }
    });
  } catch (error) {
    logger.error("Error retrieving worker process:", error);
    return json(
      {
        success: false,
        error: "Failed to retrieve worker process",
        message: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
};
const PATCH = async ({ params, request, locals }) => {
  try {
    const user = locals.user;
    if (!user) {
      return new Response("Unauthorized", { status: 401 });
    }
    const id = params.id;
    if (!id) {
      return json({ error: "Worker process ID is required" }, { status: 400 });
    }
    const body = await request.json();
    const updateData = {};
    if (body.status) {
      updateData.status = body.status;
      if (body.status === "PROCESSING" && !body.startedAt) {
        updateData.startedAt = /* @__PURE__ */ new Date();
      } else if (body.status === "COMPLETED" || body.status === "FAILED") {
        updateData.completedAt = /* @__PURE__ */ new Date();
      }
    }
    if (body.data !== void 0) {
      updateData.data = typeof body.data === "string" ? body.data : JSON.stringify(body.data || {});
    }
    if (body.error !== void 0) {
      updateData.error = body.error;
    }
    const workerProcess = await prisma.workerProcess.update({
      where: { id },
      data: updateData
    });
    return json({
      success: true,
      workerProcess,
      message: `Worker process ${id} updated`
    });
  } catch (error) {
    logger.error("Error updating worker process:", error);
    return json(
      {
        success: false,
        error: "Failed to update worker process",
        message: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
};
const DELETE = async ({ params, locals }) => {
  try {
    const user = locals.user;
    if (!user) {
      return new Response("Unauthorized", { status: 401 });
    }
    const id = params.id;
    if (!id) {
      return json({ error: "Worker process ID is required" }, { status: 400 });
    }
    await prisma.workerProcess.delete({
      where: { id }
    });
    return json({
      success: true,
      message: `Worker process ${id} deleted`
    });
  } catch (error) {
    logger.error("Error deleting worker process:", error);
    return json(
      {
        success: false,
        error: "Failed to delete worker process",
        message: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
};

export { DELETE, GET, PATCH };
//# sourceMappingURL=_server.ts-7WdXV9oJ.js.map
