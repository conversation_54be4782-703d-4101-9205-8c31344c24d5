import { r as redirect } from './index-Ddp2AB5f.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import { getFeatureUsage } from './feature-usage-SYWaZZiX.js';
import { F as FEATURES } from './dynamic-registry-Cmy1Wm2Q.js';
import { a as FeatureAccessLevel } from './features-SWeUHekJ.js';
import '@prisma/client';
import './index4-HpJcNJHQ.js';
import './false-CRHihH2U.js';

const load = async ({ locals }) => {
  const user = locals.user;
  if (!user) {
    throw redirect(302, "/auth/sign-in");
  }
  const userData = await prisma.user.findUnique({
    where: { id: user.id },
    select: {
      id: true,
      email: true,
      name: true,
      role: true,
      image: true
    }
  });
  if (!userData) {
    throw redirect(302, "/auth/sign-in");
  }
  const simplifiedUser = {
    id: userData.id,
    email: userData.email,
    name: userData.name,
    role: userData.role,
    image: userData.image
  };
  let featureUsage = [];
  let usageTrends = {
    currentMonthTotal: 0,
    previousMonthTotal: 0,
    trendPercent: 0,
    trendDirection: "stable"
  };
  try {
    const usageData = await getFeatureUsage(userData.id);
    console.log(`Loaded ${usageData.length} feature usage records for user ${userData.id}`);
    const now = /* @__PURE__ */ new Date();
    const currentMonth = now.toISOString().substring(0, 7);
    const prevMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1).toISOString().substring(0, 7);
    const currentMonthUsage = usageData.filter((u) => u.period === currentMonth);
    const prevMonthUsage = usageData.filter((u) => u.period === prevMonth);
    const currentTotalUsed = currentMonthUsage.reduce((sum, u) => sum + u.used, 0);
    const prevTotalUsed = prevMonthUsage.reduce((sum, u) => sum + u.used, 0);
    let usageTrendPercent = 0;
    if (prevTotalUsed > 0) {
      usageTrendPercent = Math.round((currentTotalUsed - prevTotalUsed) / prevTotalUsed * 100);
    }
    featureUsage = FEATURES.map((feature) => {
      const featureUsageData = usageData.filter((usage2) => usage2.featureId === feature.id);
      const usage = featureUsageData.length > 0 ? featureUsageData.map((usage2) => ({
        limitId: usage2.limitId,
        limitName: usage2.limitName,
        used: usage2.used,
        limit: usage2.limit === null ? "unlimited" : usage2.limit,
        remaining: usage2.remaining,
        percentUsed: usage2.percentUsed,
        period: usage2.period,
        description: usage2.description || ""
      })) : feature.limits?.map((limit) => ({
        limitId: limit.id,
        limitName: limit.name,
        used: 0,
        limit: limit.defaultValue || "unlimited",
        remaining: limit.defaultValue || "unlimited",
        percentUsed: 0,
        period: currentMonth,
        description: limit.description || "",
        placeholder: true
        // Mark as placeholder
      })) || [];
      let accessLevel = FeatureAccessLevel.NotIncluded;
      if (usage.length > 0) {
        if (usage.some((u) => u.limit === "unlimited")) {
          accessLevel = FeatureAccessLevel.Unlimited;
        } else if (usage.every(
          (u) => u.limit === "unlimited" || typeof u.limit === "number" && u.limit > 0
        )) {
          accessLevel = FeatureAccessLevel.Limited;
        } else {
          accessLevel = FeatureAccessLevel.Included;
        }
      }
      const currentFeatureUsage = currentMonthUsage.filter((u) => u.featureId === feature.id).reduce((sum, u) => sum + u.used, 0);
      const prevFeatureUsage = prevMonthUsage.filter((u) => u.featureId === feature.id).reduce((sum, u) => sum + u.used, 0);
      let featureTrendPercent = 0;
      if (prevFeatureUsage > 0) {
        featureTrendPercent = Math.round(
          (currentFeatureUsage - prevFeatureUsage) / prevFeatureUsage * 100
        );
      }
      return {
        ...feature,
        usage,
        accessLevel,
        currentUsage: currentFeatureUsage,
        previousUsage: prevFeatureUsage,
        trendPercent: featureTrendPercent
      };
    });
    usageTrends = {
      currentMonthTotal: currentTotalUsed,
      previousMonthTotal: prevTotalUsed,
      trendPercent: usageTrendPercent,
      trendDirection: usageTrendPercent > 0 ? "up" : usageTrendPercent < 0 ? "down" : "stable"
    };
    console.log(`Mapped ${featureUsage.length} features with usage data`);
  } catch (error) {
    console.error("Error loading feature usage:", error);
    featureUsage = FEATURES.map((feature) => ({
      ...feature,
      usage: feature.limits?.map((limit) => ({
        limitId: limit.id,
        limitName: limit.name,
        used: 0,
        limit: limit.defaultValue || "unlimited",
        remaining: limit.defaultValue || "unlimited",
        percentUsed: 0,
        period: (/* @__PURE__ */ new Date()).toISOString().substring(0, 7),
        description: limit.description || "",
        placeholder: true
      })) || [],
      accessLevel: FeatureAccessLevel.NotIncluded,
      currentUsage: 0,
      previousUsage: 0,
      trendPercent: 0
    }));
  }
  return {
    user: simplifiedUser,
    featureUsage,
    usageTrends
  };
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 70;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-DtGwnLWw.js')).default;
const server_id = "src/routes/dashboard/settings/usage/+page.server.ts";
const imports = ["_app/immutable/nodes/70.BkpDw6vP.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/DuGukytH.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/Cdn-N1RY.js","_app/immutable/chunks/BkJY4La4.js","_app/immutable/chunks/GwmmX_iF.js","_app/immutable/chunks/D50jIuLr.js","_app/immutable/chunks/I7hvcB12.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/OXTnUuEm.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/XnZcpgwi.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/DYwWIJ9y.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/Dq03aqGn.js","_app/immutable/chunks/BHEV2D3b.js","_app/immutable/chunks/BniYvUIG.js","_app/immutable/chunks/BYB878do.js","_app/immutable/chunks/BQ5jqT_2.js","_app/immutable/chunks/CbynRejM.js","_app/immutable/chunks/BhzFx1Wy.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Csk_I0QV.js","_app/immutable/chunks/lirlZJ-b.js","_app/immutable/chunks/DrGkVJ95.js","_app/immutable/chunks/DaBofrVv.js","_app/immutable/chunks/B-l1ubNa.js","_app/immutable/chunks/DW7T7T22.js","_app/immutable/chunks/-SpbofVw.js","_app/immutable/chunks/CTO_B1Jk.js","_app/immutable/chunks/iTBjRg9v.js","_app/immutable/chunks/DQC1iFs0.js","_app/immutable/chunks/DZCYCPd3.js","_app/immutable/chunks/qwsZpUIl.js","_app/immutable/chunks/C88uNE8B.js","_app/immutable/chunks/DmZyh-PW.js","_app/immutable/chunks/BwkAotBa.js"];
const stylesheets = ["_app/immutable/assets/chart-tooltip.BTdU6mpn.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=70-B5TsXxRh.js.map
