import { j as json } from './index-Ddp2AB5f.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import { l as logger } from './logger-B9AT-Fsg.js';
import { createNotification, NotificationType } from './notifications-CbZhIepm.js';
import '@prisma/client';
import './redis-DxlM1ibh.js';
import 'ioredis';

const GET = async ({ url, locals }) => {
  try {
    const user = locals.user;
    if (!user) {
      return new Response("Unauthorized", { status: 401 });
    }
    const id = url.searchParams.get("id");
    const type = url.searchParams.get("type");
    const status = url.searchParams.get("status");
    const query = {};
    if (id) {
      query.id = id;
    }
    if (type) {
      query.type = type;
    }
    if (status) {
      query.status = status;
    }
    let workerProcesses;
    if (id) {
      workerProcesses = await prisma.workerProcess.findUnique({
        where: { id }
      });
      if (!workerProcesses) {
        return json({ error: "Worker process not found" }, { status: 404 });
      }
    } else {
      workerProcesses = await prisma.workerProcess.findMany({
        where: query,
        orderBy: { createdAt: "desc" }
      });
    }
    return json({ success: true, workerProcesses });
  } catch (error) {
    logger.error("Error retrieving worker process:", error);
    return json(
      {
        success: false,
        error: "Failed to retrieve worker process",
        message: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
};
const POST = async ({ request, locals }) => {
  try {
    let user = locals.user;
    const isDevelopment = process.env.NODE_ENV === "development";
    if (!user && !isDevelopment) {
      return new Response("Unauthorized", { status: 401 });
    }
    const body = await request.json();
    const { type, status, data } = body;
    if (!type) {
      return json({ error: "Worker process type is required" }, { status: 400 });
    }
    if (!status) {
      return json({ error: "Worker process status is required" }, { status: 400 });
    }
    const id = body.id ?? `wp_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    const workerProcess = await prisma.workerProcess.create({
      data: {
        id,
        type,
        status,
        data: typeof data === "string" ? data : JSON.stringify(data ?? {}),
        createdAt: /* @__PURE__ */ new Date(),
        updatedAt: /* @__PURE__ */ new Date(),
        startedAt: status === "PENDING" ? null : /* @__PURE__ */ new Date()
      }
    });
    if (type === "resume-parsing") {
      try {
        let resumeId = null;
        let profileId = null;
        if (typeof data === "object") {
          resumeId = data.resumeId ?? null;
          profileId = data.profileId ?? null;
        } else if (typeof data === "string") {
          try {
            const parsedData = JSON.parse(data);
            resumeId = parsedData.resumeId ?? null;
            profileId = parsedData.profileId ?? null;
          } catch (parseError) {
            logger.error("Error parsing data string:", parseError);
          }
        }
        if (resumeId && user?.id === "system") {
          try {
            const resume = await prisma.resume.findUnique({
              where: { id: resumeId },
              include: { document: true }
            });
            if (resume?.document?.userId) {
              user = { ...user, id: resume.document.userId };
              logger.info(`Found user ID ${resume.document.userId} for resume ${resumeId}`);
            }
          } catch (userIdError) {
            logger.error("Error getting user ID from resume:", userIdError);
          }
        }
        if (resumeId) {
          if (user?.id) {
            await createNotification({
              userId: user.id,
              title: "Resume Parsing Started",
              message: "Your resume is being parsed. This may take a few moments.",
              type: NotificationType.INFO,
              data: {
                resumeId,
                profileId,
                workerProcessId: id
              }
            });
          }
          logger.info(
            `Created resume parsing notification for user ${user?.id ?? "system"}, resume ${resumeId}`
          );
        }
      } catch (error) {
        logger.error("Error creating notification or broadcasting message:", error);
      }
    }
    return json({
      success: true,
      workerProcess,
      message: `Worker process created with ID ${id}`
    });
  } catch (error) {
    logger.error("Error creating worker process:", error);
    return json(
      {
        success: false,
        error: "Failed to create worker process",
        message: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
};
const PATCH = async ({ request, locals }) => {
  try {
    let user = locals.user;
    const isDevelopment = process.env.NODE_ENV === "development";
    if (!user && !isDevelopment) {
      return new Response("Unauthorized", { status: 401 });
    }
    const body = await request.json();
    const { id, status, data } = body;
    if (!id) {
      return json({ error: "Worker process ID is required" }, { status: 400 });
    }
    const updateData = {};
    if (status) {
      updateData.status = status;
      if (status === "PROCESSING" && !body.startedAt) {
        updateData.startedAt = /* @__PURE__ */ new Date();
      } else if (status === "COMPLETED" || status === "FAILED") {
        updateData.completedAt = /* @__PURE__ */ new Date();
      }
    }
    if (data !== void 0) {
      updateData.data = typeof data === "string" ? data : JSON.stringify(data ?? {});
    }
    if (body.error !== void 0) {
      updateData.error = body.error;
    }
    const workerProcess = await prisma.workerProcess.update({
      where: { id },
      data: updateData
    });
    if (workerProcess.type === "resume-parsing" && (status === "COMPLETED" || status === "FAILED")) {
      try {
        let resumeId = null;
        let profileId = null;
        let parsedData = null;
        if (typeof workerProcess.data === "string") {
          try {
            const data2 = JSON.parse(workerProcess.data);
            resumeId = data2.resumeId ?? null;
            profileId = data2.profileId ?? null;
            parsedData = data2.parsedData ?? null;
          } catch (parseError) {
            logger.error("Error parsing worker process data:", parseError);
          }
        } else if (typeof workerProcess.data === "object" && workerProcess.data !== null && !Array.isArray(workerProcess.data)) {
          const dataObj = workerProcess.data;
          resumeId = dataObj.resumeId ?? null;
          profileId = dataObj.profileId ?? null;
          parsedData = dataObj.parsedData ?? null;
        }
        if (resumeId && user?.id === "system") {
          try {
            const resume = await prisma.resume.findUnique({
              where: { id: resumeId },
              include: { document: true }
            });
            if (resume?.document?.userId) {
              user = { ...user, id: resume.document.userId };
              logger.info(`Found user ID ${resume.document.userId} for resume ${resumeId}`);
            }
          } catch (userIdError) {
            logger.error("Error getting user ID from resume:", userIdError);
          }
        }
        if (resumeId) {
          if (user?.id) {
            await createNotification({
              userId: user?.id ?? "system",
              title: status === "COMPLETED" ? "Resume Parsing Completed" : "Resume Parsing Failed",
              message: status === "COMPLETED" ? "Your resume has been successfully parsed." : "There was an error parsing your resume. Please try again.",
              type: status === "COMPLETED" ? NotificationType.SUCCESS : NotificationType.ERROR,
              data: {
                resumeId,
                profileId,
                workerProcessId: id,
                parsedData: status === "COMPLETED" ? parsedData : null
              }
            });
          }
          logger.info(
            `Created resume parsing ${status.toLowerCase()} notification for user ${user?.id ?? "system"}, resume ${resumeId}`
          );
        }
      } catch (notificationError) {
        logger.error("Error creating notification or broadcasting message:", notificationError);
      }
    }
    return json({
      success: true,
      workerProcess,
      message: `Worker process ${id} updated`
    });
  } catch (error) {
    logger.error("Error updating worker process:", error);
    return json(
      {
        success: false,
        error: "Failed to update worker process",
        message: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
};

export { GET, PATCH, POST };
//# sourceMappingURL=_server.ts-B6BpThHV.js.map
