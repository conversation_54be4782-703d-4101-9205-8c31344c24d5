{"version": 3, "file": "notifications-CbZhIepm.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/notifications.js"], "sourcesContent": ["import { p as prisma } from \"./prisma.js\";\nimport { g as getRedisClient } from \"./redis.js\";\nvar NotificationType = /* @__PURE__ */ ((NotificationType2) => {\n  NotificationType2[\"INFO\"] = \"info\";\n  NotificationType2[\"SUCCESS\"] = \"success\";\n  NotificationType2[\"WARNING\"] = \"warning\";\n  NotificationType2[\"ERROR\"] = \"error\";\n  NotificationType2[\"SYSTEM\"] = \"system\";\n  NotificationType2[\"JOB\"] = \"job\";\n  NotificationType2[\"APPLICATION\"] = \"application\";\n  NotificationType2[\"RESUME\"] = \"resume\";\n  NotificationType2[\"REFERRAL\"] = \"referral\";\n  return NotificationType2;\n})(NotificationType || {});\nvar NotificationPriority = /* @__PURE__ */ ((NotificationPriority2) => {\n  NotificationPriority2[\"LOW\"] = \"low\";\n  NotificationPriority2[\"MEDIUM\"] = \"medium\";\n  NotificationPriority2[\"HIGH\"] = \"high\";\n  return NotificationPriority2;\n})(NotificationPriority || {});\nasync function createNotification(data) {\n  try {\n    if (data.userId === \"system\") {\n      console.log(\"Creating system notification (no user)\");\n    } else {\n      const preferences = await prisma.notificationSettings.findUnique({\n        where: { userId: data.userId }\n      });\n      if (preferences && !preferences.browserEnabled) {\n        console.log(`User ${data.userId} has disabled in-app notifications`);\n        return false;\n      }\n    }\n    const notificationData = {\n      userId: data.userId,\n      title: data.title,\n      message: data.message,\n      url: data.url,\n      type: data.type || \"info\",\n      priority: data.priority || \"medium\",\n      metadata: data.data ? JSON.stringify(data.data) : null,\n      expiresAt: data.expiresAt,\n      read: false\n    };\n    let notification;\n    if (data.userId === \"system\") {\n      notification = await prisma.notification.create({\n        data: {\n          ...notificationData,\n          userId: null,\n          // No specific user\n          global: true\n          // Make it a global notification\n        }\n      });\n      console.log(`Created global system notification in database with ID ${notification.id}`);\n    } else {\n      notification = await prisma.notification.create({\n        data: notificationData\n      });\n      console.log(`Created user notification in database with ID ${notification.id}`);\n    }\n    const redis = await getRedisClient();\n    if (!redis) {\n      console.error(\"Redis client not available\");\n      return false;\n    }\n    const requestId = `notification:${notification.id}:${Date.now()}`;\n    const notificationMessage = {\n      id: notification.id,\n      title: notification.title,\n      message: notification.message,\n      url: notification.url,\n      type: notification.type,\n      timestamp: notification.createdAt.toISOString(),\n      requestId\n    };\n    console.log(`Publishing notification to Redis for user ${data.userId}:`, notificationMessage);\n    await redis.publish(`user:${data.userId}:notifications`, JSON.stringify(notificationMessage));\n    try {\n      const { broadcastMessage } = await import(\"./websocket.js\");\n      broadcastMessage({\n        type: \"notification\",\n        data: notificationMessage,\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      });\n      console.log(`Broadcast notification via WebSocket for user ${data.userId}`);\n    } catch (wsError) {\n      console.error(\"Error broadcasting notification via WebSocket:\", wsError);\n    }\n    return true;\n  } catch (error) {\n    console.error(\"Error creating notification:\", error);\n    return false;\n  }\n}\nexport {\n  NotificationPriority,\n  NotificationType,\n  createNotification\n};\n"], "names": [], "mappings": ";;;;;AAEG,IAAC,gBAAgB,mBAAmB,CAAC,CAAC,iBAAiB,KAAK;AAC/D,EAAE,iBAAiB,CAAC,MAAM,CAAC,GAAG,MAAM;AACpC,EAAE,iBAAiB,CAAC,SAAS,CAAC,GAAG,SAAS;AAC1C,EAAE,iBAAiB,CAAC,SAAS,CAAC,GAAG,SAAS;AAC1C,EAAE,iBAAiB,CAAC,OAAO,CAAC,GAAG,OAAO;AACtC,EAAE,iBAAiB,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACxC,EAAE,iBAAiB,CAAC,KAAK,CAAC,GAAG,KAAK;AAClC,EAAE,iBAAiB,CAAC,aAAa,CAAC,GAAG,aAAa;AAClD,EAAE,iBAAiB,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACxC,EAAE,iBAAiB,CAAC,UAAU,CAAC,GAAG,UAAU;AAC5C,EAAE,OAAO,iBAAiB;AAC1B,CAAC,EAAE,gBAAgB,IAAI,EAAE;AACtB,IAAC,oBAAoB,mBAAmB,CAAC,CAAC,qBAAqB,KAAK;AACvE,EAAE,qBAAqB,CAAC,KAAK,CAAC,GAAG,KAAK;AACtC,EAAE,qBAAqB,CAAC,QAAQ,CAAC,GAAG,QAAQ;AAC5C,EAAE,qBAAqB,CAAC,MAAM,CAAC,GAAG,MAAM;AACxC,EAAE,OAAO,qBAAqB;AAC9B,CAAC,EAAE,oBAAoB,IAAI,EAAE;AAC7B,eAAe,kBAAkB,CAAC,IAAI,EAAE;AACxC,EAAE,IAAI;AACN,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE;AAClC,MAAM,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC;AAC3D,KAAK,MAAM;AACX,MAAM,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,oBAAoB,CAAC,UAAU,CAAC;AACvE,QAAQ,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM;AACpC,OAAO,CAAC;AACR,MAAM,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE;AACtD,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,kCAAkC,CAAC,CAAC;AAC5E,QAAQ,OAAO,KAAK;AACpB;AACA;AACA,IAAI,MAAM,gBAAgB,GAAG;AAC7B,MAAM,MAAM,EAAE,IAAI,CAAC,MAAM;AACzB,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK;AACvB,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;AAC3B,MAAM,GAAG,EAAE,IAAI,CAAC,GAAG;AACnB,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,MAAM;AAC/B,MAAM,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,QAAQ;AACzC,MAAM,QAAQ,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;AAC5D,MAAM,SAAS,EAAE,IAAI,CAAC,SAAS;AAC/B,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI,IAAI,YAAY;AACpB,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE;AAClC,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AACtD,QAAQ,IAAI,EAAE;AACd,UAAU,GAAG,gBAAgB;AAC7B,UAAU,MAAM,EAAE,IAAI;AACtB;AACA,UAAU,MAAM,EAAE;AAClB;AACA;AACA,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,uDAAuD,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9F,KAAK,MAAM;AACX,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AACtD,QAAQ,IAAI,EAAE;AACd,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,8CAA8C,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;AACrF;AACA,IAAI,MAAM,KAAK,GAAG,MAAM,cAAc,EAAE;AACxC,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC;AACjD,MAAM,OAAO,KAAK;AAClB;AACA,IAAI,MAAM,SAAS,GAAG,CAAC,aAAa,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AACrE,IAAI,MAAM,mBAAmB,GAAG;AAChC,MAAM,EAAE,EAAE,YAAY,CAAC,EAAE;AACzB,MAAM,KAAK,EAAE,YAAY,CAAC,KAAK;AAC/B,MAAM,OAAO,EAAE,YAAY,CAAC,OAAO;AACnC,MAAM,GAAG,EAAE,YAAY,CAAC,GAAG;AAC3B,MAAM,IAAI,EAAE,YAAY,CAAC,IAAI;AAC7B,MAAM,SAAS,EAAE,YAAY,CAAC,SAAS,CAAC,WAAW,EAAE;AACrD,MAAM;AACN,KAAK;AACL,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,0CAA0C,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,mBAAmB,CAAC;AACjG,IAAI,MAAM,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;AACjG,IAAI,IAAI;AACR,MAAM,MAAM,EAAE,gBAAgB,EAAE,GAAG,MAAM,OAAO,yBAAgB,CAAC;AACjE,MAAM,gBAAgB,CAAC;AACvB,QAAQ,IAAI,EAAE,cAAc;AAC5B,QAAQ,IAAI,EAAE,mBAAmB;AACjC,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,8CAA8C,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACjF,KAAK,CAAC,OAAO,OAAO,EAAE;AACtB,MAAM,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE,OAAO,CAAC;AAC9E;AACA,IAAI,OAAO,IAAI;AACf,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACxD,IAAI,OAAO,KAAK;AAChB;AACA;;;;"}