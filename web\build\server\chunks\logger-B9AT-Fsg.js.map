{"version": 3, "file": "logger-B9AT-Fsg.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/logger.js"], "sourcesContent": ["class Logger {\n  debug(message, ...args) {\n  }\n  info(message, ...args) {\n    {\n      console.info(`[INFO] ${message}`, ...args);\n    }\n  }\n  warn(message, ...args) {\n    {\n      console.warn(`[WARN] ${message}`, ...args);\n    }\n  }\n  error(message, ...args) {\n    {\n      console.error(`[ERROR] ${message}`, ...args);\n    }\n  }\n}\nconst logger = new Logger();\nexport {\n  logger as l\n};\n"], "names": [], "mappings": "AAAA,MAAM,MAAM,CAAC;AACb,EAAE,KAAK,CAAC,OAAO,EAAE,GAAG,IAAI,EAAE;AAC1B;AACA,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,EAAE;AACzB,IAAI;AACJ,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC;AAChD;AACA;AACA,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,EAAE;AACzB,IAAI;AACJ,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC;AAChD;AACA;AACA,EAAE,KAAK,CAAC,OAAO,EAAE,GAAG,IAAI,EAAE;AAC1B,IAAI;AACJ,MAAM,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC;AAClD;AACA;AACA;AACK,MAAC,MAAM,GAAG,IAAI,MAAM;;;;"}