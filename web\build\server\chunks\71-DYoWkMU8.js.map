{"version": 3, "file": "71-DYoWkMU8.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/jobApplication.js", "../../../.svelte-kit/adapter-node/entries/pages/dashboard/tracker/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/71.js"], "sourcesContent": ["import { z } from \"zod\";\nconst jobApplicationSchema = z.object({\n  company: z.string().min(1, { message: \"Company is required\" }),\n  position: z.string().min(1, { message: \"Position is required\" }),\n  location: z.string().optional(),\n  appliedDate: z.string().min(1, { message: \"Applied date is required\" }),\n  status: z.string().min(1, { message: \"Status is required\" }),\n  nextAction: z.string().optional(),\n  notes: z.string().optional(),\n  url: z.string().url({ message: \"Must be a valid URL\" }).optional().or(z.literal(\"\")),\n  jobType: z.string().min(1, { message: \"Job type is required\" }),\n  resumeUploaded: z.string().min(1, { message: \"Resume status is required\" })\n});\nexport {\n  jobApplicationSchema as j\n};\n", "import { f as fail } from \"../../../../chunks/index.js\";\nimport { s as superValidate } from \"../../../../chunks/superValidate.js\";\nimport { j as jobApplicationSchema } from \"../../../../chunks/jobApplication.js\";\nimport \"ts-deepmerge\";\nimport \"memoize-weak\";\nimport { z as zod } from \"../../../../chunks/zod.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../chunks/auth.js\";\nconst load = async ({ cookies }) => {\n  const form = await superValidate(zod(jobApplicationSchema));\n  const token = cookies.get(\"auth_token\");\n  const user = token ? await verifySessionToken(token) : null;\n  if (!user) {\n    return {\n      form,\n      applications: []\n    };\n  }\n  try {\n    const applications = await prisma.application.findMany({\n      where: {\n        userId: user.id\n      },\n      orderBy: {\n        appliedDate: \"desc\"\n      }\n    });\n    const transformedApplications = applications.map((app) => ({\n      id: app.id,\n      company: app.company,\n      position: app.position,\n      location: app.location || \"Remote\",\n      appliedDate: app.appliedDate.toISOString().split(\"T\")[0],\n      // Format as YYYY-MM-DD\n      status: app.status,\n      nextAction: app.nextAction || \"\",\n      notes: app.notes || \"\",\n      logo: \"https://placehold.co/100x100\",\n      // Default logo for now\n      url: app.url || \"\",\n      jobType: app.jobType || \"Full-time\",\n      resumeUploaded: app.resumeUploaded ? \"Yes\" : \"No\"\n    }));\n    return {\n      form,\n      applications: transformedApplications\n    };\n  } catch (error) {\n    console.error(\"Error loading applications:\", error);\n    return {\n      form,\n      applications: []\n    };\n  }\n};\nconst actions = {\n  addJob: async ({ request, cookies }) => {\n    const form = await superValidate(request, zod(jobApplicationSchema));\n    if (!form.valid) {\n      return fail(400, { form });\n    }\n    const token = cookies.get(\"auth_token\");\n    const user = token ? await verifySessionToken(token) : null;\n    if (!user) {\n      return fail(401, { form, error: \"Unauthorized\" });\n    }\n    try {\n      const application = await prisma.application.create({\n        data: {\n          userId: user.id,\n          company: form.data.company,\n          position: form.data.position,\n          location: form.data.location || null,\n          appliedDate: new Date(form.data.appliedDate),\n          status: form.data.status,\n          nextAction: form.data.nextAction || null,\n          notes: form.data.notes || null,\n          url: form.data.url || null,\n          jobType: form.data.jobType,\n          resumeUploaded: form.data.resumeUploaded === \"Yes\"\n        }\n      });\n      return {\n        form,\n        success: true,\n        application\n      };\n    } catch (error) {\n      console.error(\"Error creating application:\", error);\n      return fail(500, { form, error: \"Failed to create application\" });\n    }\n  }\n};\nexport {\n  actions,\n  load\n};\n", "import * as server from '../entries/pages/dashboard/tracker/_page.server.ts.js';\n\nexport const index = 71;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/tracker/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/tracker/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/71.DncvvIhb.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/CeJyStlM.js\",\"_app/immutable/chunks/C0-qpl0T.js\",\"_app/immutable/chunks/DQC1iFs0.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/CrHU05dq.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/DjPYYl4Z.js\",\"_app/immutable/chunks/C8B1VUaq.js\",\"_app/immutable/chunks/DaBofrVv.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/I7hvcB12.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/OXTnUuEm.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/CGK0g3x_.js\",\"_app/immutable/chunks/D2egQzE8.js\",\"_app/immutable/chunks/Ntteq2n_.js\",\"_app/immutable/chunks/DrQfh6BY.js\",\"_app/immutable/chunks/DxW95yuQ.js\",\"_app/immutable/chunks/D-o7ybA5.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/BaVT73bJ.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/Cb-3cdbh.js\",\"_app/immutable/chunks/BjCTmJLi.js\",\"_app/immutable/chunks/CzsE_FAw.js\",\"_app/immutable/chunks/3WmhYGjL.js\",\"_app/immutable/chunks/CyaAPBlz.js\",\"_app/immutable/chunks/0ykhD7u6.js\",\"_app/immutable/chunks/CQdOabBG.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/DDUgF6Ik.js\",\"_app/immutable/chunks/tdzGgazS.js\",\"_app/immutable/chunks/DMoa_yM9.js\",\"_app/immutable/chunks/CnpHcmx3.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/BvvicRXk.js\",\"_app/immutable/chunks/DMTMHyMa.js\",\"_app/immutable/chunks/VNuMAkuB.js\",\"_app/immutable/chunks/Ci8yIwIB.js\",\"_app/immutable/chunks/Cf6rS4LV.js\",\"_app/immutable/chunks/B6TiSgAN.js\",\"_app/immutable/chunks/Dmwghw4a.js\",\"_app/immutable/chunks/BniYvUIG.js\",\"_app/immutable/chunks/DW5gea7N.js\",\"_app/immutable/chunks/B5tu6DNS.js\",\"_app/immutable/chunks/BwkAotBa.js\",\"_app/immutable/chunks/BNEH2jqx.js\",\"_app/immutable/chunks/CKh8VGVX.js\",\"_app/immutable/chunks/BKLOCbjP.js\",\"_app/immutable/chunks/B2lQHLf_.js\",\"_app/immutable/chunks/CVVv9lPb.js\",\"_app/immutable/chunks/DZCYCPd3.js\",\"_app/immutable/chunks/BhzFx1Wy.js\",\"_app/immutable/chunks/DR5zc253.js\",\"_app/immutable/chunks/BYB878do.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/T7uRAIbG.js\",\"_app/immutable/chunks/C2AK_5VT.js\",\"_app/immutable/chunks/CwgkX8t9.js\",\"_app/immutable/chunks/BHEV2D3b.js\",\"_app/immutable/chunks/C2MdR6K0.js\",\"_app/immutable/chunks/hQ6uUXJy.js\",\"_app/immutable/chunks/WD4kvFhR.js\",\"_app/immutable/chunks/hrXlVaSN.js\",\"_app/immutable/chunks/D871oxnv.js\",\"_app/immutable/chunks/8b74MdfD.js\",\"_app/immutable/chunks/BgDjIxoO.js\",\"_app/immutable/chunks/Dz4exfp3.js\",\"_app/immutable/chunks/Z6UAQTuv.js\",\"_app/immutable/chunks/Cs0qIT7f.js\",\"_app/immutable/chunks/DVGNPJty.js\",\"_app/immutable/chunks/C33xR25f.js\",\"_app/immutable/chunks/BAIxhb6t.js\",\"_app/immutable/chunks/BSHZ37s_.js\",\"_app/immutable/chunks/CKg8MWp_.js\",\"_app/immutable/chunks/-SpbofVw.js\",\"_app/immutable/chunks/CIPPbbaT.js\",\"_app/immutable/chunks/CTn0v-X8.js\",\"_app/immutable/chunks/P6MDDUUJ.js\",\"_app/immutable/chunks/BHzYYMdu.js\",\"_app/immutable/chunks/DumgozFE.js\",\"_app/immutable/chunks/QtAhPN2H.js\",\"_app/immutable/chunks/DW7T7T22.js\",\"_app/immutable/chunks/C88uNE8B.js\",\"_app/immutable/chunks/CDnvByek.js\",\"_app/immutable/chunks/DmZyh-PW.js\",\"_app/immutable/chunks/yW0TxTga.js\",\"_app/immutable/chunks/CrpvsheG.js\",\"_app/immutable/chunks/DdoUfFy4.js\",\"_app/immutable/chunks/tr-scC-m.js\",\"_app/immutable/chunks/G5Oo-PmU.js\"];\nexport const stylesheets = [\"_app/immutable/assets/Toaster.DKF17Rty.css\",\"_app/immutable/assets/index.CV-KWLNP.css\",\"_app/immutable/assets/scroll-area.bHHIbcsu.css\",\"_app/immutable/assets/71.DMVPnmWB.css\"];\nexport const fonts = [];\n"], "names": ["z.object", "z.string", "z.literal"], "mappings": ";;;;;;AACK,MAAC,oBAAoB,GAAGA,UAAQ,CAAC;AACtC,EAAE,OAAO,EAAEC,UAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC;AAChE,EAAE,QAAQ,EAAEA,UAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;AAClE,EAAE,QAAQ,EAAEA,UAAQ,EAAE,CAAC,QAAQ,EAAE;AACjC,EAAE,WAAW,EAAEA,UAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;AACzE,EAAE,MAAM,EAAEA,UAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,EAAE,oBAAoB,EAAE,CAAC;AAC9D,EAAE,UAAU,EAAEA,UAAQ,EAAE,CAAC,QAAQ,EAAE;AACnC,EAAE,KAAK,EAAEA,UAAQ,EAAE,CAAC,QAAQ,EAAE;AAC9B,EAAE,GAAG,EAAEA,UAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,EAAE,CAACC,WAAS,CAAC,EAAE,CAAC,CAAC;AACtF,EAAE,OAAO,EAAED,UAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;AACjE,EAAE,cAAc,EAAEA,UAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,EAAE,2BAA2B,EAAE;AAC5E,CAAC;;ACJD,MAAM,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACpC,EAAE,MAAM,IAAI,GAAG,MAAM,aAAa,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;AAC7D,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,MAAM,IAAI,GAAG,KAAK,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC,GAAG,IAAI;AAC7D,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO;AACX,MAAM,IAAI;AACV,MAAM,YAAY,EAAE;AACpB,KAAK;AACL;AACA,EAAE,IAAI;AACN,IAAI,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAC3D,MAAM,KAAK,EAAE;AACb,QAAQ,MAAM,EAAE,IAAI,CAAC;AACrB,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,WAAW,EAAE;AACrB;AACA,KAAK,CAAC;AACN,IAAI,MAAM,uBAAuB,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AAC/D,MAAM,EAAE,EAAE,GAAG,CAAC,EAAE;AAChB,MAAM,OAAO,EAAE,GAAG,CAAC,OAAO;AAC1B,MAAM,QAAQ,EAAE,GAAG,CAAC,QAAQ;AAC5B,MAAM,QAAQ,EAAE,GAAG,CAAC,QAAQ,IAAI,QAAQ;AACxC,MAAM,WAAW,EAAE,GAAG,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9D;AACA,MAAM,MAAM,EAAE,GAAG,CAAC,MAAM;AACxB,MAAM,UAAU,EAAE,GAAG,CAAC,UAAU,IAAI,EAAE;AACtC,MAAM,KAAK,EAAE,GAAG,CAAC,KAAK,IAAI,EAAE;AAC5B,MAAM,IAAI,EAAE,8BAA8B;AAC1C;AACA,MAAM,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,EAAE;AACxB,MAAM,OAAO,EAAE,GAAG,CAAC,OAAO,IAAI,WAAW;AACzC,MAAM,cAAc,EAAE,GAAG,CAAC,cAAc,GAAG,KAAK,GAAG;AACnD,KAAK,CAAC,CAAC;AACP,IAAI,OAAO;AACX,MAAM,IAAI;AACV,MAAM,YAAY,EAAE;AACpB,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACvD,IAAI,OAAO;AACX,MAAM,IAAI;AACV,MAAM,YAAY,EAAE;AACpB,KAAK;AACL;AACA,CAAC;AACD,MAAM,OAAO,GAAG;AAChB,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC1C,IAAI,MAAM,IAAI,GAAG,MAAM,aAAa,CAAC,OAAO,EAAE,GAAG,CAAC,oBAAoB,CAAC,CAAC;AACxE,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACrB,MAAM,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC;AAChC;AACA,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC3C,IAAI,MAAM,IAAI,GAAG,KAAK,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC,GAAG,IAAI;AAC/D,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AACvD;AACA,IAAI,IAAI;AACR,MAAM,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AAC1D,QAAQ,IAAI,EAAE;AACd,UAAU,MAAM,EAAE,IAAI,CAAC,EAAE;AACzB,UAAU,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO;AACpC,UAAU,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ;AACtC,UAAU,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI;AAC9C,UAAU,WAAW,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;AACtD,UAAU,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM;AAClC,UAAU,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI;AAClD,UAAU,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI;AACxC,UAAU,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI;AACpC,UAAU,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO;AACpC,UAAU,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,KAAK;AACvD;AACA,OAAO,CAAC;AACR,MAAM,OAAO;AACb,QAAQ,IAAI;AACZ,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ;AACR,OAAO;AACP,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACzD,MAAM,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,8BAA8B,EAAE,CAAC;AACvE;AACA;AACA,CAAC;;;;;;;;AC1FM,MAAM,KAAK,GAAG,EAAE;AACvB,IAAI,eAAe;AACZ,MAAM,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAoD,CAAC,EAAE,OAAO;AAE9H,MAAM,SAAS,GAAG,8CAA8C;AAChE,MAAM,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC;AACvoI,MAAM,WAAW,GAAG,CAAC,4CAA4C,CAAC,0CAA0C,CAAC,gDAAgD,CAAC,uCAAuC,CAAC;AACtM,MAAM,KAAK,GAAG,EAAE;;;;;;;;;;;;;;;"}