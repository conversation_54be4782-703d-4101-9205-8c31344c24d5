import { p as push, R as spread_props, Q as bind_props, q as pop, Z as spread_attributes } from './index3-CqUPEnZw.js';
import { b as box, m as mergeProps } from './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import { u as useSelectGroup } from './index12-H6t3LX3-.js';
import { u as useId } from './use-id-CcFpwo20.js';

function Select_group$1($$payload, $$props) {
  push();
  let {
    id = useId(),
    ref = null,
    children,
    child,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const groupState = useSelectGroup({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, groupState.props);
  if (child) {
    $$payload.out += "<!--[-->";
    child($$payload, { props: mergedProps });
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;
    children?.($$payload);
    $$payload.out += `<!----></div>`;
  }
  $$payload.out += `<!--]-->`;
  bind_props($$props, { ref });
  pop();
}
function Select_group($$payload, $$props) {
  push();
  let { ref = null, $$slots, $$events, ...restProps } = $$props;
  $$payload.out += `<!---->`;
  Select_group$1($$payload, spread_props([{ "data-slot": "select-group" }, restProps]));
  $$payload.out += `<!---->`;
  bind_props($$props, { ref });
  pop();
}

export { Select_group as S };
//# sourceMappingURL=select-group-Cxqg41Dj.js.map
