import { w as writable } from './index2-Cut0V_vU.js';

const pricingModalStore = writable({
  isOpen: false,
  section: "pro",
  currentPlanId: null,
  onSelectPlan: null
});
function openPricingModal(options) {
  const section = options.section || "pro";
  const url = `/pricing?section=${section}`;
  if (typeof window !== "undefined") {
    window.location.href = url;
  }
  pricingModalStore.update((state) => ({
    ...state,
    isOpen: false,
    // Don't actually open the modal
    section,
    currentPlanId: options.currentPlanId || null,
    onSelectPlan: options.onSelectPlan || null
  }));
}
function closePricingModal() {
  pricingModalStore.update((state) => ({
    ...state,
    isOpen: false
  }));
}

export { closePricingModal as c, openPricingModal as o, pricingModalStore as p };
//# sourceMappingURL=pricing-D13CEnfk.js.map
