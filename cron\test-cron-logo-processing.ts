import { chromium } from "playwright";
import { uploadFile } from "./lib/storage/r2Storage";
import { PrismaClient } from "@prisma/client";
import { logger } from "./utils/logger";

/**
 * Test script to verify cron logo processing works with headless mode
 * This processes only a few companies to test the functionality
 */

// Global browser instance to reuse across searches
let globalBrowser: any = null;
let globalPage: any = null;

/**
 * Initialize browser once for all logo searches
 */
async function initializeBrowser() {
  if (!globalBrowser) {
    const { chromium } = await import("playwright");

    logger.info("🌐 Launching browser for logo searches...");
    globalBrowser = await chromium.launch({
      headless: true, // Headless mode tested and working (e.g., "Wyndy logo company")
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
    });

    const context = await globalBrowser.newContext({
      userAgent:
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    });

    globalPage = await context.newPage();
  }
}

/**
 * Close browser when done
 */
async function closeBrowser() {
  if (globalBrowser) {
    await globalBrowser.close();
    globalBrowser = null;
    globalPage = null;
  }
}

/**
 * Try to find logo using Bing Images search with company name only
 */
async function tryBingImagesSearchByName(
  companyName: string
): Promise<string | null> {
  try {
    logger.info(`   🔍 Searching Bing Images by name: ${companyName}`);

    // Ensure browser is initialized
    await initializeBrowser();

    // Search for company logo on Bing Images using company name + logo
    const searchQuery = encodeURIComponent(`"${companyName}" logo company`);
    const bingUrl = `https://www.bing.com/images/search?q=${searchQuery}`;

    logger.info(`   🌐 Bing Images: ${bingUrl}`);

    await globalPage.goto(bingUrl, { waitUntil: "networkidle" });

    // Wait for images to load
    await globalPage.waitForTimeout(3000);

    // Extract image URLs from Bing Images results
    const imageUrls = await globalPage.evaluate(() => {
      // Bing Images uses specific selectors for the actual image results
      const imageElements = Array.from(document.querySelectorAll(".iusc"));
      const urls: string[] = [];

      imageElements.forEach((element: any) => {
        try {
          const dataAttr = element.getAttribute("m");
          if (dataAttr) {
            const data = JSON.parse(dataAttr);
            if (data.murl) {
              urls.push(data.murl);
            }
          }
        } catch (e) {
          // Skip invalid elements
        }
      });

      return urls.slice(0, 3); // Get first 3 images
    });

    if (imageUrls.length === 0) {
      logger.info(`   ❌ No images found in Bing Images results`);
      return null;
    }

    logger.info(`   🎯 Found ${imageUrls.length} potential logo images`);

    // Try the image URLs
    for (let i = 0; i < imageUrls.length; i++) {
      const imageUrl = imageUrls[i];

      try {
        logger.info(`   📥 Trying image ${i + 1}: ${imageUrl}`);

        const imageResponse = await fetch(imageUrl, {
          headers: {
            "User-Agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            Referer: "https://www.google.com/",
          },
        });

        if (
          imageResponse.ok &&
          imageResponse.headers.get("content-type")?.startsWith("image/")
        ) {
          const buffer = Buffer.from(await imageResponse.arrayBuffer());
          const contentType =
            imageResponse.headers.get("content-type") || "image/png";

          // Generate a clean filename
          const cleanCompanyName = companyName
            .toLowerCase()
            .replace(/[^a-z0-9]/g, "-")
            .replace(/-+/g, "-")
            .replace(/^-|-$/g, "");

          const extension = contentType.includes("svg")
            ? "svg"
            : contentType.includes("jpeg")
              ? "jpg"
              : "png";
          const filename = `${cleanCompanyName}-cron-test-${Date.now()}.${extension}`;

          logger.info(`   📤 Uploading Bing logo to R2: ${filename}`);

          const uploadResult = await uploadFile(
            buffer,
            filename,
            contentType,
            "companyLogos"
          );

          if (uploadResult.success) {
            logger.info(
              `   ✅ Bing logo uploaded successfully: ${uploadResult.publicUrl}`
            );
            return uploadResult.publicUrl!;
          } else {
            logger.error(`   ❌ Upload failed: ${uploadResult.error}`);
          }
        }
      } catch (imageError) {
        logger.info(`   ⚠️ Failed to download image ${i + 1}: ${imageError}`);
        continue;
      }
    }

    logger.info(`   ❌ No valid logos found via Bing Images`);
    return null;
  } catch (error) {
    logger.error(
      `   ❌ Error searching Bing Images for ${companyName}:`,
      error
    );
    return null;
  }
}

/**
 * Test company logo processing for cron
 */
async function testCronLogoProcessing() {
  logger.info("🎨 Testing cron company logo processing with headless mode");

  const prisma = new PrismaClient();

  try {
    // Get a few companies without logos for testing
    const companies = await prisma.company.findMany({
      where: {
        AND: [
          { activeJobCount: { gt: 0 } }, // Has active jobs
          { logoUrl: null }, // No logo yet
        ],
      },
      select: {
        id: true,
        name: true,
        activeJobCount: true,
      },
      orderBy: {
        activeJobCount: "desc", // Process companies with most jobs first
      },
      take: 3, // Test with just 3 companies
    });

    logger.info(`📊 Found ${companies.length} companies without logos for testing`);

    if (companies.length === 0) {
      logger.info("✅ No companies need logos for testing");
      return;
    }

    let logosAdded = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const company of companies) {
      try {
        logger.info(
          `🏢 Processing: ${company.name} (${company.activeJobCount} jobs)`
        );

        // Use Bing Images search by name
        const r2LogoUrl = await tryBingImagesSearchByName(company.name);

        if (r2LogoUrl) {
          // Update company with R2 logo URL
          await prisma.company.update({
            where: { id: company.id },
            data: {
              logoUrl: r2LogoUrl,
            },
          });

          logger.info(`   ✅ Added R2 logo: ${r2LogoUrl}`);
          logosAdded++;
        } else {
          logger.info(`   ❌ No logo found for ${company.name}`);
          skippedCount++;
        }

        // Small delay between companies
        await new Promise((resolve) => setTimeout(resolve, 1000));
      } catch (error) {
        logger.error(`❌ Error processing ${company.name}:`, error);
        errorCount++;
      }
    }

    // Summary
    logger.info("\n🎉 Cron logo processing test completed!");
    logger.info(`   ✅ Logos uploaded to R2: ${logosAdded}`);
    logger.info(`   ⏭️ Skipped: ${skippedCount}`);
    logger.info(`   ❌ Errors: ${errorCount}`);
    logger.info(`   📊 Total processed: ${companies.length}`);

    if (logosAdded > 0) {
      logger.info(
        `\n📈 Success rate: ${((logosAdded / companies.length) * 100).toFixed(1)}%`
      );
    }
  } catch (error) {
    logger.error("❌ Error during cron logo processing test:", error);
    throw error;
  } finally {
    // Close browser and disconnect from database
    await closeBrowser();
    await prisma.$disconnect();
  }
}

// Run the test
testCronLogoProcessing()
  .then(() => {
    logger.info("✅ Cron logo processing test completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("❌ Cron logo processing test failed:", error);
    process.exit(1);
  });
