{"version": 3, "file": "roles-OPAy2hMb.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/roles.js"], "sourcesContent": ["const Roles = {\n  free: {\n    name: \"Free\",\n    limits: {\n      jobSubmissionsPerMonth: 10,\n      profiles: 1\n    },\n    features: {\n      autoApply: true,\n      analytics: false,\n      priorityQueue: false\n    }\n  },\n  casual: {\n    name: \"Pro - Casual\",\n    limits: {\n      jobSubmissionsPerMonth: 50,\n      profiles: 5\n    },\n    features: {\n      autoApply: true,\n      analytics: false,\n      priorityQueue: false\n    }\n  },\n  active: {\n    name: \"Pro - Active\",\n    limits: {\n      jobSubmissionsPerMonth: 150,\n      profiles: 10\n    },\n    features: {\n      autoApply: true,\n      analytics: true,\n      priorityQueue: false\n    }\n  },\n  daily: {\n    name: \"Pro - Daily\",\n    limits: {\n      jobSubmissionsPerMonth: 500,\n      profiles: 15\n    },\n    features: {\n      autoApply: true,\n      analytics: true,\n      priorityQueue: true\n    }\n  },\n  power: {\n    name: \"Pro - Power\",\n    limits: {\n      jobSubmissionsPerMonth: null,\n      profiles: 25\n    },\n    features: {\n      autoApply: true,\n      analytics: true,\n      priorityQueue: true\n    }\n  },\n  startup: {\n    name: \"Team - Startup\",\n    limits: {\n      jobSubmissionsPerMonth: 500,\n      profiles: 100\n    },\n    features: {\n      autoApply: true,\n      analytics: true,\n      priorityQueue: true\n    },\n    team: true\n  },\n  medium: {\n    name: \"Team - Medium\",\n    limits: {\n      jobSubmissionsPerMonth: 1500,\n      profiles: 250\n    },\n    features: {\n      autoApply: true,\n      analytics: true,\n      priorityQueue: true\n    },\n    team: true\n  },\n  enterprise: {\n    name: \"Team - Enterprise\",\n    limits: {\n      jobSubmissionsPerMonth: null,\n      profiles: 500\n    },\n    features: {\n      autoApply: true,\n      analytics: true,\n      priorityQueue: true\n    },\n    team: true\n  },\n  admin: {\n    name: \"Admin\",\n    limits: {\n      jobSubmissionsPerMonth: null,\n      profiles: null\n    },\n    features: {\n      autoApply: true,\n      analytics: true,\n      priorityQueue: true\n    }\n  }\n};\nexport {\n  Roles\n};\n"], "names": [], "mappings": "AAAK,MAAC,KAAK,GAAG;AACd,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE;AACZ,MAAM,sBAAsB,EAAE,EAAE;AAChC,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,SAAS,EAAE,KAAK;AACtB,MAAM,aAAa,EAAE;AACrB;AACA,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,cAAc;AACxB,IAAI,MAAM,EAAE;AACZ,MAAM,sBAAsB,EAAE,EAAE;AAChC,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,SAAS,EAAE,KAAK;AACtB,MAAM,aAAa,EAAE;AACrB;AACA,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,cAAc;AACxB,IAAI,MAAM,EAAE;AACZ,MAAM,sBAAsB,EAAE,GAAG;AACjC,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,aAAa,EAAE;AACrB;AACA,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,MAAM,EAAE;AACZ,MAAM,sBAAsB,EAAE,GAAG;AACjC,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,aAAa,EAAE;AACrB;AACA,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,MAAM,EAAE;AACZ,MAAM,sBAAsB,EAAE,IAAI;AAClC,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,aAAa,EAAE;AACrB;AACA,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,MAAM,EAAE;AACZ,MAAM,sBAAsB,EAAE,GAAG;AACjC,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,aAAa,EAAE;AACrB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,eAAe;AACzB,IAAI,MAAM,EAAE;AACZ,MAAM,sBAAsB,EAAE,IAAI;AAClC,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,aAAa,EAAE;AACrB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,mBAAmB;AAC7B,IAAI,MAAM,EAAE;AACZ,MAAM,sBAAsB,EAAE,IAAI;AAClC,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,aAAa,EAAE;AACrB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,MAAM,EAAE;AACZ,MAAM,sBAAsB,EAAE,IAAI;AAClC,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,aAAa,EAAE;AACrB;AACA;AACA;;;;"}